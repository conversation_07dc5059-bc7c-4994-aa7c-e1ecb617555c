import { useRef } from 'react';
import { PanResponder, Animated } from 'react-native';

interface UseSwipeNavigationProps {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  enabled?: boolean;
  threshold?: number;
  minVelocity?: number;
}

export const useSwipeNavigation = ({
  onSwipeLeft,
  onSwipeRight,
  onSwipeUp,
  onSwipeDown,
  enabled = true,
  threshold = 50,
  minVelocity = 0.3,
}: UseSwipeNavigationProps) => {
  const pan = useRef(new Animated.ValueXY()).current;
  
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => enabled,
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        if (!enabled) return false;
        
        const { dx, dy } = gestureState;
        // Only capture the gesture if the movement is significant
        return Math.abs(dx) > 5 || Math.abs(dy) > 5;
      },
      
      onPanResponderGrant: () => {
        // Set the initial value to the current state of the drag
        pan.setOffset({
          x: (pan.x as any)._value,
          y: (pan.y as any)._value,
        });
      },
      
      onPanResponderMove: Animated.event(
        [null, { dx: pan.x, dy: pan.y }],
        { useNativeDriver: false }
      ),
      
      onPanResponderRelease: (evt, gestureState) => {
        const { dx, dy, vx, vy } = gestureState;
        
        // Flatten the offset to avoid any issues
        pan.flattenOffset();
        
        // Determine the direction of the swipe
        const absX = Math.abs(dx);
        const absY = Math.abs(dy);
        
        // Check if the swipe meets the threshold and velocity requirements
        if (absX > absY && absX > threshold && Math.abs(vx) > minVelocity) {
          // Horizontal swipe
          if (dx > 0 && onSwipeRight) {
            onSwipeRight();
          } else if (dx < 0 && onSwipeLeft) {
            onSwipeLeft();
          }
        } else if (absY > absX && absY > threshold && Math.abs(vy) > minVelocity) {
          // Vertical swipe
          if (dy > 0 && onSwipeDown) {
            onSwipeDown();
          } else if (dy < 0 && onSwipeUp) {
            onSwipeUp();
          }
        }
        
        // Reset position
        Animated.spring(pan, {
          toValue: { x: 0, y: 0 },
          useNativeDriver: false,
          tension: 50,
          friction: 10,
        }).start();
      },
    })
  ).current;
  
  return {
    panHandlers: panResponder.panHandlers,
    pan,
  };
};
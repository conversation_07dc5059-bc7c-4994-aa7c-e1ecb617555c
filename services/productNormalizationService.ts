import { logger } from '../utils/logger';

/**
 * Servicio de normalización de productos para mejorar el matching
 * entre productos generados por IA y productos del inventario
 */

interface NormalizedProduct {
  brand: string;
  line: string;
  type: string;
  shade: string;
  original: string;
}

export class ProductNormalizationService {
  // Mapeo de variaciones comunes de marcas
  private static brandVariations: Record<string, string[]> = {
    'loreal': ['l\'oreal', 'l\'oréal', 'loréal', 'l oreal', 'loreal professionnel'],
    'wella': ['wella professionals', 'wellaton'],
    'schwarzkopf': ['schwarzkopf professional', 'schwarz kopf'],
    'salerm': ['salerm cosmetics', 'salerm vision'],
    'revlon': ['revlon professional', 'revlonissimo'],
    'alfaparf': ['alfaparf milano', 'alfa parf'],
    'matrix': ['matrix professional'],
    'redken': ['redken professional'],
    'goldwell': ['goldwell topchic', 'goldwell colorance'],
    'pravana': ['pravana chromasilk'],
  };

  // Tipos de productos y sus variaciones
  private static typeVariations: Record<string, string[]> = {
    'tinte': ['color', 'coloración', 'tinte permanente', 'color permanente', 'dye', 'hair color'],
    'oxidante': ['oxidant', 'peróxido', 'developer', 'revelador', 'activador', 'oxigenada'],
    'decolorante': ['bleach', 'decoloración', 'polvo decolorante', 'lightener', 'aclarante'],
    'matizador': ['toner', 'tonalizador', 'matiz', 'neutralizador'],
    'tratamiento': ['treatment', 'mascarilla', 'mask', 'acondicionador', 'reparador'],
    'aditivo': ['additive', 'booster', 'protector', 'amplificador'],
    'pre-pigmentacion': ['pre-pigmentation', 'prepigmentación', 'filler', 'relleno'],
  };

  // Patrones de volúmenes de oxidante
  private static oxidantVolumePatterns = [
    { pattern: /(\d+)\s*vol/i, normalize: (match: string) => `${match} vol` },
    { pattern: /(\d+)\s*%/i, normalize: (match: string) => `${match}%` },
    { pattern: /(\d+)\s*volumenes/i, normalize: (match: string) => `${match} vol` },
  ];

  // Patrones de tonos/números
  private static shadePatterns = [
    // Formato X.XX o X/XX o X-XX
    { pattern: /(\d+)[\.\/-](\d+)/i, normalize: (n1: string, n2: string) => `${n1}.${n2}` },
    // Formato simple X
    { pattern: /^(\d+)$/i, normalize: (n: string) => n },
    // Con letras (ej: 7N, 8A)
    { pattern: /(\d+)([A-Z]+)/i, normalize: (n: string, l: string) => `${n}${l}` },
  ];

  /**
   * Normaliza un nombre de producto completo extrayendo sus componentes
   */
  static parseProduct(productName: string): NormalizedProduct {
    const original = productName;
    let workingName = productName.toLowerCase().trim();
    
    logger.debug('Normalizando producto:', 'ProductNormalization', original);

    // Extraer marca
    const brand = this.extractBrand(workingName);
    if (brand) {
      // Remover la marca del nombre para facilitar parsing
      const brandPattern = new RegExp(brand + '\\s*', 'gi');
      workingName = workingName.replace(brandPattern, '');
    }

    // Extraer línea (si existe)
    const line = this.extractLine(workingName, brand);
    if (line) {
      const linePattern = new RegExp(line + '\\s*', 'gi');
      workingName = workingName.replace(linePattern, '');
    }

    // Extraer tipo
    const type = this.extractType(workingName);
    if (type) {
      // Remover variaciones del tipo
      const typeVariations = this.typeVariations[type] || [type];
      typeVariations.forEach(variation => {
        const typePattern = new RegExp(variation + '\\s*', 'gi');
        workingName = workingName.replace(typePattern, '');
      });
    }

    // Extraer tono/shade
    const shade = this.extractShade(workingName);

    const result = {
      brand: brand || '',
      line: line || '',
      type: type || '',
      shade: shade || '',
      original
    };

    logger.debug('Producto normalizado:', 'ProductNormalization', result);
    return result;
  }

  /**
   * Extrae la marca del nombre del producto
   */
  private static extractBrand(productName: string): string {
    for (const [normalizedBrand, variations] of Object.entries(this.brandVariations)) {
      for (const variation of variations) {
        if (productName.includes(variation.toLowerCase())) {
          return this.capitalizeWords(normalizedBrand);
        }
      }
    }

    // Si no encuentra variación conocida, buscar palabras que parezcan marcas
    const words = productName.split(/\s+/);
    const possibleBrand = words[0];
    
    // Si la primera palabra tiene más de 3 letras y no es un tipo conocido
    if (possibleBrand && possibleBrand.length > 3 && !this.isKnownType(possibleBrand)) {
      return this.capitalizeWords(possibleBrand);
    }

    return '';
  }

  /**
   * Extrae la línea del producto
   */
  private static extractLine(productName: string, brand: string): string {
    // Líneas conocidas por marca
    const knownLines: Record<string, string[]> = {
      'wella': ['koleston', 'illumina', 'color touch', 'blondor'],
      'loreal': ['majirel', 'inoa', 'dia light', 'majirouge'],
      'salerm': ['vision', 'biokera'],
      'schwarzkopf': ['igora', 'blondme', 'essensity'],
    };

    const brandKey = brand.toLowerCase().replace(/[^a-z]/g, '');
    const lines = knownLines[brandKey] || [];

    for (const line of lines) {
      if (productName.includes(line.toLowerCase())) {
        return this.capitalizeWords(line);
      }
    }

    return '';
  }

  /**
   * Extrae el tipo de producto
   */
  private static extractType(productName: string): string {
    for (const [normalizedType, variations] of Object.entries(this.typeVariations)) {
      for (const variation of variations) {
        if (productName.includes(variation.toLowerCase())) {
          return normalizedType;
        }
      }
    }

    // Si tiene volumen, probablemente es oxidante
    if (/\d+\s*(vol|%|volumenes)/i.test(productName)) {
      return 'oxidante';
    }

    // Por defecto asumir tinte si no se puede determinar
    return 'tinte';
  }

  /**
   * Extrae el tono/número del producto
   */
  private static extractShade(productName: string): string {
    // Para oxidantes, buscar volumen
    if (/oxidante|oxidant|developer|peróxido/i.test(productName)) {
      for (const { pattern, normalize } of this.oxidantVolumePatterns) {
        const match = productName.match(pattern);
        if (match) {
          return normalize(match[1]);
        }
      }
    }

    // Para tintes y otros, buscar patrones de tonos
    for (const { pattern, normalize } of this.shadePatterns) {
      const match = productName.match(pattern);
      if (match) {
        if (match[2]) {
          return normalize(match[1], match[2]);
        }
        return normalize(match[1]);
      }
    }

    return '';
  }

  /**
   * Verifica si una palabra es un tipo conocido
   */
  private static isKnownType(word: string): boolean {
    const lowerWord = word.toLowerCase();
    for (const variations of Object.values(this.typeVariations)) {
      if (variations.some(v => v.toLowerCase() === lowerWord)) {
        return true;
      }
    }
    return false;
  }

  /**
   * Capitaliza las palabras correctamente
   */
  private static capitalizeWords(str: string): string {
    return str.split(/\s+/)
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }

  /**
   * Normaliza variaciones de tonos (7.31 = 7/31 = 7-31)
   */
  static normalizeShade(shade: string): string {
    if (!shade) return '';
    
    // Normalizar separadores a punto
    return shade.replace(/[\/-]/g, '.');
  }

  /**
   * Compara si dos tonos son equivalentes
   */
  static areShadesEquivalent(shade1: string, shade2: string): boolean {
    if (!shade1 || !shade2) return false;
    
    const normalized1 = this.normalizeShade(shade1);
    const normalized2 = this.normalizeShade(shade2);
    
    return normalized1 === normalized2;
  }

  /**
   * Calcula la similitud entre dos productos normalizados
   */
  static calculateSimilarity(product1: NormalizedProduct, product2: NormalizedProduct): number {
    let score = 0;
    let maxScore = 0;

    // Marca (40 puntos)
    maxScore += 40;
    if (product1.brand && product2.brand) {
      if (product1.brand.toLowerCase() === product2.brand.toLowerCase()) {
        score += 40;
      }
    }

    // Tipo (30 puntos)
    maxScore += 30;
    if (product1.type && product2.type) {
      if (product1.type === product2.type) {
        score += 30;
      }
    }

    // Tono (20 puntos)
    maxScore += 20;
    if (product1.shade && product2.shade) {
      if (this.areShadesEquivalent(product1.shade, product2.shade)) {
        score += 20;
      }
    }

    // Línea (10 puntos)
    maxScore += 10;
    if (product1.line && product2.line) {
      if (product1.line.toLowerCase() === product2.line.toLowerCase()) {
        score += 10;
      }
    }

    return Math.round((score / maxScore) * 100);
  }
}
import { supabase } from './supabase';

export type AITask = 
  | 'diagnose_image'
  | 'analyze_desired_look'
  | 'generate_formula'
  | 'convert_formula'
  | 'parse_product_text';

interface AIRequest {
  task: AITask;
  payload: Record<string, any>;
}

interface AIResponse {
  success: boolean;
  data?: any;
  error?: string;
  cached?: boolean;
}

/**
 * Call the Salonier AI Assistant Edge Function
 * @param task The AI task to perform
 * @param payload The data for the task
 * @returns The AI response
 */
export async function callAIAssistant(task: AITask, payload: Record<string, any>): Promise<AIResponse> {
  try {
    const { data, error } = await supabase.functions.invoke('salonier-assistant', {
      body: { task, payload }
    });

    if (error) {
      console.error('Edge function error:', error);
      return {
        success: false,
        error: error.message || 'Error calling AI assistant'
      };
    }

    return data as AIResponse;
  } catch (error: any) {
    console.error('Error calling edge function:', error);
    return {
      success: false,
      error: error.message || 'Network error'
    };
  }
}

/**
 * Diagnose hair from an image
 * @param imageUrl URL of the image to analyze
 */
export async function diagnoseHairImage(imageUrl: string) {
  return callAIAssistant('diagnose_image', { imageUrl });
}

/**
 * Analyze a desired hair look
 * @param imageUrl URL of the desired look image
 * @param currentLevel Current hair level
 */
export async function analyzeDesiredLook(imageUrl: string, currentLevel: number) {
  return callAIAssistant('analyze_desired_look', { imageUrl, currentLevel });
}

/**
 * Generate a hair color formula
 * @param diagnosis Current hair diagnosis
 * @param desiredResult Desired hair result
 * @param brand Product brand
 * @param line Product line
 * @param clientHistory Optional client history
 * @param regionalConfig Optional regional configuration for localized formulas
 */
export async function generateFormula(
  diagnosis: any,
  desiredResult: any,
  brand: string,
  line: string,
  clientHistory?: string,
  regionalConfig?: any
) {
  return callAIAssistant('generate_formula', {
    diagnosis,
    desiredResult,
    brand,
    line,
    clientHistory,
    regionalConfig
  });
}

/**
 * Convert a formula between brands
 * @param originalBrand Original brand
 * @param originalLine Original line
 * @param originalFormula Original formula
 * @param targetBrand Target brand
 * @param targetLine Target line
 */
export async function convertFormula(
  originalBrand: string,
  originalLine: string,
  originalFormula: string,
  targetBrand: string,
  targetLine: string
) {
  return callAIAssistant('convert_formula', {
    originalBrand,
    originalLine,
    originalFormula,
    targetBrand,
    targetLine
  });
}

/**
 * Parse product information from text
 * @param text Text containing product information
 */
export async function parseProductText(text: string) {
  return callAIAssistant('parse_product_text', { text });
}
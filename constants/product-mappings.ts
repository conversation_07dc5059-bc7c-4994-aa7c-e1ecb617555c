/**
 * Centralized mapping between Spanish UI categories and English database types
 * This ensures consistency across all product creation flows
 */

export const CATEGORY_TO_TYPE_MAPPING: Record<string, string> = {
  // Spanish categories (UI) → English types (database)
  'tinte': 'color',
  'oxidante': 'developer',
  'decolorante': 'bleach', 
  'tratamiento': 'treatment',
  'matizador': 'toner',
  'aditivo': 'additive',
  'pre-pigmentacion': 'pre_pigment',
  'protector': 'protector',
  'mascarilla': 'mask',
  'aceite': 'oil',
  'serum': 'serum',
  'champu': 'shampoo',
  'acondicionador': 'conditioner',
  'neutralizante': 'neutralizer',
  'gel': 'gel',
  'laca': 'hairspray',
  'espuma': 'mousse',
  'protector-termico': 'heat_protectant',
  'otro': 'other',
  
  // Additional Spanish variants that might come from AI
  'colorante': 'color',
  'tinte/color': 'color',
  'oxigenada': 'developer',
  'agua oxigenada': 'developer',
  'oxidante/revelador': 'developer',
  'revelador': 'developer',
  'decolorante/blanqueador': 'bleach',
  'blanqueador': 'bleach',
  'polvo decolorante': 'bleach',
  'matizador/toner': 'toner',
  'tratamiento reconstructor': 'treatment',
  'protector/aditivo': 'protector',
  'mascarilla capilar': 'mask',
  'aceite capilar': 'oil',
  'serum/ampolla': 'serum',
  'ampolla': 'serum',
  'champú técnico': 'shampoo',
  'champú': 'shampoo',
  'gel/cera/pomada': 'gel',
  'cera': 'gel',
  'pomada': 'gel',
  'laca/spray fijador': 'hairspray',
  'spray fijador': 'hairspray',
  'espuma/mousse': 'mousse',
  'mousse': 'mousse',
  'protector térmico': 'heat_protectant',
  
  // English types (for backwards compatibility)
  'color': 'color',
  'hair color': 'color',
  'developer': 'developer',
  'bleach': 'bleach',
  'lightener': 'bleach',
  'treatment': 'treatment',
  'toner': 'toner',
  'shampoo': 'shampoo',
  'conditioner': 'conditioner',
  'neutralizer': 'neutralizer',
  'styling': 'gel',
  'gel': 'gel',
  'wax': 'gel',
  'hairspray': 'hairspray',
  'mousse': 'mousse',
  'foam': 'mousse',
  'additive': 'additive',
  'protector': 'protector',
  'mask': 'mask',
  'hair mask': 'mask',
  'oil': 'oil',
  'hair oil': 'oil',
  'serum': 'serum',
  'heat protectant': 'heat_protectant',
  'pre_pigment': 'pre_pigment',
  'other': 'other'
};

/**
 * Convert Spanish category or any type to valid English database type
 */
export function mapCategoryToType(categoryOrType: string): string {
  const normalized = categoryOrType?.toLowerCase().trim() || '';
  return CATEGORY_TO_TYPE_MAPPING[normalized] || 'other';
}

/**
 * Validate that a type value is valid for the database
 */
export function isValidDatabaseType(type: string): boolean {
  const validTypes = [
    'color', 'developer', 'bleach', 'treatment', 'toner',
    'shampoo', 'conditioner', 'neutralizer', 'gel', 'hairspray',
    'mousse', 'additive', 'protector', 'mask', 'oil',
    'serum', 'heat_protectant', 'pre_pigment', 'other'
  ];
  return validTypes.includes(type?.toLowerCase());
}
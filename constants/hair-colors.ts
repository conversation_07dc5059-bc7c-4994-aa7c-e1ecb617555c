// Hair color palette - Natural and elegant colors for UI display
export const hairColorMap: Record<string, string> = {
  // Tonos principales - Matching dropdown options exactly
  'Negro': '#1a1a1a',
  'Castaño oscuro': '#3d2314',
  'Castaño Oscuro': '#3d2314',
  'Castaño medio': '#5c3825',
  'Castaño Medio': '#5c3825',
  'Castaño claro': '#8b6239',
  'Castaño Claro': '#8b6239',
  'Rubio oscuro': '#b08d57',
  'Rubio Oscuro': '#b08d57',
  'Rubio medio': '#d4a574',
  'Rubio Medio': '#d4a574',
  'Rubio claro': '#e6c697',
  'Rubio Claro': '#e6c697',
  'Rubio muy claro': '#f4e0c1',
  '<PERSON>ubi<PERSON>': '#f4e0c1',
  'Rubio platino': '#faf0dc',
  'Rubio <PERSON>': '#faf0dc',
  'Rojo': '#8b3a26',
  'Cao<PERSON>': '#6f2c1f',
  'Rojo Cobrizo': '#b85742',
  
  // Subtonos/Reflejos
  'Cenizo': '#8a8680',
  'Natural': '#a0826d',
  'Dorado': '#d4a574',
  'Cobrizo': '#b85742',
  'Rojizo': '#a0522d',
  'Violeta': '#8b7898',
  'Beige': '#e6d7c3',
  'Irisado': '#e8e3f5',
  'Neutro': '#a0826d',
  
  // Matices no deseados
  'Naranja': '#ff8c42',
  'Amarillo': '#ffd966',
  'Verde': '#7cb342',
  
  // Estados especiales
  'Frío': '#6c9bd2',
  'Neutro': '#a0a0a0',
  'Cálido': '#d4a574',
};

// Temperature indicators
export const temperatureColors = {
  cold: '#6c9bd2',
  neutral: '#a0a0a0',
  warm: '#d4a574',
};

// Get temperature from reflect
export const getTemperatureFromReflect = (reflect: string): 'cold' | 'neutral' | 'warm' => {
  const coldTones = ['Cenizo', 'Violeta', 'Irisado', 'Frío'];
  const warmTones = ['Dorado', 'Cobrizo', 'Rojizo', 'Caoba', 'Cálido'];
  
  if (coldTones.includes(reflect)) return 'cold';
  if (warmTones.includes(reflect)) return 'warm';
  return 'neutral';
};

// Legacy alias for backward compatibility
export const getTemperatureFromUndertone = getTemperatureFromReflect;

// Helper to get indicator type for a field
export const getIndicatorType = (label: string): 'thickness' | 'density' | 'color' | 'state' | 'none' => {
  const lowerLabel = label.toLowerCase();
  
  if (lowerLabel.includes('grosor')) return 'thickness';
  if (lowerLabel.includes('densidad')) return 'density';
  if (lowerLabel.includes('tono') || lowerLabel.includes('color') || lowerLabel.includes('matiz')) return 'color';
  if (lowerLabel.includes('estado')) return 'state';
  
  return 'none';
};
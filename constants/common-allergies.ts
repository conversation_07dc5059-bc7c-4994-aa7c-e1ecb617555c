export interface Allergy {
  id: string;
  name: string;
  category: 'chemical' | 'metal' | 'preservative' | 'fragrance' | 'other';
  severity: 'high' | 'medium' | 'low';
  relatedTo?: string[]; // Related products/procedures
}

export const COMMON_ALLERGIES: Allergy[] = [
  // Químicos de coloración - Alta severidad
  {
    id: 'ppd',
    name: 'PPD (Parafenilendiamina)',
    category: 'chemical',
    severity: 'high',
    relatedTo: ['tintes permanentes', 'tintes oscuros']
  },
  {
    id: 'ptd',
    name: 'PTD (Paratoluendiamina)',
    category: 'chemical',
    severity: 'high',
    relatedTo: ['tintes permanentes']
  },
  {
    id: 'ammonia',
    name: 'Amonía<PERSON>',
    category: 'chemical',
    severity: 'medium',
    relatedTo: ['tintes permanentes', 'decolorantes']
  },
  {
    id: 'resorcinol',
    name: 'Resorcinol',
    category: 'chemical',
    severity: 'medium',
    relatedTo: ['tintes permanentes']
  },
  {
    id: 'persulfates',
    name: 'Persulfatos',
    category: 'chemical',
    severity: 'high',
    relatedTo: ['decolorantes', 'polvos decolorantes']
  },
  {
    id: 'hydrogen_peroxide',
    name: 'Peróxido de hidrógeno',
    category: 'chemical',
    severity: 'low',
    relatedTo: ['oxidantes', 'decolorantes']
  },
  
  // Metales
  {
    id: 'nickel',
    name: 'Níquel',
    category: 'metal',
    severity: 'medium',
    relatedTo: ['herramientas metálicas', 'pinzas']
  },
  {
    id: 'cobalt',
    name: 'Cobalto',
    category: 'metal',
    severity: 'medium',
    relatedTo: ['tintes azules', 'herramientas']
  },
  {
    id: 'chromium',
    name: 'Cromo',
    category: 'metal',
    severity: 'medium',
    relatedTo: ['tintes verdes']
  },
  
  // Conservantes
  {
    id: 'parabens',
    name: 'Parabenos',
    category: 'preservative',
    severity: 'low',
    relatedTo: ['champús', 'acondicionadores', 'tratamientos']
  },
  {
    id: 'formaldehyde',
    name: 'Formaldehído',
    category: 'preservative',
    severity: 'high',
    relatedTo: ['alisados', 'keratinas']
  },
  {
    id: 'methylisothiazolinone',
    name: 'Metilisotiazolinona (MI)',
    category: 'preservative',
    severity: 'medium',
    relatedTo: ['champús', 'productos de lavado']
  },
  {
    id: 'phenoxyethanol',
    name: 'Fenoxietanol',
    category: 'preservative',
    severity: 'low',
    relatedTo: ['productos capilares']
  },
  
  // Fragancias
  {
    id: 'fragrance_mix',
    name: 'Mezcla de fragancias',
    category: 'fragrance',
    severity: 'medium',
    relatedTo: ['productos perfumados']
  },
  {
    id: 'essential_oils',
    name: 'Aceites esenciales',
    category: 'fragrance',
    severity: 'low',
    relatedTo: ['productos naturales', 'aromaterapia']
  },
  {
    id: 'lavender',
    name: 'Lavanda',
    category: 'fragrance',
    severity: 'low',
    relatedTo: ['productos relajantes']
  },
  {
    id: 'tea_tree',
    name: 'Árbol de té',
    category: 'fragrance',
    severity: 'low',
    relatedTo: ['tratamientos anticaspa']
  },
  
  // Otros
  {
    id: 'latex',
    name: 'Látex',
    category: 'other',
    severity: 'high',
    relatedTo: ['guantes']
  },
  {
    id: 'lanolin',
    name: 'Lanolina',
    category: 'other',
    severity: 'low',
    relatedTo: ['acondicionadores', 'tratamientos']
  },
  {
    id: 'propylene_glycol',
    name: 'Propilenglicol',
    category: 'other',
    severity: 'low',
    relatedTo: ['productos capilares']
  },
  {
    id: 'sulfates',
    name: 'Sulfatos (SLS/SLES)',
    category: 'other',
    severity: 'low',
    relatedTo: ['champús']
  },
  {
    id: 'gluten',
    name: 'Gluten/Proteína de trigo',
    category: 'other',
    severity: 'low',
    relatedTo: ['productos fortalecedores']
  }
];

// Función helper para buscar alergias
export function searchAllergies(query: string): Allergy[] {
  const normalizedQuery = query.toLowerCase().trim();
  
  if (!normalizedQuery) return [];
  
  return COMMON_ALLERGIES.filter(allergy => {
    // Buscar en el nombre
    if (allergy.name.toLowerCase().includes(normalizedQuery)) return true;
    
    // Buscar en el ID (para abreviaciones como PPD)
    if (allergy.id.toLowerCase().includes(normalizedQuery)) return true;
    
    // Buscar en productos relacionados
    if (allergy.relatedTo?.some(related => 
      related.toLowerCase().includes(normalizedQuery)
    )) return true;
    
    return false;
  });
}

// Función para obtener alergias por categoría
export function getAllergiesByCategory(category: Allergy['category']): Allergy[] {
  return COMMON_ALLERGIES.filter(allergy => allergy.category === category);
}

// Función para obtener alergias de alta severidad
export function getHighSeverityAllergies(): Allergy[] {
  return COMMON_ALLERGIES.filter(allergy => allergy.severity === 'high');
}
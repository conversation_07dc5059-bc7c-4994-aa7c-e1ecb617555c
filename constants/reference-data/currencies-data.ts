export interface CurrencyInfo {
  code: string;
  symbol: string;
  name: string;
  countries: string[];
  popular?: boolean;
}

export const currenciesData: CurrencyInfo[] = [
  // Most Popular Currencies
  {
    code: 'USD',
    symbol: '$',
    name: 'US Dollar',
    countries: ['US', 'EC', 'SV', 'PA', 'DO', 'PR'],
    popular: true,
  },
  {
    code: 'EUR',
    symbol: '€',
    name: 'Euro',
    countries: ['ES', 'FR', 'DE', 'IT', 'PT', 'NL', 'BE', 'AT', 'IE', 'FI', 'GR'],
    popular: true,
  },
  {
    code: 'GBP',
    symbol: '£',
    name: 'British Pound',
    countries: ['GB'],
    popular: true,
  },
  {
    code: 'MXN',
    symbol: '$',
    name: 'Mexican Peso',
    countries: ['MX'],
    popular: true,
  },
  {
    code: 'BRL',
    symbol: 'R$',
    name: 'Brazilian Real',
    countries: ['BR'],
    popular: true,
  },
  
  // Latin American Currencies
  {
    code: 'ARS',
    symbol: '$',
    name: 'Argentine Peso',
    countries: ['AR'],
    popular: false,
  },
  {
    code: 'CLP',
    symbol: '$',
    name: 'Chilean Peso',
    countries: ['CL'],
    popular: false,
  },
  {
    code: 'COP',
    symbol: '$',
    name: 'Colombian Peso',
    countries: ['CO'],
    popular: false,
  },
  {
    code: 'PEN',
    symbol: 'S/',
    name: 'Peruvian Sol',
    countries: ['PE'],
    popular: false,
  },
  {
    code: 'UYU',
    symbol: '$',
    name: 'Uruguayan Peso',
    countries: ['UY'],
    popular: false,
  },
  {
    code: 'PYG',
    symbol: '₲',
    name: 'Paraguayan Guaraní',
    countries: ['PY'],
    popular: false,
  },
  {
    code: 'BOB',
    symbol: 'Bs',
    name: 'Bolivian Boliviano',
    countries: ['BO'],
    popular: false,
  },
  {
    code: 'VES',
    symbol: 'Bs',
    name: 'Venezuelan Bolívar',
    countries: ['VE'],
    popular: false,
  },
  
  // Central American & Caribbean
  {
    code: 'GTQ',
    symbol: 'Q',
    name: 'Guatemalan Quetzal',
    countries: ['GT'],
    popular: false,
  },
  {
    code: 'HNL',
    symbol: 'L',
    name: 'Honduran Lempira',
    countries: ['HN'],
    popular: false,
  },
  {
    code: 'NIO',
    symbol: 'C$',
    name: 'Nicaraguan Córdoba',
    countries: ['NI'],
    popular: false,
  },
  {
    code: 'CRC',
    symbol: '₡',
    name: 'Costa Rican Colón',
    countries: ['CR'],
    popular: false,
  },
  {
    code: 'DOP',
    symbol: 'RD$',
    name: 'Dominican Peso',
    countries: ['DO'],
    popular: false,
  },
  
  // Other European Currencies
  {
    code: 'CHF',
    symbol: 'CHF',
    name: 'Swiss Franc',
    countries: ['CH'],
    popular: false,
  },
  {
    code: 'SEK',
    symbol: 'kr',
    name: 'Swedish Krona',
    countries: ['SE'],
    popular: false,
  },
  {
    code: 'NOK',
    symbol: 'kr',
    name: 'Norwegian Krone',
    countries: ['NO'],
    popular: false,
  },
  {
    code: 'DKK',
    symbol: 'kr',
    name: 'Danish Krone',
    countries: ['DK'],
    popular: false,
  },
  {
    code: 'PLN',
    symbol: 'zł',
    name: 'Polish Złoty',
    countries: ['PL'],
    popular: false,
  },
  {
    code: 'CZK',
    symbol: 'Kč',
    name: 'Czech Koruna',
    countries: ['CZ'],
    popular: false,
  },
  {
    code: 'HUF',
    symbol: 'Ft',
    name: 'Hungarian Forint',
    countries: ['HU'],
    popular: false,
  },
  {
    code: 'RON',
    symbol: 'lei',
    name: 'Romanian Leu',
    countries: ['RO'],
    popular: false,
  },
  
  // North American
  {
    code: 'CAD',
    symbol: '$',
    name: 'Canadian Dollar',
    countries: ['CA'],
    popular: true,
  },
];

// Helper functions
export const getCurrencyByCode = (code: string): CurrencyInfo | undefined => {
  return currenciesData.find(currency => currency.code === code);
};

export const getPopularCurrencies = (): CurrencyInfo[] => {
  return currenciesData.filter(currency => currency.popular);
};

export const getCurrenciesByCountry = (countryCode: string): CurrencyInfo[] => {
  return currenciesData.filter(currency => currency.countries.includes(countryCode));
};
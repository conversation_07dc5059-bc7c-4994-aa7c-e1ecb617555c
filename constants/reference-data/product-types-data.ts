/**
 * Professional hair product types reference data
 * Includes both Spanish and English names for AI compatibility
 */

export interface ProductType {
  id: string;
  name: string; // Spanish display name
  englishName: string; // English name for AI matching
  category: 'coloring' | 'treatment' | 'washing' | 'styling' | 'other';
  description?: string;
  commonBrands?: string[]; // Brands that commonly have this product type
  searchTerms: string[]; // Additional search terms for better matching
}

export const productTypes: ProductType[] = [
  // Coloring category
  {
    id: 'tinte',
    name: 'Tinte/Color',
    englishName: 'Hair Color',
    category: 'coloring',
    description: 'Coloración permanente o semipermanente',
    searchTerms: ['color', 'tinte', 'coloración', 'hair color', 'dye']
  },
  {
    id: 'oxidante',
    name: 'Oxidante/Revelador',
    englishName: 'Developer',
    category: 'coloring',
    description: 'Peróxido activador para coloración',
    searchTerms: ['oxidante', 'revelador', 'developer', 'peróxido', 'activador', 'oxigenada']
  },
  {
    id: 'decolorante',
    name: 'Decolorante/Blanqueador',
    englishName: 'Bleach',
    category: 'coloring',
    description: 'Polvo o crema decolorante',
    searchTerms: ['decolorante', 'bleach', 'blanqueador', 'lightener', 'polvo decolorante']
  },
  {
    id: 'matizador',
    name: 'Matizador/Toner',
    englishName: 'Toner',
    category: 'coloring',
    description: 'Neutraliza tonos no deseados',
    searchTerms: ['matizador', 'toner', 'violeta', 'silver', 'neutralizador']
  },
  {
    id: 'pre-pigmentacion',
    name: 'Pre-pigmentación',
    englishName: 'Pre-pigment',
    category: 'coloring',
    description: 'Prepara el cabello para mejor absorción del color',
    searchTerms: ['prepigmentación', 'pre-pigment', 'mordiente', 'filler']
  },

  // Treatment category
  {
    id: 'tratamiento',
    name: 'Tratamiento Reconstructor',
    englishName: 'Treatment',
    category: 'treatment',
    description: 'Tratamiento reparador o reconstructor',
    searchTerms: ['tratamiento', 'treatment', 'reconstructor', 'reparador']
  },
  {
    id: 'protector',
    name: 'Protector/Aditivo',
    englishName: 'Protector/Additive',
    category: 'treatment',
    description: 'Protege el cabello durante procesos químicos',
    commonBrands: ['Olaplex', 'Wellaplex', 'Fibreplex'],
    searchTerms: ['protector', 'aditivo', 'plex', 'bond', 'additive', 'booster']
  },
  {
    id: 'mascarilla',
    name: 'Mascarilla Capilar',
    englishName: 'Hair Mask',
    category: 'treatment',
    description: 'Tratamiento intensivo de hidratación',
    searchTerms: ['mascarilla', 'mask', 'máscara', 'intensivo']
  },
  {
    id: 'aceite',
    name: 'Aceite Capilar',
    englishName: 'Hair Oil',
    category: 'treatment',
    description: 'Aceite nutritivo o de acabado',
    searchTerms: ['aceite', 'oil', 'óleo', 'elixir']
  },
  {
    id: 'serum',
    name: 'Serum/Ampolla',
    englishName: 'Serum',
    category: 'treatment',
    description: 'Tratamiento concentrado',
    searchTerms: ['serum', 'sérum', 'ampolla', 'ampoule', 'vial']
  },

  // Washing category
  {
    id: 'champu',
    name: 'Champú Técnico',
    englishName: 'Shampoo',
    category: 'washing',
    description: 'Champú profesional o técnico',
    searchTerms: ['champú', 'shampoo', 'champu', 'lavado']
  },
  {
    id: 'acondicionador',
    name: 'Acondicionador',
    englishName: 'Conditioner',
    category: 'washing',
    description: 'Acondicionador o suavizante',
    searchTerms: ['acondicionador', 'conditioner', 'suavizante', 'bálsamo']
  },
  {
    id: 'neutralizante',
    name: 'Neutralizante',
    englishName: 'Neutralizer',
    category: 'washing',
    description: 'Neutraliza procesos químicos',
    searchTerms: ['neutralizante', 'neutralizer', 'stop']
  },

  // Styling category
  {
    id: 'gel',
    name: 'Gel/Cera/Pomada',
    englishName: 'Styling Gel/Wax',
    category: 'styling',
    description: 'Productos de fijación y modelado',
    searchTerms: ['gel', 'cera', 'wax', 'pomada', 'paste', 'clay']
  },
  {
    id: 'laca',
    name: 'Laca/Spray Fijador',
    englishName: 'Hairspray',
    category: 'styling',
    description: 'Fijación en spray',
    searchTerms: ['laca', 'spray', 'hairspray', 'fijador', 'finish']
  },
  {
    id: 'espuma',
    name: 'Espuma/Mousse',
    englishName: 'Mousse',
    category: 'styling',
    description: 'Espuma moldeadora',
    searchTerms: ['espuma', 'mousse', 'foam', 'voluminizador']
  },
  {
    id: 'protector-termico',
    name: 'Protector Térmico',
    englishName: 'Heat Protectant',
    category: 'styling',
    description: 'Protege del calor de secadores y planchas',
    searchTerms: ['protector térmico', 'heat protectant', 'thermal', 'calor']
  },

  // Other category
  {
    id: 'otro',
    name: 'Otro',
    englishName: 'Other',
    category: 'other',
    description: 'Otros productos profesionales',
    searchTerms: ['otro', 'other', 'varios', 'misc']
  }
];

/**
 * Search product types by query
 */
export function searchProductTypes(query: string): ProductType[] {
  const normalizedQuery = query.toLowerCase().trim();
  
  if (!normalizedQuery) {
    return productTypes;
  }

  return productTypes.filter(type => {
    // Check main name
    if (type.name.toLowerCase().includes(normalizedQuery)) {
      return true;
    }
    
    // Check English name
    if (type.englishName.toLowerCase().includes(normalizedQuery)) {
      return true;
    }
    
    // Check search terms
    return type.searchTerms.some(term => 
      term.toLowerCase().includes(normalizedQuery)
    );
  });
}

/**
 * Get product type by ID
 */
export function getProductTypeById(id: string): ProductType | undefined {
  return productTypes.find(type => type.id === id);
}

/**
 * Get product types by category
 */
export function getProductTypesByCategory(category: ProductType['category']): ProductType[] {
  return productTypes.filter(type => type.category === category);
}

/**
 * Get all product type categories with labels
 */
export const productCategories = [
  { id: 'coloring', label: 'Coloración' },
  { id: 'treatment', label: 'Tratamientos' },
  { id: 'washing', label: 'Lavado' },
  { id: 'styling', label: 'Acabado y Fijación' },
  { id: 'other', label: 'Otros' }
] as const;
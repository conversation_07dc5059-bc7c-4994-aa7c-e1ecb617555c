// Premium color palette inspired by professional hair salon branding
// Updated for WCAG 2.1 AA compliance
const premiumBlack = "#1C1C1C";
const goldPrimary = "#B8941F"; // Darkened for better contrast
const goldDark = "#8B6F1F"; // Darkened for better contrast
const goldLight = "#E6C757";
const warmOrange = "#D47A3A"; // Darkened for better contrast
const beigeLight = "#F5F5F7";
const creamWhite = "#FFFFFF";
const successGreen = "#2E7D32"; // Darkened for better contrast
const warmGray = "#6B5D54"; // Darkened for better contrast
const warningYellow = "#F57C00"; // Darkened for better contrast
const errorRed = "#C62828"; // Darkened for better contrast
const infoBlue = "#1565C0"; // Darkened for better contrast

export default {
  light: {
    // Text colors
    text: "#1A1612", // Darkened for better contrast
    textSecondary: warmGray,
    textLight: "#FFFFFF",
    
    // Background colors
    background: "#FFFFFF",
    backgroundSecondary: "#F5F5F7",
    backgroundDark: premiumBlack,
    
    // Brand colors
    primary: goldPrimary,
    primaryDark: goldDark,
    primaryLight: goldLight,
    secondary: warmOrange,
    accent: "#CD853F",
    
    // UI elements
    card: "#FFFFFF",
    cardDark: premiumBlack,
    surface: "#F5F5F7",
    border: "#E5E5E5",
    borderLight: "#F0F0F0",
    
    // Navigation
    tabIconDefault: warmGray,
    tabIconSelected: goldPrimary,
    tint: goldPrimary,
    
    // Status colors
    success: successGreen,
    warning: warningYellow,
    error: errorRed,
    danger: errorRed,
    info: infoBlue,
    
    // Grays
    gray: warmGray,
    grayLight: "#C4C4C4",
    lightGray: "#F5F5F7",
    darkGray: "#4A4A4A",
    
    // Special UI states
    notification: warmOrange,
    highlight: goldLight,
    
    // AI Analysis specific colors
    aiProcessing: goldPrimary,
    aiSuccess: successGreen,
    aiWarning: warningYellow,
    aiError: errorRed,
    
    // Privacy and security colors
    privacy: successGreen,
    security: infoBlue,
    
    // Quality indicators
    qualityExcellent: successGreen,
    qualityGood: goldPrimary,
    qualityPoor: errorRed,
    
    // Progress indicators
    progressBackground: "#F5F5F7",
    progressFill: goldPrimary,
    
    // Shadows
    shadowColor: "#000000",
    shadowLight: "rgba(0, 0, 0, 0.05)",
    shadowMedium: "rgba(0, 0, 0, 0.1)",
  },
};
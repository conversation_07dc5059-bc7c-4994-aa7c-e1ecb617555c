#!/usr/bin/env node

/**
 * Script simple para probar la Edge Function optimizada
 * Usa node scripts/test-edge-function-simple.js
 */

// Test rápido usando curl
const { exec } = require('child_process');

// Configuración - actualiza estos valores
const SUPABASE_URL = 'https://ajsamgugqfbttkrlgvbr.supabase.co';
const ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFqc2FtZ3VncWZidHRrcmxndmJyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjA0NzEzMTEsImV4cCI6MjAzNjA0NzMxMX0.JvI2T_3DWKhO6DJQhO3qfrCKFV5EygLJrIg5pVDRT3c';

// Necesitas un token de autenticación válido
// Puedes obtenerlo haciendo login en la app y mirando el localStorage
const AUTH_TOKEN = 'TU_TOKEN_AQUI'; // <-- ACTUALIZA ESTO

const testPayload = {
  task: 'diagnose_image',
  payload: {
    imageBase64: '/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCABAAEADASIAAhEBAxEB/8QAGwAAAgMBAQEAAAAAAAAAAAAABQYDBAcCAQD/xAA1EAACAQMCBAMGBQMFAAAAAAABAgMABBEFIQYSMUETUWEHFCJxgZEVMkKhsRYjwVJi0eHw/8QAGAEAAwEBAAAAAAAAAAAAAAAAAQIDAAT/xAAYEQEBAQEBAAAAAAAAAAAAAAAAARECEv/aAAwDAQACEQMRAD8AWuJuLtS1SVljkMUQ6KpqG71m4nt1E0zOQNt6rPaFF8WRRt51yITIMRjIFcsXCtK0gKOwI9Kht7e6vJAIEdz5AZonLBPGMSBlPmRTZ7M7y00y1me4ZQ0jdWO2KPM2gVlocdrP/qB23zRyOwXA2A9avHW9AmPM0iFvMjcVZtr7RZyBHMufImqZBsRa1zjTq9tMrqzD4c9q9l0KW9uRFGjHfc42FN0NnprLnEf71Laj3ef4VUfL1oCXtf4JltuVHLOD0BOwpWu9Ilhm8PkYnPQCtcju4xqxknuVWJVx8RxmlbiHWIJbkeDMrqBgFTkE0Jo6L9QoWldoMHkY9s0Zu+DbuS1Mr8sIHVnbb9qW2aYgZLZPUb0/8FcXNPI2m6ukkkBHKJG6oaMqbClxHY6tBZCztsSzyNgOwwFXG+fvUfAejrcakGlJWO0HOR5v2H8/amnXNP8AeNZjuLZ1khKjl36YH+c1HbQvbMByBX6EL51XdYFx2K4BGDnpXVrKgABbB+dSxBWAqteo6EgI3yNJUMSajeR6bp8lzO4CIMDfqaS5rgzvJcyHLyMWJ+Zqpr2qy6nKkJXw4U/SepPmao+PygZJ+tdcc9PICcZ/3dfl8qgZRnLHA9KjEjSZOaqdyPQDqaIkfr2r0MD03+tQSZYjmqPmIPetGP/Z'
  }
};

console.log('🚀 Probando Edge Function optimizada v10...\n');

if (AUTH_TOKEN === 'TU_TOKEN_AQUI') {
  console.error('❌ Error: Necesitas actualizar AUTH_TOKEN en el script');
  console.log('\nPasos para obtener el token:');
  console.log('1. Abre la app en un navegador');
  console.log('2. Inicia sesión');
  console.log('3. Abre la consola del navegador (F12)');
  console.log('4. Ejecuta: localStorage.getItem("supabase.auth.token")');
  console.log('5. Copia el valor del access_token y pégalo en AUTH_TOKEN\n');
  process.exit(1);
}

const curlCommand = `curl -X POST "${SUPABASE_URL}/functions/v1/salonier-assistant" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${AUTH_TOKEN}" \
  -H "apikey: ${ANON_KEY}" \
  -d '${JSON.stringify(testPayload)}' \
  -w "\n\nTiempo total: %{time_total}s\n"`;

console.log('📡 Enviando solicitud de diagnóstico...\n');

exec(curlCommand, (error, stdout, stderr) => {
  if (error) {
    console.error('❌ Error:', error.message);
    return;
  }
  
  if (stderr) {
    console.error('❌ Error:', stderr);
    return;
  }
  
  console.log('📥 Respuesta recibida:\n');
  
  try {
    // Extraer JSON de la respuesta
    const jsonMatch = stdout.match(/\{.*\}/s);
    if (jsonMatch) {
      const response = JSON.parse(jsonMatch[0]);
      
      if (response.success) {
        console.log('✅ Diagnóstico exitoso!');
        console.log('\n📊 Métricas:');
        console.log(`  - Tokens usados: ${response.metrics?.tokensUsed || 0}`);
        console.log(`  - Costo: $${response.metrics?.cost?.toFixed(4) || 0}`);
        console.log(`  - Cache hit: ${response.metrics?.cacheHit ? '✅ Sí' : '❌ No'}`);
        
        console.log('\n🔍 Datos del diagnóstico:');
        console.log(`  - Grosor: ${response.data?.hairThickness}`);
        console.log(`  - Densidad: ${response.data?.hairDensity}`);
        console.log(`  - Tono general: ${response.data?.overallTone}`);
        console.log(`  - Nivel promedio: ${response.data?.averageDepthLevel}`);
      } else {
        console.log('❌ Error en el diagnóstico:', response.error);
      }
    }
    
    // Mostrar tiempo total
    const timeMatch = stdout.match(/Tiempo total: ([\d.]+)s/);
    if (timeMatch) {
      console.log(`\n⏱️  Tiempo total: ${timeMatch[1]}s`);
    }
    
  } catch (e) {
    console.log('Respuesta completa:', stdout);
  }
});

console.log('\n💡 Para más pruebas, edita este script y cambia el task y payload');
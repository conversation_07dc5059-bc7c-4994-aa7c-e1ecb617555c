#!/usr/bin/env ts-node

/**
 * Script para validar que la respuesta de la Edge Function contenga todos los campos esperados
 * Uso: npm run validate-ai-response
 */

import { HairZone } from '../types/hair-diagnosis';

// Estructura esperada de la respuesta de IA
interface ExpectedAIResponse {
  hairThickness: string;
  hairDensity: string;
  overallTone: string;
  overallUndertone: string;
  averageDepthLevel: number;
  zoneAnalysis: {
    [zone: string]: {
      depth: number;
      tone: string;
      undertone: string;
      percentage: number;
      state: string;
      unwantedTone: string | null;
      // Campos específicos de raíces
      grayPercentage?: number;
      grayType?: string | null;
      grayPattern?: string | null;
      // Campos de análisis físico
      cuticleState: string;
      damage: string;
      elasticity: string;
      porosity: string;
      resistance: string;
      // Campos específicos de medios/puntas
      demarkationBands?: Array<{ location: number; contrast: string }>;
      pigmentAccumulation?: string;
    };
  };
  detectedChemicalProcess: string | null;
  estimatedLastProcessDate: string;
  detectedRisks: {
    metallic: boolean;
    henna: boolean;
    damaged: boolean;
    overProcessed: boolean;
    incompatibleProducts: boolean;
  };
  serviceComplexity: string;
  estimatedTime: number;
  overallCondition: string;
  recommendations: string[];
  overallConfidence: number;
}

// Función para validar la estructura
function validateAIResponse(response: any): { valid: boolean; missing: string[] } {
  const missing: string[] = [];
  
  // Validar campos principales
  const mainFields = [
    'hairThickness', 'hairDensity', 'overallTone', 'overallUndertone',
    'averageDepthLevel', 'zoneAnalysis', 'detectedChemicalProcess',
    'estimatedLastProcessDate', 'detectedRisks', 'serviceComplexity',
    'estimatedTime', 'overallCondition', 'recommendations', 'overallConfidence'
  ];
  
  for (const field of mainFields) {
    if (!(field in response)) {
      missing.push(field);
    }
  }
  
  // Validar zonas
  if (response.zoneAnalysis) {
    const zones = [HairZone.ROOTS, HairZone.MIDS, HairZone.ENDS];
    const zoneFields = [
      'depth', 'tone', 'undertone', 'percentage', 'state',
      'cuticleState', 'damage', 'elasticity', 'porosity', 'resistance'
    ];
    
    for (const zone of zones) {
      if (!response.zoneAnalysis[zone]) {
        missing.push(`zoneAnalysis.${zone}`);
        continue;
      }
      
      for (const field of zoneFields) {
        if (!(field in response.zoneAnalysis[zone])) {
          missing.push(`zoneAnalysis.${zone}.${field}`);
        }
      }
      
      // Campos específicos de raíces
      if (zone === HairZone.ROOTS) {
        if (!('grayPercentage' in response.zoneAnalysis[zone])) {
          missing.push(`zoneAnalysis.${zone}.grayPercentage`);
        }
      }
      
      // Campos específicos de medios
      if (zone === HairZone.MIDS && !('demarkationBands' in response.zoneAnalysis[zone])) {
        missing.push(`zoneAnalysis.${zone}.demarkationBands`);
      }
      
      // Campos de medios y puntas
      if ((zone === HairZone.MIDS || zone === HairZone.ENDS) && !('pigmentAccumulation' in response.zoneAnalysis[zone])) {
        missing.push(`zoneAnalysis.${zone}.pigmentAccumulation`);
      }
    }
  }
  
  // Validar detectedRisks
  if (response.detectedRisks) {
    const riskFields = ['metallic', 'henna', 'damaged', 'overProcessed', 'incompatibleProducts'];
    for (const field of riskFields) {
      if (!(field in response.detectedRisks)) {
        missing.push(`detectedRisks.${field}`);
      }
    }
  }
  
  return {
    valid: missing.length === 0,
    missing
  };
}

// Ejemplo de respuesta para probar
const exampleResponse = {
  hairThickness: "Medio",
  hairDensity: "Media",
  overallTone: "Castaño medio",
  overallUndertone: "Cálido",
  averageDepthLevel: 6.5,
  zoneAnalysis: {
    [HairZone.ROOTS]: {
      depth: 5.5,
      tone: "Castaño claro",
      undertone: "Cálido",
      percentage: 30,
      state: "Natural",
      unwantedTone: null,
      grayPercentage: 15,
      grayType: "Blanco",
      grayPattern: "Disperso",
      cuticleState: "Cerrada",
      damage: "Ninguno",
      elasticity: "Buena",
      porosity: "Baja",
      resistance: "Fuerte"
    },
    [HairZone.MIDS]: {
      depth: 6.5,
      tone: "Castaño medio",
      undertone: "Cálido",
      percentage: 40,
      state: "Teñido",
      unwantedTone: "Naranja",
      demarkationBands: [{ location: 10, contrast: "Medio" }],
      pigmentAccumulation: "Leve",
      cuticleState: "Abierta",
      damage: "Leve",
      elasticity: "Regular",
      porosity: "Media",
      resistance: "Media"
    },
    [HairZone.ENDS]: {
      depth: 7.5,
      tone: "Castaño oscuro",
      undertone: "Cálido",
      percentage: 30,
      state: "Teñido",
      unwantedTone: "Rojo",
      pigmentAccumulation: "Moderada",
      cuticleState: "Dañada",
      damage: "Moderado",
      elasticity: "Mala",
      porosity: "Alta",
      resistance: "Débil"
    }
  },
  detectedChemicalProcess: "Coloración",
  estimatedLastProcessDate: "Hace 2-3 meses",
  detectedRisks: {
    metallic: false,
    henna: false,
    damaged: true,
    overProcessed: false,
    incompatibleProducts: false
  },
  serviceComplexity: "medium",
  estimatedTime: 120,
  overallCondition: "Cabello con proceso químico previo, requiere tratamiento de hidratación",
  recommendations: [
    "Aplicar tratamiento de hidratación profunda",
    "Usar técnica de pre-pigmentación en puntas",
    "Considerar un corte de puntas para eliminar daño"
  ],
  overallConfidence: 85
};

// Ejecutar validación
console.log("🔍 Validando estructura de respuesta de IA...\n");

const validation = validateAIResponse(exampleResponse);

if (validation.valid) {
  console.log("✅ La estructura de respuesta es válida!");
  console.log("   Todos los campos esperados están presentes.\n");
} else {
  console.log("❌ La estructura de respuesta NO es válida!");
  console.log("   Campos faltantes:");
  validation.missing.forEach(field => {
    console.log(`   - ${field}`);
  });
  console.log("");
}

console.log("📋 Campos críticos para el formulario:");
console.log("   - Análisis físico por zona: porosity, elasticity, resistance, damage");
console.log("   - Estado de cutícula: cuticleState");
console.log("   - Tonos no deseados: unwantedTone");
console.log("   - Acumulación de pigmentos: pigmentAccumulation");
console.log("   - Bandas de demarcación: demarkationBands");
console.log("   - Análisis de canas: grayPercentage, grayType, grayPattern");

export { validateAIResponse, ExpectedAIResponse };
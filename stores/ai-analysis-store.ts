/**
 * AI Analysis Store - Optimizado v2
 * 
 * Optimizaciones:
 * - performImageAnalysis unificada (-200 líneas)
 * - Logging condicional (sin logs en producción)
 * - ImageProcessor para compresión centralizada
 * - Retry con exponential backoff mejorado
 * - Reducido de 646 a ~390 líneas
 */

import { create } from "zustand";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { supabase, getCurrentSalonId } from "@/lib/supabase";
import { FunctionsHttpError, FunctionsRelayError, FunctionsFetchError } from '@supabase/supabase-js';
import { logger } from '@/utils/logger';
import { ImageProcessor } from '@/utils/image-processor';
import { uploadAndAnonymizeImage } from '@/utils/secure-image-upload';
import { useAuthStore } from '@/stores/auth-store';

// ============= INTERFACES =============
interface ZoneAnalysisResult {
  zone: string;
  level: number;
  depthLevel?: number; // Compatibilidad
  tone: string;
  reflect: string;
  undertone?: string; // Compatibilidad
  state: string;
  grayPercentage?: number;
  porosity: string;
  elasticity: string;
  resistance: string;
  damage: string;
  unwantedTone?: string;
  confidence: number;
  grayType?: string;
  grayPattern?: string;
  pigmentAccumulation?: string;
  cuticleState?: string;
  demarkationBands?: { location: number; contrast: string }[];
}

interface AIAnalysisResult {
  hairThickness: string;
  hairDensity: string;
  overallTone: string;
  overallReflect: string;
  overallUndertone?: string; // Compatibilidad
  averageLevel: number;
  averageDepthLevel?: number; // Compatibilidad
  zoneAnalysis: {
    roots: ZoneAnalysisResult;
    mids: ZoneAnalysisResult;
    ends: ZoneAnalysisResult;
  };
  detectedChemicalProcess?: string;
  estimatedLastProcessDate?: string;
  detectedHomeRemedies?: boolean;
  detectedRisks?: {
    metalSalts: { detected: boolean; confidence: number; signs: string[] };
    henna: { detected: boolean; confidence: number; signs: string[] };
    extremeDamage: { detected: boolean; zones: string[] };
  };
  serviceComplexity?: 'simple' | 'medium' | 'complex';
  estimatedTime?: number;
  overallCondition: string;
  recommendations: string[];
  overallConfidence: number;
  analysisTimestamp: number;
}

interface DesiredPhotoAnalysis {
  photoId: string;
  detectedLevel: number;
  detectedTone: string;
  detectedTechnique: string;
  detectedTones: string[];
  viabilityScore: number;
  estimatedSessions: number;
  requiredProcesses: string[];
  confidence: number;
  warnings?: string[];
  zoneAnalysis?: any; // Zone-specific analysis data
}

interface AIAnalysisSettings {
  autoFaceBlur: boolean;
  imageQualityThreshold: number;
  privacyMode: boolean;
  saveAnalysisHistory: boolean;
}

interface AIAnalysisState {
  isAnalyzing: boolean;
  analysisResult: AIAnalysisResult | null;
  analysisHistory: AIAnalysisResult[];
  isAnalyzingDesiredPhoto: boolean;
  analyzingPhotoId: string | null;
  desiredPhotoAnalyses: Record<string, DesiredPhotoAnalysis>;
  settings: AIAnalysisSettings;
  privacyMode: boolean;
  
  analyzeImage: (imageUri: string) => Promise<void>;
  analyzeDesiredPhoto: (photoId: string, imageUri: string, diagnosis: any, clientId?: string) => Promise<DesiredPhotoAnalysis | null>;
  clearAnalysis: () => void;
  updateSettings: (settings: Partial<AIAnalysisSettings>) => Promise<void>;
  setPrivacyMode: (enabled: boolean) => void;
  getAnalysisHistory: () => AIAnalysisResult[];
  clearAnalysisHistory: () => Promise<void>;
}

// ============= CONFIGURACIÓN =============
const RETRY_CONFIG = {
  maxAttempts: 3,
  timeout: 30000,
  backoffDelay: 1500
};

// ============= FUNCIÓN COMPARTIDA PRINCIPAL =============
async function performImageAnalysis(
  imageUri: string,
  task: 'diagnose_image' | 'analyze_desired_look',
  additionalPayload: any = {}
): Promise<any> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), RETRY_CONFIG.timeout);

  try {
    // Determinar si es una URL pública o una URI local
    const isPublicUrl = imageUri.startsWith('http://') || imageUri.startsWith('https://');
    let payload: any = { ...additionalPayload };
    
    if (isPublicUrl) {
      // Si es una URL pública (imagen ya anonimizada), usar directamente
      logger.info('Using public URL for analysis', 'AIAnalysisStore', { url: imageUri });
      payload.imageUrl = imageUri;
    } else {
      // Si es una URI local, mantener compatibilidad con base64 por ahora
      // TODO: Migrar completamente a URLs después de actualizar el flujo de captura
      logger.info('Using base64 for compatibility (local URI)');
      
      // Validar y comprimir imagen
      const validation = await ImageProcessor.validateQuality(imageUri);
      if (!validation.valid) {
        const errors = validation.issues
          .filter(i => i.severity === 'error')
          .map(i => i.message)
          .join('. ');
        throw new Error(errors || 'Imagen no válida');
      }
      
      const purpose = task === 'diagnose_image' ? 'diagnosis' : 'desired';
      const imageBase64 = await ImageProcessor.compressForAI(imageUri, purpose);
      payload.imageBase64 = imageBase64;
    }

    // 2. Verificar sesión y obtener token
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      throw new Error('No hay sesión activa. Por favor, cierra y vuelve a abrir la aplicación.');
    }

    // 2.5. Verificar que el usuario tenga salon_id
    const salonId = await getCurrentSalonId();
    if (!salonId) {
      logger.error('User has no salon_id, attempting to repair...', 'AIAnalysisStore');
      
      // Intentar reparar el perfil
      const authStore = useAuthStore.getState();
      if (authStore.user) {
        await authStore.initializeAuth(); // This will trigger ensureUserHasSalonId
        
        // Verificar nuevamente
        const repairedSalonId = await getCurrentSalonId();
        if (!repairedSalonId) {
          throw new Error('Usuario no asociado a ningún salón. Por favor, contacta al administrador.');
        }
      } else {
        throw new Error('Usuario no autenticado correctamente. Por favor, cierra sesión y vuelve a iniciar.');
      }
    }

    // 3. Llamar Edge Function con reintentos
    let lastError: any;
    for (let attempt = 1; attempt <= RETRY_CONFIG.maxAttempts; attempt++) {
      try {
        const { data, error } = await supabase.functions.invoke('salonier-assistant', {
          body: { task, payload },
          headers: {
            Authorization: `Bearer ${session.access_token}`
          },
          signal: controller.signal
        });

        // Log detailed response information for debugging
        logger.debug('Edge function response', 'AIAnalysisStore', {
          attempt,
          task,
          hasError: !!error,
          hasData: !!data,
          dataKeys: data ? Object.keys(data) : null,
          dataSuccess: data?.success,
          dataError: data?.error
        });

        if (error) {
          logger.error('Edge function HTTP error', 'AIAnalysisStore', error);
          const errorMessage = await handleEdgeFunctionError(error);
          // Si es error 401, intentar refrescar sesión
          if (errorMessage.includes('401') || errorMessage.includes('Invalid token')) {
            logger.warn('Token inválido, intentando refrescar sesión...', 'AIAnalysisStore');
            const { data: { session: newSession }, error: refreshError } = await supabase.auth.refreshSession();
            if (refreshError || !newSession) {
              throw new Error('Sesión expirada. Por favor, cierra y vuelve a abrir la aplicación.');
            }
            // Reintentar con el nuevo token
            continue;
          }
          throw new Error(errorMessage);
        }
        if (!data) throw new Error('Sin respuesta del servidor');
        if (data.success === true && data.data === null) throw new Error('Respuesta vacía de OpenAI');
        
        if (!data.success) {
          logger.error('Edge function returned error', 'AIAnalysisStore', {
            success: data.success,
            error: data.error,
            attempt,
            maxAttempts: RETRY_CONFIG.maxAttempts,
            task
          });
          
          if (data.error && data.error.includes('Invalid response from OpenAI') && attempt < RETRY_CONFIG.maxAttempts) {
            logger.warn(`Retrying due to OpenAI response error (attempt ${attempt}/${RETRY_CONFIG.maxAttempts})...`);
            await new Promise(r => setTimeout(r, RETRY_CONFIG.backoffDelay * attempt));
            continue;
          }
          throw new Error(data.error || 'Error desconocido');
        }

        if (!data.data) throw new Error('Respuesta incompleta');
        
        clearTimeout(timeoutId);
        return data.data;

      } catch (error: any) {
        lastError = error;
        if (error.name === 'AbortError') throw new Error('TIMEOUT_ERROR');
        
        if (attempt < RETRY_CONFIG.maxAttempts && !error.message.includes('TIMEOUT_ERROR')) {
          const delay = RETRY_CONFIG.backoffDelay * Math.pow(2, attempt - 1);
          await new Promise(r => setTimeout(r, delay));
        } else {
          throw error;
        }
      }
    }
    throw lastError || new Error('Máximos reintentos alcanzados');
  } catch (error: any) {
    clearTimeout(timeoutId);
    throw error;
  } finally {
    clearTimeout(timeoutId);
  }
}

// ============= FUNCIONES ESPECÍFICAS =============
async function performAIAnalysis(imageUri: string): Promise<AIAnalysisResult> {
  const result = await performImageAnalysis(imageUri, 'diagnose_image', {});
  
  if (!result.hairThickness || !result.hairDensity || !result.zoneAnalysis) {
    throw new Error('Análisis incompleto. Intenta con otra foto más clara.');
  }
  
  return { ...result, analysisTimestamp: Date.now() };
}

async function analyzeDesiredColorPhoto(
  photoId: string, 
  imageUri: string, 
  diagnosis: any
): Promise<DesiredPhotoAnalysis> {
  // Si diagnosis es un número, mantener compatibilidad hacia atrás
  const payload = typeof diagnosis === 'number' 
    ? { currentLevel: diagnosis } 
    : { diagnosis };
    
  const result = await performImageAnalysis(imageUri, 'analyze_desired_look', payload);
  return { photoId, ...result };
}

// ============= MANEJO DE ERRORES =============
async function handleEdgeFunctionError(error: any): Promise<string> {
  logger.error('Edge Function Error', 'AIAnalysisStore', error);
  
  if (error instanceof FunctionsHttpError) {
    const status = error.context.status;
    logger.error('HTTP Status', 'AIAnalysisStore', status);
    
    try {
      const errorData = await error.context.json();
      logger.error('Function returned error', 'AIAnalysisStore', errorData);
      
      // Incluir el status HTTP en el mensaje para mejor manejo
      if (status === 401) {
        return `HTTP 401: ${errorData.error || errorData.message || 'Invalid token'}`;
      }
      
      return errorData.error || errorData.message || 'Error en el servidor';
    } catch (e) {
      logger.error('Could not parse error response', 'AIAnalysisStore', e);
      return `HTTP ${status}: Error al procesar la respuesta del servidor`;
    }
  } else if (error instanceof FunctionsRelayError) {
    return 'Error de conexión con el servidor';
  } else if (error instanceof FunctionsFetchError) {
    return 'Error al conectar con el servidor';
  } else {
    return error.message || 'Error desconocido';
  }
}

// ============= ZUSTAND STORE =============
export const useAIAnalysisStore = create<AIAnalysisState>((set, get) => ({
  isAnalyzing: false,
  analysisResult: null,
  analysisHistory: [],
  isAnalyzingDesiredPhoto: false,
  analyzingPhotoId: null,
  desiredPhotoAnalyses: {},
  privacyMode: true,
  settings: {
    autoFaceBlur: true,
    imageQualityThreshold: 60,
    privacyMode: true,
    saveAnalysisHistory: false,
  },

  analyzeImage: async (imageUri: string) => {
    set({ isAnalyzing: true, analysisResult: null });
    
    try {
      const result = await performAIAnalysis(imageUri);
      logger.info("AI Analysis completed successfully");
      
      set(state => ({
        isAnalyzing: false,
        analysisResult: result,
        analysisHistory: state.settings.saveAnalysisHistory 
          ? [...state.analysisHistory, result]
          : state.analysisHistory
      }));
      
      // Haptic feedback for successful analysis
      try {
        const Haptics = require('expo-haptics');
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      } catch {
        // Silently fail if haptics not available
      }
      
      // Guardar historial si está habilitado
      const { settings, analysisHistory } = get();
      if (settings.saveAnalysisHistory) {
        try {
          await AsyncStorage.setItem(
            'salonier-analysis-history', 
            JSON.stringify(analysisHistory)
          );
        } catch (error) {
          logger.error('Error saving analysis history', 'AIAnalysisStore', error);
        }
      }
      
    } catch (error: any) {
      set({ isAnalyzing: false });
      logger.error('Error in analyzeImage', 'AIAnalysisStore', error);
      
      // Mensajes de error user-friendly
      if (error.message === 'TIMEOUT_ERROR') {
        throw new Error('El análisis tardó demasiado. Por favor, intenta de nuevo.');
      } else if (error.message.includes('servidor')) {
        throw new Error('No se pudo conectar con el servidor. Verifica tu conexión.');
      } else {
        throw new Error('Error al procesar la imagen. Por favor, intenta con otra foto.');
      }
    }
  },

  analyzeDesiredPhoto: async (photoId: string, imageUri: string, diagnosis: any, clientId?: string) => {
    logger.info('Starting desired photo analysis', 'AIAnalysisStore', { photoId, hasDiagnosis: !!diagnosis });
    set({ isAnalyzingDesiredPhoto: true, analyzingPhotoId: photoId });
    
    try {
      let finalImageUri = imageUri;
      
      // Check if image is already a public URL
      const isPublicUrl = imageUri.startsWith('http://') || imageUri.startsWith('https://');
      
      if (!isPublicUrl && clientId) {
        // Upload and anonymize the image first
        logger.info('Uploading desired photo for secure analysis');
        const uploadResult = await uploadAndAnonymizeImage(imageUri, {
          clientId,
          photoType: 'desired',
          onProgress: (progress) => {
            logger.debug(`Desired photo upload progress: ${progress}%`);
          }
        });
        
        if (!uploadResult.success || !uploadResult.publicUrl) {
          throw new Error(uploadResult.error || 'Failed to upload desired photo');
        }
        
        finalImageUri = uploadResult.publicUrl;
        logger.info('Desired photo uploaded successfully', 'AIAnalysisStore', { publicUrl: finalImageUri });
      }
      
      const analysis = await analyzeDesiredColorPhoto(photoId, finalImageUri, diagnosis);
      
      set(state => ({
        isAnalyzingDesiredPhoto: false,
        analyzingPhotoId: null,
        desiredPhotoAnalyses: {
          ...state.desiredPhotoAnalyses,
          [photoId]: analysis
        }
      }));
      
      return analysis;
    } catch (error: any) {
      set({ isAnalyzingDesiredPhoto: false, analyzingPhotoId: null });
      logger.error('Error analyzing desired photo', 'AIAnalysisStore', error);
      
      if (error.message === 'TIMEOUT_ERROR') {
        throw new Error('TIMEOUT_ERROR');
      }
      
      return null;
    }
  },

  clearAnalysis: () => {
    set({ 
      analysisResult: null, 
      desiredPhotoAnalyses: {},
      analyzingPhotoId: null 
    });
  },

  updateSettings: async (newSettings: Partial<AIAnalysisSettings>) => {
    const updatedSettings = { ...get().settings, ...newSettings };
    set({ settings: updatedSettings });
    
    try {
      await AsyncStorage.setItem(
        'salonier-ai-settings', 
        JSON.stringify(updatedSettings)
      );
    } catch (error) {
      logger.error('Error saving AI settings', 'AIAnalysisStore', error);
    }
  },

  setPrivacyMode: (enabled: boolean) => {
    set({ privacyMode: enabled });
  },

  getAnalysisHistory: () => {
    return get().analysisHistory;
  },

  clearAnalysisHistory: async () => {
    set({ analysisHistory: [] });
    try {
      await AsyncStorage.removeItem('salonier-analysis-history');
    } catch (error) {
      logger.error('Error clearing analysis history', 'AIAnalysisStore', error);
    }
  }
}));

// ============= INICIALIZACIÓN =============
const initializeAIAnalysisStore = async () => {
  try {
    const savedSettings = await AsyncStorage.getItem('salonier-ai-settings');
    const savedHistory = await AsyncStorage.getItem('salonier-analysis-history');
    
    if (savedSettings) {
      const settings = JSON.parse(savedSettings);
      useAIAnalysisStore.getState().updateSettings(settings);
    }
    
    if (savedHistory) {
      const history = JSON.parse(savedHistory);
      useAIAnalysisStore.setState({ analysisHistory: history });
    }
  } catch (error) {
    logger.error('Error initializing AI analysis store', 'AIAnalysisStore', error);
  }
};

initializeAIAnalysisStore();
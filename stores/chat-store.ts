import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '@/lib/supabase';
import { logger } from '@/utils/logger';
import { useAuthStore } from './auth-store';

export interface ChatAttachment {
  id?: string;
  type: 'image' | 'document';
  url: string;
  fileName?: string;
  fileSize?: number;
  mimeType?: string;
  width?: number;
  height?: number;
  thumbnailUrl?: string;
  localUri?: string; // Para imágenes no subidas aún
  uploadStatus?: 'pending' | 'uploading' | 'completed' | 'failed';
}

export interface ChatMessage {
  id: string;
  conversationId: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  promptTokens?: number;
  completionTokens?: number;
  totalTokens?: number;
  costUsd?: number;
  metadata?: Record<string, any>;
  createdAt: Date;
  localId?: string; // Para mensajes offline
  synced?: boolean;
  hasAttachments?: boolean;
  attachments?: ChatAttachment[];
}

export interface ChatConversation {
  id: string;
  salonId: string;
  userId: string;
  title: string;
  contextType?: 'general' | 'client' | 'service' | 'formula' | 'inventory';
  contextId?: string;
  status: 'active' | 'archived';
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  // Campos calculados
  messageCount?: number;
  lastMessageAt?: Date;
  totalTokensUsed?: number;
  totalCostUsd?: number;
  // Nuevos campos
  isFavorite?: boolean;
  lastMessage?: string;
}

export interface ChatContextReference {
  id: string;
  messageId: string;
  referenceType: 'client' | 'service' | 'formula' | 'product' | 'image';
  referenceId: string;
  referenceData?: Record<string, any>;
  createdAt: Date;
}

interface ChatStore {
  // Estado
  conversations: ChatConversation[];
  messages: Record<string, ChatMessage[]>; // Indexed by conversationId
  activeConversationId: string | null;
  isLoading: boolean;
  isSending: boolean;
  error: Error | null;
  uploadingImages: Record<string, boolean>; // Track upload status by localId
  
  // Sincronización
  pendingSyncMessages: ChatMessage[];
  lastSync: Date | null;
  
  // Actions - Conversations
  loadConversations: () => Promise<void>;
  createConversation: (params: {
    title?: string;
    contextType?: ChatConversation['contextType'];
    contextId?: string;
    metadata?: Record<string, any>;
  }) => Promise<ChatConversation | null>;
  updateConversation: (id: string, updates: Partial<ChatConversation>) => Promise<void>;
  archiveConversation: (id: string) => Promise<void>;
  deleteConversation: (id: string) => Promise<void>;
  toggleFavorite: (id: string) => Promise<void>;
  setActiveConversation: (id: string | null) => void;
  
  // Actions - Messages
  loadMessages: (conversationId: string) => Promise<void>;
  sendMessage: (content: string, conversationId?: string, attachments?: ChatAttachment[]) => Promise<void>;
  addOptimisticMessage: (message: Omit<ChatMessage, 'id' | 'createdAt'>) => string;
  updateOptimisticMessage: (localId: string, updates: Partial<ChatMessage>) => void;
  
  // Actions - Attachments
  uploadChatImage: (imageUri: string, messageId?: string) => Promise<ChatAttachment | null>;
  updateAttachmentStatus: (messageId: string, attachmentIndex: number, status: ChatAttachment['uploadStatus']) => void;
  
  // Actions - Context
  addContextReference: (messageId: string, reference: Omit<ChatContextReference, 'id' | 'messageId' | 'createdAt'>) => Promise<void>;
  
  // Sync
  syncPendingMessages: () => Promise<void>;
  
  // Utils
  generateSmartTitle: (content: string) => string;
  clearError: () => void;
  reset: () => void;
}

const initialState = {
  conversations: [],
  messages: {},
  activeConversationId: null,
  isLoading: false,
  isSending: false,
  error: null,
  uploadingImages: {},
  pendingSyncMessages: [],
  lastSync: null,
};

export const useChatStore = create<ChatStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Load conversations
      loadConversations: async () => {
        try {
          set({ isLoading: true, error: null });
          
          const { data: profile } = await supabase.auth.getUser();
          if (!profile?.user) {
            throw new Error('Usuario no autenticado');
          }

          const { data, error } = await supabase
            .from('chat_conversations_with_stats')
            .select('*')
            .eq('status', 'active')
            .order('updated_at', { ascending: false });

          if (error) throw error;

          const conversations = data.map((conv: any) => ({
            ...conv,
            createdAt: new Date(conv.created_at),
            updatedAt: new Date(conv.updated_at),
            lastMessageAt: conv.last_message_at ? new Date(conv.last_message_at) : undefined,
            messageCount: conv.message_count || 0,
            totalTokensUsed: conv.total_tokens_used || 0,
            totalCostUsd: parseFloat(conv.total_cost_usd || '0'),
            isFavorite: conv.metadata?.isFavorite || false,
            lastMessage: conv.last_message || '',
          }));

          set({ conversations, isLoading: false });
          logger.info('Chat conversations loaded', { count: conversations.length });
        } catch (error) {
          logger.error('Error loading conversations', error);
          set({ error: error as Error, isLoading: false });
        }
      },

      // Create conversation
      createConversation: async (params) => {
        try {
          const { user } = useAuthStore.getState();
          
          if (!user?.id || !user?.salonId) {
            throw new Error('Usuario o salón no configurado');
          }

          const conversationData = {
            salon_id: user.salonId,
            user_id: user.id,
            title: params.title || 'Nueva conversación',
            context_type: params.contextType,
            context_id: params.contextId,
            metadata: params.metadata || {},
            status: 'active',
          };

          const { data, error } = await supabase
            .from('chat_conversations')
            .insert(conversationData)
            .select()
            .single();

          if (error) throw error;

          const newConversation: ChatConversation = {
            ...data,
            createdAt: new Date(data.created_at),
            updatedAt: new Date(data.updated_at),
            messageCount: 0,
            totalTokensUsed: 0,
            totalCostUsd: 0,
          };

          set(state => ({
            conversations: [newConversation, ...state.conversations],
            activeConversationId: newConversation.id,
          }));

          logger.info('Conversation created', { id: newConversation.id });
          return newConversation;
        } catch (error) {
          logger.error('Error creating conversation', error);
          set({ error: error as Error });
          return null;
        }
      },

      // Update conversation
      updateConversation: async (id, updates) => {
        try {
          const { error } = await supabase
            .from('chat_conversations')
            .update(updates)
            .eq('id', id);

          if (error) throw error;

          set(state => ({
            conversations: state.conversations.map(conv =>
              conv.id === id
                ? { ...conv, ...updates, updatedAt: new Date() }
                : conv
            ),
          }));

          logger.info('Conversation updated', { id });
        } catch (error) {
          logger.error('Error updating conversation', error);
          set({ error: error as Error });
        }
      },

      // Archive conversation
      archiveConversation: async (id) => {
        await get().updateConversation(id, { status: 'archived' });
        set(state => ({
          conversations: state.conversations.filter(conv => conv.id !== id),
          activeConversationId: state.activeConversationId === id ? null : state.activeConversationId,
        }));
      },

      // Delete conversation permanently
      deleteConversation: async (id) => {
        try {
          // Delete all messages first
          const { error: messagesError } = await supabase
            .from('chat_messages')
            .delete()
            .eq('conversation_id', id);

          if (messagesError) throw messagesError;

          // Delete the conversation
          const { error: conversationError } = await supabase
            .from('chat_conversations')
            .delete()
            .eq('id', id);

          if (conversationError) throw conversationError;

          // Update local state
          set(state => ({
            conversations: state.conversations.filter(conv => conv.id !== id),
            messages: Object.fromEntries(
              Object.entries(state.messages).filter(([key]) => key !== id)
            ),
            activeConversationId: state.activeConversationId === id ? null : state.activeConversationId,
          }));

          logger.info('Conversation deleted permanently', { id });
        } catch (error) {
          console.error('Error deleting conversation:', error);
          set({ error: error as Error });
          throw error;
        }
      },

      // Toggle favorite
      toggleFavorite: async (id) => {
        const conversation = get().conversations.find(conv => conv.id === id);
        if (conversation) {
          const newFavoriteStatus = !conversation.isFavorite;
          await get().updateConversation(id, { 
            metadata: { 
              ...conversation.metadata, 
              isFavorite: newFavoriteStatus 
            } 
          });
          set(state => ({
            conversations: state.conversations.map(conv =>
              conv.id === id
                ? { ...conv, isFavorite: newFavoriteStatus }
                : conv
            ),
          }));
        }
      },

      // Set active conversation
      setActiveConversation: (id) => {
        set({ activeConversationId: id });
        if (id) {
          get().loadMessages(id);
        }
      },

      // Load messages
      loadMessages: async (conversationId) => {
        try {
          set({ isLoading: true, error: null });

          const { data, error } = await supabase
            .from('chat_messages')
            .select('*')
            .eq('conversation_id', conversationId)
            .order('created_at', { ascending: true });

          if (error) throw error;

          const messages = data.map((msg: any) => ({
            id: msg.id,
            conversationId: msg.conversation_id,
            role: msg.role,
            content: msg.content,
            promptTokens: msg.prompt_tokens,
            completionTokens: msg.completion_tokens,
            totalTokens: msg.total_tokens,
            costUsd: parseFloat(msg.cost_usd || '0'),
            metadata: msg.metadata,
            createdAt: new Date(msg.created_at),
            synced: true,
          }));

          set(state => ({
            messages: { ...state.messages, [conversationId]: messages },
            isLoading: false,
          }));

          logger.info('Messages loaded', { conversationId, count: messages.length });
        } catch (error) {
          logger.error('Error loading messages', error);
          set({ error: error as Error, isLoading: false });
        }
      },

      // Send message
      sendMessage: async (content, conversationId, attachments) => {
        const state = get();
        let targetConversationId = conversationId || state.activeConversationId;

        // Create conversation if needed
        if (!targetConversationId) {
          // Generate a smart title based on the first message
          const smartTitle = state.generateSmartTitle(content);
          const newConv = await state.createConversation({ title: smartTitle });
          if (!newConv) return;
          targetConversationId = newConv.id;
        }

        // Add optimistic user message
        const userMessageId = state.addOptimisticMessage({
          conversationId: targetConversationId,
          role: 'user',
          content,
          hasAttachments: !!attachments && attachments.length > 0,
          attachments: attachments || [],
        });

        set({ isSending: true, error: null });

        try {
          // Call Edge Function
          const { user } = useAuthStore.getState();
          
          if (!user?.id || !user?.salonId) {
            throw new Error('Usuario o salón no configurado');
          }

          const payload = {
            conversationId: targetConversationId,
            message: content,
            salonId: user.salonId,
            userId: user.id,
            attachments: attachments?.map(att => ({
              type: att.type,
              url: att.url,
              mimeType: att.mimeType,
            })) || [],
          };

          logger.info('Sending message to chat assistant', payload);

          const { data, error } = await supabase.functions.invoke('chat-assistant', {
            body: payload,
          });

          if (error) throw error;

          // Update user message with server data
          if (data.userMessage) {
            state.updateOptimisticMessage(userMessageId, {
              id: data.userMessage.id,
              synced: true,
              promptTokens: data.userMessage.prompt_tokens,
              totalTokens: data.userMessage.total_tokens,
              costUsd: data.userMessage.cost_usd,
            });
          }

          // Add assistant response
          if (data.assistantMessage) {
            const assistantMessage: ChatMessage = {
              id: data.assistantMessage.id,
              conversationId: targetConversationId,
              role: 'assistant',
              content: data.assistantMessage.content,
              promptTokens: data.assistantMessage.prompt_tokens,
              completionTokens: data.assistantMessage.completion_tokens,
              totalTokens: data.assistantMessage.total_tokens,
              costUsd: data.assistantMessage.cost_usd,
              metadata: data.assistantMessage.metadata,
              createdAt: new Date(data.assistantMessage.created_at),
              synced: true,
            };

            set(state => ({
              messages: {
                ...state.messages,
                [targetConversationId]: [
                  ...(state.messages[targetConversationId] || []),
                  assistantMessage,
                ],
              },
              conversations: state.conversations.map(conv =>
                conv.id === targetConversationId
                  ? { 
                      ...conv, 
                      lastMessage: assistantMessage.content.substring(0, 50) + (assistantMessage.content.length > 50 ? '...' : ''),
                      lastMessageAt: new Date()
                    }
                  : conv
              ),
            }));

            // Update conversation stats and title if it's the first real exchange
            if (data.conversationStats) {
              set(state => {
                const conversation = state.conversations.find(c => c.id === targetConversationId);
                const shouldUpdateTitle = conversation && 
                  conversation.title.startsWith('Nueva conversación') && 
                  data.conversationStats.message_count <= 2;
                
                return {
                  conversations: state.conversations.map(conv =>
                    conv.id === targetConversationId
                      ? {
                          ...conv,
                          title: shouldUpdateTitle ? state.generateSmartTitle(content) : conv.title,
                          messageCount: data.conversationStats.message_count,
                          totalTokensUsed: data.conversationStats.total_tokens_used,
                          totalCostUsd: data.conversationStats.total_cost_usd,
                          lastMessageAt: new Date(),
                          updatedAt: new Date(),
                          lastMessage: content.substring(0, 50) + (content.length > 50 ? '...' : ''),
                        }
                      : conv
                  ),
                };
              });
            }
          }

          set({ isSending: false });
        } catch (error) {
          logger.error('Error sending message', error);
          
          // Mark message as pending sync
          set(state => ({
            pendingSyncMessages: [
              ...state.pendingSyncMessages,
              state.messages[targetConversationId]?.find(m => m.localId === userMessageId)!,
            ],
            error: error as Error,
            isSending: false,
          }));
        }
      },

      // Add optimistic message
      addOptimisticMessage: (message) => {
        const localId = `local_${Date.now()}_${Math.random()}`;
        const optimisticMessage: ChatMessage = {
          ...message,
          id: localId,
          localId,
          createdAt: new Date(),
          synced: false,
        };

        set(state => ({
          messages: {
            ...state.messages,
            [message.conversationId]: [
              ...(state.messages[message.conversationId] || []),
              optimisticMessage,
            ],
          },
        }));

        return localId;
      },

      // Update optimistic message
      updateOptimisticMessage: (localId, updates) => {
        set(state => {
          const newMessages = { ...state.messages };
          
          for (const conversationId in newMessages) {
            newMessages[conversationId] = newMessages[conversationId].map(msg =>
              msg.localId === localId ? { ...msg, ...updates } : msg
            );
          }

          return { messages: newMessages };
        });
      },

      // Add context reference
      addContextReference: async (messageId, reference) => {
        try {
          const { error } = await supabase
            .from('chat_context_references')
            .insert({
              message_id: messageId,
              ...reference,
            });

          if (error) throw error;

          logger.info('Context reference added', { messageId, type: reference.referenceType });
        } catch (error) {
          logger.error('Error adding context reference', error);
        }
      },

      // Sync pending messages
      syncPendingMessages: async () => {
        const state = get();
        if (state.pendingSyncMessages.length === 0) return;

        logger.info('Syncing pending messages', { count: state.pendingSyncMessages.length });

        for (const message of state.pendingSyncMessages) {
          try {
            await state.sendMessage(message.content, message.conversationId);
            
            set(state => ({
              pendingSyncMessages: state.pendingSyncMessages.filter(m => m.localId !== message.localId),
            }));
          } catch (error) {
            logger.error('Error syncing message', { messageId: message.localId, error });
          }
        }

        set({ lastSync: new Date() });
      },

      // Upload chat image
      uploadChatImage: async (imageUri, messageId) => {
        try {
          logger.debug('Starting chat image upload', { imageUri: imageUri.substring(0, 50) + '...', messageId });
          
          const { user } = useAuthStore.getState();
          if (!user?.id || !user?.salonId) {
            throw new Error('Usuario o salón no configurado');
          }

          // Generate unique filename
          const timestamp = Date.now();
          const randomId = Math.random().toString(36).substring(7);
          const fileName = `chat/${user.salonId}/${timestamp}-${randomId}.jpg`;
          logger.debug('Generated filename', { fileName });

          // Get the image data
          let uploadData: ArrayBuffer;
          
          if (imageUri.startsWith('data:image')) {
            logger.debug('Processing base64 data URI');
            // Handle base64 data URI - extract just the base64 part
            const base64 = imageUri.split(',')[1];
            
            if (!base64) {
              throw new Error('Invalid base64 data URI - missing data after comma');
            }
            
            logger.debug('Base64 length', { length: base64.length });
            
            // Decode base64 for React Native
            const { decode } = await import('base64-arraybuffer');
            uploadData = decode(base64);
            
            logger.debug('Decoded ArrayBuffer', { byteLength: uploadData.byteLength });
          } else {
            // Handle regular URI - should not happen in our case
            // since ImageProcessor returns base64
            throw new Error(`Expected base64 data URI, got: ${imageUri.substring(0, 100)}`);
          }

          // Upload to Supabase Storage
          logger.debug('Starting Supabase Storage upload', { fileName, byteLength: uploadData.byteLength });
          
          const { data, error } = await supabase.storage
            .from('service-photos')
            .upload(fileName, uploadData, {
              contentType: 'image/jpeg',
              cacheControl: '3600',
            });

          if (error) {
            logger.error('Supabase Storage upload failed', error);
            throw error;
          }

          logger.debug('Supabase Storage upload successful', data);

          // Get public URL
          const { data: { publicUrl } } = supabase.storage
            .from('service-photos')
            .getPublicUrl(fileName);

          logger.debug('Generated public URL', { publicUrl });

          const attachment: ChatAttachment = {
            type: 'image',
            url: publicUrl,
            fileName: fileName.split('/').pop(),
            fileSize: uploadData.byteLength || 0,
            mimeType: 'image/jpeg',
            uploadStatus: 'completed',
          };

          logger.info('Chat image uploaded successfully', { fileName, messageId, fileSize: attachment.fileSize });
          return attachment;
        } catch (error) {
          logger.error('Error uploading chat image', error);
          set({ error: error as Error });
          return null;
        }
      },

      // Update attachment status
      updateAttachmentStatus: (messageId, attachmentIndex, status) => {
        set(state => {
          const newMessages = { ...state.messages };
          
          for (const conversationId in newMessages) {
            newMessages[conversationId] = newMessages[conversationId].map(msg => {
              if (msg.id === messageId || msg.localId === messageId) {
                const attachments = [...(msg.attachments || [])];
                if (attachments[attachmentIndex]) {
                  attachments[attachmentIndex] = {
                    ...attachments[attachmentIndex],
                    uploadStatus: status,
                  };
                }
                return { ...msg, attachments };
              }
              return msg;
            });
          }

          return { messages: newMessages };
        });
      },

      // Generate smart title from message content
      generateSmartTitle: (content: string) => {
        // Extract key terms for title
        const lowerContent = content.toLowerCase();
        
        // Check for specific keywords
        if (lowerContent.includes('stock') || lowerContent.includes('inventario')) {
          return 'Consulta de inventario';
        }
        if (lowerContent.includes('fórmula') || lowerContent.includes('formula')) {
          return 'Consulta sobre fórmula';
        }
        if (lowerContent.includes('cliente')) {
          return 'Consulta sobre cliente';
        }
        if (lowerContent.includes('rubio') || lowerContent.includes('rubia')) {
          return 'Fórmula rubio';
        }
        if (lowerContent.includes('cana') || lowerContent.includes('gris')) {
          return 'Cobertura de canas';
        }
        if (lowerContent.includes('rojo') || lowerContent.includes('cobrizo')) {
          return 'Tonos rojizos';
        }
        if (lowerContent.includes('mechas') || lowerContent.includes('balayage')) {
          return 'Técnica de mechas';
        }
        if (lowerContent.includes('corrección') || lowerContent.includes('corregir')) {
          return 'Corrección de color';
        }
        
        // Default: Use first 40 characters
        const cleanContent = content.replace(/\s+/g, ' ').trim();
        if (cleanContent.length > 40) {
          return cleanContent.substring(0, 40) + '...';
        }
        return cleanContent || 'Nueva conversación';
      },

      // Clear error
      clearError: () => set({ error: null }),

      // Reset store
      reset: () => set(initialState),
    }),
    {
      name: 'chat-store',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        activeConversationId: state.activeConversationId,
        pendingSyncMessages: state.pendingSyncMessages,
        lastSync: state.lastSync,
      }),
    }
  )
);
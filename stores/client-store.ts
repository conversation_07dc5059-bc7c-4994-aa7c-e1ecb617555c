import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { supabase, getCurrentSalonId } from "@/lib/supabase";
import { useSyncQueueStore, generateLocalId, isLocalId } from "./sync-queue-store";
import { Database } from "@/types/database";

type SupabaseClient = Database['public']['Tables']['clients']['Row'];
type SupabaseClientInsert = Database['public']['Tables']['clients']['Insert'];
type SupabaseClientUpdate = Database['public']['Tables']['clients']['Update'];

export interface Client {
  id: string;
  name: string;
  email: string;
  phone: string;
  since: string;
  lastVisit?: string;
  notes?: string;
  lastAnalysis?: {
    date: string;
    currentLevel: string;
    grayPercentage: string;
    hairTexture: string;
    confidence: number;
  };
  riskLevel?: string;
  allergies?: string[];
  preferences?: any[];
  // New safety and history fields
  knownAllergies?: string;
  pregnancyOrNursing?: boolean;
  sensitiveSkin?: boolean;
  chemicalTreatments?: {
    henna?: boolean;
    chemicalStraightening?: boolean;
    keratin?: boolean;
  };
  acceptsReminders?: boolean;
  preferredContact?: 'whatsapp' | 'sms' | 'call';
  // Sync status
  _syncStatus?: 'synced' | 'pending' | 'error';
  _localId?: string;
}

interface ClientStore {
  clients: Client[];
  isLoading: boolean;
  isInitialized: boolean;
  error: Error | null;
  
  // Actions
  loadClients: () => Promise<void>;
  addClient: (client: Omit<Client, 'id' | 'since'>) => Promise<void>;
  updateClient: (id: string, updates: Partial<Client>) => Promise<void>;
  deleteClient: (id: string) => Promise<void>;
  getClient: (id: string) => Client | undefined;
  syncWithSupabase: () => Promise<void>;
}

// Helper functions to convert between local and Supabase formats
function convertSupabaseToLocal(supabaseClient: SupabaseClient): Client {
  return {
    id: supabaseClient.id,
    name: supabaseClient.name,
    email: supabaseClient.email || '',
    phone: supabaseClient.phone || '',
    since: supabaseClient.created_at ? new Date(supabaseClient.created_at).toLocaleDateString('es-ES', { 
      day: 'numeric', 
      month: 'long', 
      year: 'numeric' 
    }) : '',
    notes: supabaseClient.notes || undefined,
    allergies: supabaseClient.allergies || [],
    knownAllergies: supabaseClient.allergies?.join(', ') || undefined,
    pregnancyOrNursing: supabaseClient.medical_conditions?.includes('pregnancy') || supabaseClient.medical_conditions?.includes('nursing'),
    sensitiveSkin: supabaseClient.medical_conditions?.includes('sensitive_skin'),
    chemicalTreatments: {
      henna: supabaseClient.medical_conditions?.includes('henna'),
      chemicalStraightening: supabaseClient.medical_conditions?.includes('chemical_straightening'),
      keratin: supabaseClient.medical_conditions?.includes('keratin'),
    },
    acceptsReminders: supabaseClient.tags?.includes('accepts_reminders'),
    preferredContact: supabaseClient.tags?.includes('whatsapp') ? 'whatsapp' : 
                     supabaseClient.tags?.includes('sms') ? 'sms' : 
                     supabaseClient.tags?.includes('call') ? 'call' : undefined,
    _syncStatus: 'synced',
  };
}

function convertLocalToSupabase(client: Client): SupabaseClientInsert {
  const medicalConditions = [];
  if (client.pregnancyOrNursing) medicalConditions.push('pregnancy_or_nursing');
  if (client.sensitiveSkin) medicalConditions.push('sensitive_skin');
  if (client.chemicalTreatments?.henna) medicalConditions.push('henna');
  if (client.chemicalTreatments?.chemicalStraightening) medicalConditions.push('chemical_straightening');
  if (client.chemicalTreatments?.keratin) medicalConditions.push('keratin');

  const tags = [];
  if (client.acceptsReminders) tags.push('accepts_reminders');
  if (client.preferredContact) tags.push(client.preferredContact);

  return {
    name: client.name,
    email: client.email || null,
    phone: client.phone || null,
    notes: client.notes || null,
    allergies: client.allergies || [],
    medical_conditions: medicalConditions.join(', ') || null,
    tags: tags,
    is_vip: client.preferences?.includes('vip') || false,
  };
}

export const useClientStore = create<ClientStore>()(
  persist(
    (set, get) => ({
      clients: [],
      isLoading: false,
      isInitialized: false,
      error: null,

      loadClients: async () => {
        set({ isLoading: true, error: null });
        
        try {
          const salonId = await getCurrentSalonId();
          if (!salonId) {
            // Silently return if no salon ID (user not authenticated yet)
            set({ isLoading: false });
            return;
          }

          const { data, error } = await supabase
            .from('clients')
            .select('*')
            .eq('salon_id', salonId)
            .order('created_at', { ascending: false });

          if (error) throw error;

          const localClients = data?.map(convertSupabaseToLocal) || [];
          
          set({ 
            clients: localClients, 
            isLoading: false,
            isInitialized: true 
          });
        } catch (error) {
          console.error('Error loading clients:', error);
          // Si hay error, mantener los datos locales
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error : new Error('Error al cargar clientes') 
          });
        }
      },

      addClient: async (clientData) => {
        const tempId = generateLocalId('client');
        const newClient: Client = {
          ...clientData,
          id: tempId,
          since: new Date().toLocaleDateString('es-ES', { 
            day: 'numeric', 
            month: 'long', 
            year: 'numeric' 
          }),
          _syncStatus: 'pending',
          _localId: tempId,
        };
        
        // 1. Actualizar UI inmediatamente (UI Optimista)
        set((state) => ({
          clients: [newClient, ...state.clients],
        }));

        // 2. Intentar sincronizar con Supabase
        const { isOnline } = useSyncQueueStore.getState();
        
        if (isOnline) {
          try {
            const salonId = await getCurrentSalonId();
            if (!salonId) throw new Error('Usuario no autenticado');

            const supabaseData = convertLocalToSupabase(clientData);
            const { data, error } = await supabase
              .from('clients')
              .insert({
                ...supabaseData,
                salon_id: salonId,
              })
              .select()
              .single();

            if (error) throw error;

            // 3. Actualizar con datos reales de Supabase
            set((state) => ({
              clients: state.clients.map((c) =>
                c.id === tempId ? convertSupabaseToLocal(data) : c
              ),
            }));
          } catch (error) {
            console.error('Error syncing client:', error);
            // 4. Si falla, agregar a la cola de sincronización
            useSyncQueueStore.getState().addToQueue({
              type: 'create',
              table: 'clients',
              data: {
                ...convertLocalToSupabase(clientData),
                _tempId: tempId,
              },
            });
            
            // Marcar como error
            set((state) => ({
              clients: state.clients.map((c) =>
                c.id === tempId ? { ...c, _syncStatus: 'error' } : c
              ),
            }));
          }
        } else {
          // Sin conexión, agregar a la cola
          useSyncQueueStore.getState().addToQueue({
            type: 'create',
            table: 'clients',
            data: {
              ...convertLocalToSupabase(clientData),
              _tempId: tempId,
            },
          });
        }
      },

      updateClient: async (id, updates) => {
        // 1. Actualizar UI inmediatamente
        set((state) => ({
          clients: state.clients.map((client) =>
            client.id === id ? { ...client, ...updates, _syncStatus: 'pending' } : client
          ),
        }));

        // 2. Intentar sincronizar
        const { isOnline } = useSyncQueueStore.getState();
        
        if (isOnline && !isLocalId(id)) {
          try {
            const client = get().clients.find(c => c.id === id);
            if (!client) return;

            const supabaseUpdates = convertLocalToSupabase({ ...client, ...updates });
            const { error } = await supabase
              .from('clients')
              .update(supabaseUpdates)
              .eq('id', id);

            if (error) throw error;

            // Marcar como sincronizado
            set((state) => ({
              clients: state.clients.map((c) =>
                c.id === id ? { ...c, _syncStatus: 'synced' } : c
              ),
            }));
          } catch (error) {
            console.error('Error updating client:', error);
            // Agregar a la cola
            useSyncQueueStore.getState().addToQueue({
              type: 'update',
              table: 'clients',
              data: { id, ...updates },
            });
            
            // Marcar como error
            set((state) => ({
              clients: state.clients.map((c) =>
                c.id === id ? { ...c, _syncStatus: 'error' } : c
              ),
            }));
          }
        } else {
          // Sin conexión o ID local, agregar a la cola
          useSyncQueueStore.getState().addToQueue({
            type: 'update',
            table: 'clients',
            data: { id, ...updates },
          });
        }
      },

      deleteClient: async (id) => {
        // 1. Actualizar UI inmediatamente
        set((state) => ({
          clients: state.clients.filter((client) => client.id !== id),
        }));

        // 2. Intentar sincronizar
        const { isOnline } = useSyncQueueStore.getState();
        
        if (isOnline && !isLocalId(id)) {
          try {
            const { error } = await supabase
              .from('clients')
              .delete()
              .eq('id', id);

            if (error) throw error;
          } catch (error) {
            console.error('Error deleting client:', error);
            // Agregar a la cola
            useSyncQueueStore.getState().addToQueue({
              type: 'delete',
              table: 'clients',
              data: { id },
            });
            
            // Restaurar el cliente con estado de error
            const client = get().clients.find(c => c.id === id);
            if (client) {
              set((state) => ({
                clients: [...state.clients, { ...client, _syncStatus: 'error' }],
              }));
            }
          }
        } else {
          // Sin conexión o ID local, agregar a la cola
          if (!isLocalId(id)) {
            useSyncQueueStore.getState().addToQueue({
              type: 'delete',
              table: 'clients',
              data: { id },
            });
          }
        }
      },

      getClient: (id) => {
        const state = get();
        return state.clients.find((client) => client.id === id);
      },

      syncWithSupabase: async () => {
        const { isInitialized } = get();
        if (!isInitialized) {
          await get().loadClients();
        }
      },
    }),
    {
      name: "client-storage",
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        clients: state.clients.filter(c => c._syncStatus !== 'synced'), // Solo persistir datos no sincronizados
      }),
    }
  )
);
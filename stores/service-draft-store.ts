import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface ServiceDraft {
  id: string;
  clientId: string;
  clientName: string;
  currentStep: number;
  lastSaved: Date;
  
  // Diagnosis data
  diagnosisData?: {
    method: string;
    hairPhotos: Array<{ id: string; angle: string; uri: string }>;
    hairThickness: string;
    hairDensity: string;
    overallTone: string;
    overallUndertone: string;
    lastChemicalProcessType: string;
    lastChemicalProcessDate: string;
    diagnosisNotes: string;
    zoneColorAnalysis: any;
    zonePhysicalAnalysis: any;
  };
  
  // Desired result data
  desiredData?: {
    method: string;
    desiredPhotos: Array<{ id: string; type: string; uri: string }>;
    desiredAnalysisResult: any;
    desiredNotes: string;
  };
  
  // Formulation data
  formulationData?: {
    selectedBrand: string;
    selectedLine: string;
    formula: string;
    isFormulaFromAI: boolean;
    formulaCost: any;
    viabilityAnalysis: any;
    stockValidation: any;
  };
  
  // Result data
  resultData?: {
    resultImage: string | null;
    clientSatisfaction: number;
    resultNotes: string;
  };
}

interface ServiceDraftStore {
  drafts: ServiceDraft[];
  
  // Actions
  saveDraft: (draft: Partial<ServiceDraft> & { clientId: string; clientName: string }) => void;
  getDraft: (clientId: string) => ServiceDraft | undefined;
  deleteDraft: (draftId: string) => void;
  getPendingDrafts: () => ServiceDraft[];
  clearAllDrafts: () => void;
  updateDraftStep: (clientId: string, step: number) => void;
  
  // Helper to generate draft from current service state
  createDraftFromServiceState: (
    clientId: string,
    clientName: string,
    currentStep: number,
    serviceState: any
  ) => ServiceDraft;
}

export const useServiceDraftStore = create<ServiceDraftStore>()(
  persist(
    (set, get) => ({
      drafts: [],
      
      saveDraft: (draft) => {
        const existingDraftIndex = get().drafts.findIndex(d => d.clientId === draft.clientId);
        
        const newDraft: ServiceDraft = {
          id: existingDraftIndex >= 0 ? get().drafts[existingDraftIndex].id : Date.now().toString(),
          clientId: draft.clientId,
          clientName: draft.clientName,
          currentStep: draft.currentStep || 0,
          lastSaved: new Date(),
          ...draft
        };
        
        if (existingDraftIndex >= 0) {
          // Update existing draft
          set(state => ({
            drafts: state.drafts.map((d, i) => i === existingDraftIndex ? newDraft : d)
          }));
        } else {
          // Add new draft
          set(state => ({
            drafts: [...state.drafts, newDraft]
          }));
        }
        
        console.log('[ServiceDraftStore] Draft saved for client:', draft.clientName);
      },
      
      getDraft: (clientId) => {
        return get().drafts.find(d => d.clientId === clientId);
      },
      
      deleteDraft: (draftId) => {
        set(state => ({
          drafts: state.drafts.filter(d => d.id !== draftId)
        }));
        console.log('[ServiceDraftStore] Draft deleted:', draftId);
      },
      
      getPendingDrafts: () => {
        return get().drafts;
      },
      
      clearAllDrafts: () => {
        set({ drafts: [] });
        console.log('[ServiceDraftStore] All drafts cleared');
      },
      
      updateDraftStep: (clientId, step) => {
        set(state => ({
          drafts: state.drafts.map(d => 
            d.clientId === clientId 
              ? { ...d, currentStep: step, lastSaved: new Date() }
              : d
          )
        }));
      },
      
      createDraftFromServiceState: (clientId, clientName, currentStep, serviceState) => {
        const {
          diagnosisMethod,
          hairPhotos,
          hairThickness,
          hairDensity,
          overallTone,
          overallUndertone,
          lastChemicalProcessType,
          lastChemicalProcessDate,
          diagnosisNotes,
          zoneColorAnalysis,
          zonePhysicalAnalysis,
          desiredMethod,
          desiredPhotos,
          desiredAnalysisResult,
          desiredNotes,
          selectedBrand,
          selectedLine,
          formula,
          isFormulaFromAI,
          formulaCost,
          viabilityAnalysis,
          stockValidation,
          resultImage,
          clientSatisfaction,
          resultNotes
        } = serviceState;
        
        const draft: ServiceDraft = {
          id: Date.now().toString(),
          clientId,
          clientName,
          currentStep,
          lastSaved: new Date()
        };
        
        // Add data based on current step
        if (currentStep >= 0) {
          draft.diagnosisData = {
            method: diagnosisMethod,
            hairPhotos: hairPhotos.map((p: any) => ({ 
              id: p.id, 
              angle: p.angle, 
              uri: p.uri 
            })),
            hairThickness,
            hairDensity,
            overallTone,
            overallUndertone,
            lastChemicalProcessType,
            lastChemicalProcessDate,
            diagnosisNotes,
            zoneColorAnalysis,
            zonePhysicalAnalysis
          };
        }
        
        if (currentStep >= 1) {
          draft.desiredData = {
            method: desiredMethod,
            desiredPhotos: desiredPhotos.map((p: any) => ({ 
              id: p.id, 
              type: p.type, 
              uri: p.uri 
            })),
            desiredAnalysisResult,
            desiredNotes
          };
        }
        
        if (currentStep >= 2) {
          draft.formulationData = {
            selectedBrand,
            selectedLine,
            formula,
            isFormulaFromAI,
            formulaCost,
            viabilityAnalysis,
            stockValidation
          };
        }
        
        if (currentStep >= 3) {
          draft.resultData = {
            resultImage,
            clientSatisfaction,
            resultNotes
          };
        }
        
        return draft;
      }
    }),
    {
      name: 'service-drafts-storage',
      storage: {
        getItem: async (name) => {
          const value = await AsyncStorage.getItem(name);
          return value ? JSON.parse(value) : null;
        },
        setItem: async (name, value) => {
          await AsyncStorage.setItem(name, JSON.stringify(value));
        },
        removeItem: async (name) => {
          await AsyncStorage.removeItem(name);
        },
      },
    }
  )
);
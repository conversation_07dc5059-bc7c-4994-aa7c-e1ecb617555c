import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import { supabase } from '@/lib/supabase';

export interface SyncOperation {
  id: string;
  type: 'create' | 'update' | 'delete';
  table: string;
  data: any;
  timestamp: number;
  retries: number;
  status: 'pending' | 'syncing' | 'failed';
  error?: string;
}

interface SyncQueueState {
  queue: SyncOperation[];
  isOnline: boolean;
  isSyncing: boolean;
  lastSyncTime: number | null;
  lastSyncError: string | null;
  
  // Actions
  addToQueue: (operation: Omit<SyncOperation, 'id' | 'timestamp' | 'retries' | 'status'>) => void;
  removeFromQueue: (id: string) => void;
  updateOperationStatus: (id: string, status: SyncOperation['status'], error?: string) => void;
  setOnlineStatus: (isOnline: boolean) => void;
  processSyncQueue: () => Promise<void>;
  clearQueue: () => void;
  initializeNetworkListener: () => () => void;
  getQueueStatus: () => { pending: number; syncing: number; failed: number };
}

const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1 second base delay

export const useSyncQueueStore = create<SyncQueueState>()(
  persist(
    (set, get) => ({
      queue: [],
      isOnline: true,
      isSyncing: false,
      lastSyncTime: null,
      lastSyncError: null,

      addToQueue: (operation) => {
        const newOperation: SyncOperation = {
          ...operation,
          id: `${operation.table}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: Date.now(),
          retries: 0,
          status: 'pending',
        };

        set((state) => ({
          queue: [...state.queue, newOperation],
        }));

        // Try to process immediately if online
        const { isOnline } = get();
        if (isOnline && !get().isSyncing) {
          get().processSyncQueue();
        }
      },

      removeFromQueue: (id) => {
        set((state) => ({
          queue: state.queue.filter((op) => op.id !== id),
        }));
      },

      updateOperationStatus: (id, status, error) => {
        set((state) => ({
          queue: state.queue.map((op) =>
            op.id === id ? { ...op, status, error } : op
          ),
        }));
      },

      setOnlineStatus: (isOnline) => {
        set({ isOnline });
        
        // Process queue when coming back online
        if (isOnline && !get().isSyncing && get().queue.length > 0) {
          get().processSyncQueue();
        }
      },

      processSyncQueue: async () => {
        const { queue, isOnline } = get();
        
        if (!isOnline || queue.length === 0) return;
        
        set({ isSyncing: true, lastSyncError: null });

        // Process operations in order
        for (const operation of queue.filter(op => op.status === 'pending' || op.status === 'failed')) {
          if (!get().isOnline) break;

          get().updateOperationStatus(operation.id, 'syncing');

          try {
            // Execute the operation based on type
            let result;
            
            switch (operation.type) {
              case 'create':
                result = await supabase
                  .from(operation.table)
                  .insert(operation.data)
                  .select()
                  .single();
                break;
                
              case 'update':
                result = await supabase
                  .from(operation.table)
                  .update(operation.data)
                  .eq('id', operation.data.id)
                  .select()
                  .single();
                break;
                
              case 'delete':
                result = await supabase
                  .from(operation.table)
                  .delete()
                  .eq('id', operation.data.id);
                break;
            }

            if (result?.error) {
              throw result.error;
            }

            // Success - remove from queue
            get().removeFromQueue(operation.id);
            
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            set({ lastSyncError: errorMessage });
            
            // Update retry count
            const updatedOperation = {
              ...operation,
              retries: operation.retries + 1,
              status: 'failed' as const,
              error: errorMessage,
            };

            if (updatedOperation.retries >= MAX_RETRIES) {
              // Max retries reached, keep in queue but stop retrying
              get().updateOperationStatus(operation.id, 'failed', errorMessage);
            } else {
              // Retry with exponential backoff
              const delay = RETRY_DELAY * Math.pow(2, updatedOperation.retries);
              setTimeout(() => {
                get().updateOperationStatus(operation.id, 'pending');
                get().processSyncQueue();
              }, delay);
            }
          }
        }

        set({ 
          isSyncing: false,
          lastSyncTime: Date.now()
        });
      },

      clearQueue: () => {
        set({ queue: [] });
      },

      initializeNetworkListener: () => {
        // Initial check
        NetInfo.fetch().then(state => {
          get().setOnlineStatus(state.isConnected ?? false);
        });

        // Listen for changes
        const unsubscribe = NetInfo.addEventListener(state => {
          get().setOnlineStatus(state.isConnected ?? false);
        });

        return unsubscribe;
      },

      getQueueStatus: () => {
        const { queue } = get();
        return {
          pending: queue.filter(op => op.status === 'pending').length,
          syncing: queue.filter(op => op.status === 'syncing').length,
          failed: queue.filter(op => op.status === 'failed').length,
        };
      },
    }),
    {
      name: 'sync-queue-storage',
      storage: {
        getItem: async (name) => {
          const value = await AsyncStorage.getItem(name);
          return value ? JSON.parse(value) : null;
        },
        setItem: async (name, value) => {
          await AsyncStorage.setItem(name, JSON.stringify(value));
        },
        removeItem: async (name) => {
          await AsyncStorage.removeItem(name);
        },
      },
    }
  )
);

// Helper to generate local IDs that can be synced
export function generateLocalId(prefix: string = 'local'): string {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Helper to check if an ID is local
export function isLocalId(id: string): boolean {
  return id.startsWith('local_');
}
# 🔧 Guía de Debugging para Errores de OpenAI

**Versión**: 2025-01-22  
**Edge Function**: v49 con logging mejorado  

---

## 🚨 Error: "Invalid response from OpenAI"

### Causas Comunes:

1. **API Key Inválido**
   - Edge Function logs: "OpenAI API key is invalid or missing"
   - Solución: Verificar variable de entorno `OPENAI_API_KEY`

2. **Límite de Rate Exceeded**
   - Edge Function logs: "OpenAI API rate limit exceeded" 
   - Solución: Esperar o aumentar limits en OpenAI

3. **Imagen Demasiado Grande**
   - Edge Function logs: "Image too large: X.X MB"
   - Solución: Comprimir imagen antes de enviar

4. **Formato de Imagen Inválido**
   - Edge Function logs: "Invalid base64 characters detected"
   - Solución: Verificar que sea base64 válido

5. **Respuesta de OpenAI Sin Contenido**
   - Edge Function logs: "OpenAI response message missing content - content blocked or filtered"
   - Solución: La imagen puede contener contenido bloqueado por OpenAI

## 🔍 Cómo Diagnosticar

### 1. Ver Logs de Edge Function
```bash
supabase functions logs salonier-assistant --tail
```

### 2. Logs Importantes que Buscar:

**✅ Flujo Normal:**
```
diagnoseImage called with: { hasImageUrl: true, hasImageBase64: false, ... }
Using public URL for diagnosis: https://...
Making request to OpenAI...
Request headers prepared, auth header length: 51
OpenAI response received, has choices: true
Validating OpenAI response structure...
OpenAI response structure validation passed
AI response preview: {"level": 7.5, "tone": "Rubio medio"...
Successfully parsed AI response
Analysis completed: { inputTokens: 1205, outputTokens: 95, ... }
```

**❌ Error de API Key:**
```
OpenAI API request failed: { status: 401, statusText: "Unauthorized" }
Error response: { error: { message: "Invalid API key" } }
```

**❌ Error de Contenido Bloqueado:**
```
OpenAI response structure validation passed
AI response preview: I'm sorry, but I cannot analyze...
Failed to parse AI response: SyntaxError: Unexpected token 'I'
```

### 3. Verificar en Cliente:
```javascript
// En desarrollo, ver logs detallados
console.log('Cliente recibió:', data);
```

## 🛠 Soluciones por Tipo de Error

### Error 401 - API Key
```bash
# Verificar variables de entorno
supabase secrets list
# Actualizar si es necesario
supabase secrets set OPENAI_API_KEY=sk-proj-...
```

### Error 429 - Rate Limit
- Implementar backoff más largo
- Considerar usar gpt-4o-mini en lugar de gpt-4o
- Verificar quota en OpenAI Dashboard

### Error de Parsing JSON
- La IA está devolviendo texto en lugar de JSON
- Usar prompt más específico (ya implementado en v49)
- Verificar que imagen sea clara y apropiada

### Error de Imagen
```bash
# Ver tamaño de imagen en logs
Base64 size: 2048576 bytes (2000.00 KB)
# Si es > 4MB, comprimir antes de enviar
```

## 📊 Métricas de Monitoreo

### Tokens y Costos:
```
Analysis completed: {
  inputTokens: 1205,
  outputTokens: 95, 
  totalTokens: 1300,
  costUsd: "$0.0033"
}
```

### Cache Hit/Miss:
```
Cache hit for diagnose_image    # ✅ Bueno
Cache miss or error: ...        # ⚠️ Incrementa costos
```

## 🚀 Testing Rápido

### Script para probar Edge Function:
```bash
curl -X POST https://ajsamgugqfbttkrlgvbr.supabase.co/functions/v1/salonier-assistant \\
  -H "Authorization: Bearer $(supabase auth token)" \\
  -H "Content-Type: application/json" \\
  -d '{
    "task": "diagnose_image",
    "payload": {
      "imageUrl": "https://example.com/test-image.jpg"
    }
  }'
```

### Respuesta Esperada:
```json
{
  "success": true,
  "data": {
    "level": 7.5,
    "tone": "Rubio medio",
    "reflect": "Cálido",
    // ... más campos
  }
}
```

## ⚡ Mejoras Implementadas en v49

1. **Logging Detallado**: Cada paso del proceso se registra
2. **Validación Mejorada**: Detecta problemas específicos de OpenAI  
3. **Manejo de Errores**: Mensajes más descriptivos
4. **Prompt Simplificado**: Previene respuestas "I'm sorry..."
5. **Debugging en Cliente**: Logs detallados del cliente

---

## 📞 Próximos Pasos si el Error Persiste

1. **Verificar imagen**: ¿Es clara? ¿Contiene rostros? ¿Es apropiada?
2. **Probar imagen diferente**: Usar imagen de prueba conocida
3. **Verificar quota OpenAI**: Dashboard de OpenAI
4. **Revisar logs completos**: 30 segundos antes y después del error
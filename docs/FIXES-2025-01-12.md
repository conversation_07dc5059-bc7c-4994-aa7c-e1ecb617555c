# Fixes Aplicados - 2025-01-12

## 1. Error: Missing 'signature' Column

**Problema**: La columna `signature` no existe en la tabla `client_consents`
**Solución**: Actualizado el código para usar `signature_data` en lugar de `signature`

### Archivos modificados:
- `stores/client-history-store.ts` - Cambiado todas las referencias de `consent.signature` a `consent.signature_data`
- `types/database.ts` - Actualizado los tipos para incluir todas las columnas faltantes

## 2. Error: Missing 'skip_safety' Column

**Problema**: El schema cache de Supabase no encontraba la columna `skip_safety`
**Solución**: Aplicada la migración pendiente `011_fix_client_consents_columns.sql`

```sql
ALTER TABLE client_consents 
ADD COLUMN IF NOT EXISTS skip_safety BOOLEAN DEFAULT false;
```

## 3. Warning: Invalid viewBox prop

**Problema**: Un SVG estaba usando `viewBox="0 0 100% 200"` con valores de porcentaje
**Solución**: Actualizado `SignatureCanvas.tsx` para asegurar que siempre se usen valores numéricos

```typescript
// Ensure width and height are numeric values for viewBox
const numericWidth = typeof width === 'number' ? width : defaultWidth;
const numericHeight = typeof height === 'number' ? height : defaultHeight;
```

## 4. Warning: ImagePicker.MediaTypeOptions deprecated

**Problema**: API deprecada de expo-image-picker
**Solución**: Cambiado `MediaTypeOptions` por `MediaType` en `app/service/new.tsx`

```typescript
// Antes:
mediaTypes: ImagePicker.MediaTypeOptions.Images,

// Después:
mediaTypes: ImagePicker.MediaType.Images,
```

## Scripts Creados

### `scripts/update-database-types.sh`
Script para regenerar los tipos TypeScript desde el schema de Supabase:
```bash
./scripts/update-database-types.sh
```

## Recomendaciones

1. **Después de migraciones**: Siempre ejecutar el script de actualización de tipos
2. **Schema cache**: Si hay errores de columnas faltantes después de migraciones, puede ser necesario reiniciar el servidor o esperar a que se actualice el cache
3. **Validación de tipos**: Siempre validar que los valores sean del tipo esperado antes de usarlos en propiedades que requieren tipos específicos (como viewBox)
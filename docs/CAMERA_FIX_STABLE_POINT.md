# Punto de Estabilidad - Cámara de Color Deseado Funcionando

## Fecha: 2025-07-03
## Estado: ✅ FUNCIONANDO CORRECTAMENTE

Este documento marca un punto de estabilidad donde la cámara para capturar fotos del color deseado funciona correctamente sin crashes.

## Resumen del Problema Resuelto

La aplicación se cerraba (crash) al intentar tomar fotos en la fase de "color deseado" del flujo de servicio. El problema NO ocurría en la fase de "color actual" (diagnóstico).

## Solución Implementada

### 1. Estados Separados
Se creó un estado dedicado `desiredPhotoAngle` para el flujo de color deseado, evitando interferencias con `currentPhotoAngle` usado en diagnóstico.

### 2. Delay C<PERSON>í<PERSON>o (SOLUCIÓN CLAVE)
Se agregó un delay de 100ms antes de abrir la cámara:

```typescript
// En handleDesiredPhotoAdd
setTimeout(() => {
  setShowGuidedCamera(true);
}, 100);
```

Este delay previene la race condition entre el Modal y la inicialización de CameraView.

## Archivos Clave Modificados

### `/app/service/new.tsx`
- Línea ~118: Agregado `const [desiredPhotoAngle, setDesiredPhotoAngle] = useState<PhotoAngle>(PhotoAngle.FRONT);`
- Línea ~1654-1659: Implementado setTimeout en handleDesiredPhotoAdd
- Línea ~2254-2256: GuidedCamera usa `desiredPhotoAngle` cuando `cameraMode === 'desired'`
- Línea ~1813: handleDesiredCameraCapture actualiza desiredPhotoAngle para el siguiente paso

### `/components/GuidedCamera.tsx`
- Línea ~21: Agregado valor por defecto `currentAngle = PhotoAngle.FRONT`
- Línea ~44-60: Mejorada validación de currentAngle

## Diferencias Clave entre Flujos

### Color Actual (Diagnóstico) - FUNCIONA SIN DELAY
```typescript
setCurrentPhotoAngle(nextGuide.angle);
setCameraMode('diagnosis');
setShowGuidedCamera(true); // Directo, sin delay
```

### Color Deseado - REQUIERE DELAY
```typescript
setCurrentDesiredCaptureStep(captureStep);
setDesiredPhotoAngle(desiredAngle);
setCameraMode('desired');

setTimeout(() => {
  setShowGuidedCamera(true); // Con delay de 100ms
}, 100);
```

## Por Qué Funciona

1. **Race Condition Resuelta**: El delay permite que React complete su ciclo de renderizado antes de inicializar el Modal con la cámara.

2. **Estados Independientes**: Cada flujo mantiene su propio estado de ángulo, evitando interferencias.

3. **Inicialización Secuencial**: Los componentes se inicializan en el orden correcto sin superposiciones.

## Verificación de Funcionamiento

Los siguientes logs confirman el funcionamiento correcto:

```
[DesiredPhoto] Iniciando proceso de cámara...
[DesiredPhoto] Permisos concedidos, configurando estado...
[getDesiredCaptureAngle] Called with step: overall
[getDesiredCaptureAngle] Mapped angle: front
[DesiredPhoto] Angle calculado: front
[GuidedCamera] Opening with angle: front ✅
[GuidedCamera] Taking picture for angle: front ✅
[DesiredPhoto] Captura recibida ✅
```

## Notas Importantes

1. **NO ELIMINAR EL DELAY**: El setTimeout es crítico para evitar el crash.

2. **NO MEZCLAR ESTADOS**: Mantener `currentPhotoAngle` solo para diagnóstico y `desiredPhotoAngle` solo para color deseado.

3. **PERMISOS**: Los permisos de cámara son manejados internamente por GuidedCamera, no hay necesidad de solicitarlos antes.

## Recomendaciones si Aparecen Problemas Futuros

1. Verificar que el delay sigue presente en handleDesiredPhotoAdd
2. Confirmar que los estados no se están mezclando entre flujos
3. Revisar que no se hayan agregado nuevas llamadas asíncronas antes de abrir la cámara
4. Comprobar que GuidedCamera mantiene su valor por defecto para currentAngle

## Commit de Referencia

Este punto de estabilidad está documentado en el commit con el mensaje:
"fix: Solución definitiva para crash de cámara en color deseado con delay de 100ms"
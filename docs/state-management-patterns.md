# State Management Patterns - Salonier

## Cuándo usar Stores de Zustand vs Estado Local

### ✅ Usa Stores de Zustand cuando:

1. **El estado es compartido entre múltiples componentes**
   ```typescript
   // ❌ Mal - Estado duplicado en componentes
   const ComponentA = () => {
     const [report, setReport] = useState(null);
     // ...
   };
   
   const ComponentB = () => {
     const [report, setReport] = useState(null);
     // ...
   };
   
   // ✅ Bien - Estado centralizado
   const ComponentA = () => {
     const { currentReport } = useInventoryStore();
     // ...
   };
   ```

2. **El estado persiste entre navegaciones**
   ```typescript
   // Store con persistencia
   export const useInventoryStore = create()(
     persist(
       (set, get) => ({
         currentReport: null,
         // ...
       }),
       {
         name: 'inventory-storage',
         storage: createJSONStorage(() => AsyncStorage),
       }
     )
   );
   ```

3. **El estado requiere sincronización con backend**
   ```typescript
   // Estado que se sincroniza con Supabase
   const { products, syncWithSupabase } = useInventoryStore();
   ```

4. **Operaciones asíncronas complejas**
   ```typescript
   // ❌ Mal - Lógica compleja en componente
   const Component = () => {
     const [isLoading, setIsLoading] = useState(false);
     const [data, setData] = useState(null);
     
     const loadData = async () => {
       setIsLoading(true);
       try {
         // Lógica compleja aquí
       } finally {
         setIsLoading(false);
       }
     };
   };
   
   // ✅ Bien - Lógica en el store
   const Component = () => {
     const { loadInventoryReport, isLoadingReport } = useInventoryStore();
   };
   ```

### 🔵 Usa Estado Local cuando:

1. **Estado UI temporal**
   ```typescript
   // Estado que solo afecta la presentación
   const [isExpanded, setIsExpanded] = useState(false);
   const [selectedTab, setSelectedTab] = useState(0);
   ```

2. **Formularios simples**
   ```typescript
   // Valores del formulario antes de enviar
   const [formData, setFormData] = useState({
     name: '',
     email: ''
   });
   ```

3. **Animaciones y transiciones**
   ```typescript
   const scaleAnim = useRef(new Animated.Value(0)).current;
   ```

## Ejemplos de Refactorización

### Antes (Estado Local)
```typescript
export default function InventoryReports() {
  const [report, setReport] = useState<InventoryReport | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    loadReport();
  }, []);
  
  const loadReport = () => {
    setIsLoading(true);
    try {
      const reportData = generateInventoryReport();
      setReport(reportData);
    } finally {
      setIsLoading(false);
    }
  };
}
```

### Después (Store Centralizado)
```typescript
// En el store
interface InventoryStore {
  currentReport: InventoryReport | null;
  isLoadingReport: boolean;
  loadInventoryReport: () => void;
  clearInventoryReport: () => void;
}

// En el componente
export default function InventoryReports() {
  const { 
    currentReport, 
    isLoadingReport, 
    loadInventoryReport,
    clearInventoryReport
  } = useInventoryStore();
  
  useEffect(() => {
    loadInventoryReport();
    return () => clearInventoryReport();
  }, []);
}
```

## Hooks Personalizados para Lógica Compartida

Cuando múltiples componentes necesitan la misma lógica pero con diferentes instancias:

```typescript
// hooks/usePhotoAnalysis.ts
export function usePhotoAnalysis({ currentLevel, onPhotoAnalyzed }) {
  const { 
    analyzeDesiredPhoto, 
    analyzingPhotoId 
  } = useAIAnalysisStore();
  
  const analyzePhoto = useCallback(async (photo) => {
    const result = await analyzeDesiredPhoto(photo.id, photo.uri, currentLevel);
    if (result && onPhotoAnalyzed) {
      onPhotoAnalyzed(photo.id, result);
    }
  }, [analyzeDesiredPhoto, currentLevel, onPhotoAnalyzed]);
  
  return { analyzePhoto, isAnalyzingPhoto };
}
```

## Checklist de Decisión

1. **¿El estado se usa en más de un componente?** → Store
2. **¿Necesita persistir entre recargas?** → Store
3. **¿Se sincroniza con el backend?** → Store
4. **¿Es complejo de calcular/cargar?** → Store
5. **¿Solo afecta la UI del componente actual?** → Estado Local
6. **¿Es temporal (formularios, modales)?** → Estado Local

## Anti-patrones a Evitar

❌ **No dupliques estado entre store y componente**
```typescript
// Mal
const { report } = useStore();
const [localReport, setLocalReport] = useState(report);
```

❌ **No hagas fetching directo en componentes**
```typescript
// Mal
useEffect(() => {
  fetch('/api/data').then(setData);
}, []);
```

❌ **No mezcles lógica de negocio con UI**
```typescript
// Mal
const Component = () => {
  const calculateComplexReport = () => {
    // 100 líneas de lógica aquí
  };
};
```
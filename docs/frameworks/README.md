# Documentación de Frameworks - Salonier

**Última actualización**: 17 de Julio 2025  
**Estado**: Context7 MCP conectado y funcionando ✅

## Frameworks del Proyecto

### 🚀 Frameworks Principales

1. **[React Native 0.79.1](./react-native/overview.md)**
   - Framework principal para desarrollo móvil
   - Soporte para iOS, Android y Web
   - Nueva arquitectura habilitada

2. **[Expo SDK 53.0.4](./expo/overview.md)**
   - Plataforma de desarrollo universal
   - Expo Router para navegación
   - Herramientas de desarrollo optimizadas

3. **[Supabase 2.50.5](./supabase/overview.md)**
   - Backend como servicio (BaaS)
   - Base de datos Postgres con RLS
   - Edge Functions para lógica serverless

4. **[Zustand 5.0.5](./zustand/overview.md)**
   - Gestión de estado ligera
   - API simple basada en hooks
   - Soporte completo para TypeScript

5. **[NativeWind 4.1.23](./nativewind/overview.md)**
   - TailwindCSS para React Native
   - Flujo de trabajo utility-first
   - Soporte para temas personalizados

### 🔧 Librerías Adicionales

6. **[React Navigation 7.1.6](./other-libraries/react-navigation.md)**
   - Navegación estándar para React Native
   - Stack, Tab y Drawer navigators
   - Deep linking y autenticación

7. **[React Native Reanimated 3.17.4](./other-libraries/react-native-reanimated.md)**
   - Animaciones performantes
   - Gestos nativos
   - Worklets para animaciones complejas

8. **[React Native Skia 2.0.0](./other-libraries/react-native-skia.md)**
   - Renderizado de gráficos 2D
   - Canvas personalizado
   - Efectos visuales avanzados

## Estado de Context7 MCP

### ✅ Context7 Conectado y Documentación Actualizada

Context7 MCP Server está correctamente conectado y la documentación ha sido actualizada con información específica y oficial.

**Problema resuelto**: 
- Error de módulo `zod` que causaba fallas en la conexión
- Solución: Limpiar caché de npm y npx

**Estado actual**:
- Context7 MCP: ✅ Conectado
- Supabase MCP: ✅ Conectado
- Logs disponibles en: `/Users/<USER>/Library/Caches/claude-cli-nodejs/...`

### ✅ Documentación Actualizada con Context7 y Configuración Completa

1. **React Native 0.79.1**: ✅ Actualizado con componentes oficiales y APIs específicas
2. **Expo SDK 53.0.4**: ✅ Actualizado con especificaciones técnicas y características únicas
3. **Supabase 2.50.5**: ✅ Actualizado con métodos de instalación y áreas funcionales
4. **NativeWind 4.1.23**: ✅ Completamente configurado y documentado
5. **Zustand 5.0.5**: ✅ Documentado y funcionando correctamente
6. **React Navigation 7.1.6**: ✅ Documentado y configurado

### 🎯 Mejoras Implementadas

- **Información específica por versión**: Datos oficiales de documentación
- **Ejemplos actualizados**: Código de ejemplo oficial y actualizado
- **Características únicas**: Funcionalidades específicas de cada versión
- **Configuración precisa**: Pasos de instalación y configuración oficiales
- **NativeWind completamente configurado**: Todos los archivos de configuración implementados

## Estructura de Archivos

```
docs/frameworks/
├── README.md                          # Este archivo
├── react-native/
│   ├── overview.md                   # Documentación general
│   ├── setup.md                      # Configuración específica
│   └── best-practices.md             # Mejores prácticas
├── expo/
│   ├── overview.md                   # Documentación general
│   ├── router.md                     # Expo Router específico
│   └── plugins.md                    # Plugins y configuración
├── supabase/
│   ├── overview.md                   # Documentación general
│   ├── auth.md                       # Autenticación
│   └── database.md                   # Base de datos y RLS
├── zustand/
│   ├── overview.md                   # Documentación general
│   └── patterns.md                   # Patrones de estado
├── nativewind/
│   ├── overview.md                   # Documentación general
│   └── setup.md                      # Configuración específica
└── other-libraries/
    ├── react-navigation.md           # Navegación
    ├── react-native-reanimated.md    # Animaciones
    └── react-native-skia.md          # Gráficos
```

## Configuración del Proyecto

### Versiones Específicas

```json
{
  "react-native": "0.79.1",
  "expo": "~53.0.4",
  "@supabase/supabase-js": "^2.50.5",
  "zustand": "^5.0.5",
  "nativewind": "^4.1.23",
  "@react-navigation/native": "^7.1.6",
  "react-native-reanimated": "~3.17.4",
  "@shopify/react-native-skia": "v2.0.0-next.4"
}
```

### Configuración Metro

```js
// metro.config.js
const { getDefaultConfig } = require('expo/metro-config');
const { withNativeWind } = require('nativewind/metro');

const config = getDefaultConfig(__dirname);
module.exports = withNativeWind(config, { input: './global.css' });
```

### Configuración Babel

```js
// babel.config.js
module.exports = function (api) {
  api.cache(true);
  return {
    presets: [
      ["babel-preset-expo", { jsxImportSource: "nativewind" }],
      "nativewind/babel",
    ],
  };
};
```

## Mejores Prácticas del Proyecto

### 1. Estructura de Componentes

```typescript
// Seguir patrón de componentes base
const BaseComponent = ({ className, children, ...props }) => {
  return (
    <View className={`base-styles ${className}`} {...props}>
      {children}
    </View>
  );
};
```

### 2. Gestión de Estado

```typescript
// Usar Zustand para estado global
const useAppStore = create<AppState>()((set) => ({
  // Estado global de la aplicación
}));
```

### 3. Navegación

```typescript
// Usar Expo Router para navegación
// Estructura basada en archivos en /app
```

### 4. Estilos

```typescript
// Usar NativeWind para estilos consistentes
<View className="flex-1 bg-white p-4">
  <Text className="text-lg font-semibold text-gray-900">
    Título
  </Text>
</View>
```

## Recursos Adicionales

- [React Native Docs](https://reactnative.dev/)
- [Expo Docs](https://docs.expo.dev/)
- [Supabase Docs](https://supabase.com/docs)
- [Zustand Docs](https://zustand.docs.pmnd.rs/)
- [NativeWind Docs](https://www.nativewind.dev/)
- [React Navigation Docs](https://reactnavigation.org/)

## Contacto y Soporte

Para más información sobre la implementación específica del proyecto, consultar:
- `planning.md` - Arquitectura y visión del proyecto
- `todo.md` - Tareas pendientes por hitos
- `CLAUDE.md` - Guía de trabajo para Claude
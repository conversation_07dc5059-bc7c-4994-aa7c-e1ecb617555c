# React Navigation 7.1.6 - Documentación

**Versión del proyecto**: 7.1.6  
**Fecha de actualización**: Enero 2025  
**Fuente**: [React Navigation](https://reactnavigation.org/)

## Descripción General

React Navigation es la librería estándar para navegación en React Native, proporcionando componentes y APIs para manejar la navegación entre pantallas de manera nativa en iOS y Android.

## Características Principales de React Navigation 7

### 🚀 Nuevas Características

1. **Static Configuration API**: Re-introducción de API de configuración estática
2. **Streamlined API**: API simplificada para evitar patrones que causan bugs
3. **Updated Drawer Navigator**: Actualización del navegador de cajón
4. **Header Back Button Changes**: Cambios en el botón de retroceso del header

### 📦 Instalación

```bash
npm install @react-navigation/native@^7.1.6
npm install react-native-screens react-native-safe-area-context

# Para Stack Navigator
npm install @react-navigation/stack

# Para Bottom Tabs Navigator
npm install @react-navigation/bottom-tabs

# Para Drawer Navigator
npm install @react-navigation/drawer react-native-reanimated react-native-gesture-handler
```

## Configuración Básica

### 1. Configuración Inicial

```typescript
// App.tsx
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';

const Stack = createStackNavigator();

export default function App() {
  return (
    <NavigationContainer>
      <Stack.Navigator>
        <Stack.Screen name="Home" component={HomeScreen} />
        <Stack.Screen name="Profile" component={ProfileScreen} />
      </Stack.Navigator>
    </NavigationContainer>
  );
}
```

### 2. Configuración con TypeScript

```typescript
// types/navigation.ts
export type RootStackParamList = {
  Home: undefined;
  Profile: { userId: string };
  Settings: { section: string };
};

declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}
```

## Stack Navigator

### 1. Navegación Básica

```typescript
import { createStackNavigator } from '@react-navigation/stack';
import type { NativeStackScreenProps } from '@react-navigation/native-stack';

type Props = NativeStackScreenProps<RootStackParamList, 'Home'>;

const Stack = createStackNavigator<RootStackParamList>();

function HomeScreen({ navigation }: Props) {
  return (
    <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
      <Text>Home Screen</Text>
      <Button
        title="Go to Profile"
        onPress={() => navigation.navigate('Profile', { userId: '123' })}
      />
    </View>
  );
}

function App() {
  return (
    <NavigationContainer>
      <Stack.Navigator>
        <Stack.Screen name="Home" component={HomeScreen} />
        <Stack.Screen name="Profile" component={ProfileScreen} />
      </Stack.Navigator>
    </NavigationContainer>
  );
}
```

### 2. Configuración de Opciones

```typescript
<Stack.Navigator
  screenOptions={{
    headerStyle: {
      backgroundColor: '#f4511e',
    },
    headerTintColor: '#fff',
    headerTitleStyle: {
      fontWeight: 'bold',
    },
  }}
>
  <Stack.Screen 
    name="Home" 
    component={HomeScreen}
    options={{
      title: 'My Home',
      headerRight: () => (
        <Button
          onPress={() => alert('This is a button!')}
          title="Info"
          color="#fff"
        />
      ),
    }}
  />
</Stack.Navigator>
```

## Bottom Tabs Navigator

### 1. Configuración Básica

```typescript
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import Ionicons from '@expo/vector-icons/Ionicons';

const Tab = createBottomTabNavigator();

function App() {
  return (
    <NavigationContainer>
      <Tab.Navigator
        screenOptions={({ route }) => ({
          tabBarIcon: ({ focused, color, size }) => {
            let iconName;

            if (route.name === 'Home') {
              iconName = focused ? 'home' : 'home-outline';
            } else if (route.name === 'Settings') {
              iconName = focused ? 'settings' : 'settings-outline';
            }

            return <Ionicons name={iconName} size={size} color={color} />;
          },
          tabBarActiveTintColor: 'tomato',
          tabBarInactiveTintColor: 'gray',
        })}
      >
        <Tab.Screen name="Home" component={HomeScreen} />
        <Tab.Screen name="Settings" component={SettingsScreen} />
      </Tab.Navigator>
    </NavigationContainer>
  );
}
```

### 2. Personalización Avanzada

```typescript
<Tab.Navigator
  screenOptions={{
    tabBarStyle: {
      backgroundColor: '#f8f9fa',
      borderTopWidth: 0,
      elevation: 0,
      shadowOpacity: 0,
      height: 60,
    },
    tabBarLabelStyle: {
      fontSize: 12,
      fontWeight: '600',
    },
    tabBarItemStyle: {
      paddingVertical: 5,
    },
  }}
>
  <Tab.Screen
    name="Home"
    component={HomeScreen}
    options={{
      tabBarBadge: 3,
      tabBarBadgeStyle: {
        backgroundColor: '#ff4444',
        color: '#fff',
      },
    }}
  />
</Tab.Navigator>
```

## Drawer Navigator

### 1. Configuración Básica

```typescript
import { createDrawerNavigator } from '@react-navigation/drawer';

const Drawer = createDrawerNavigator();

function App() {
  return (
    <NavigationContainer>
      <Drawer.Navigator>
        <Drawer.Screen name="Home" component={HomeScreen} />
        <Drawer.Screen name="Article" component={ArticleScreen} />
      </Drawer.Navigator>
    </NavigationContainer>
  );
}
```

### 2. Drawer Personalizado

```typescript
import { DrawerContentScrollView, DrawerItem } from '@react-navigation/drawer';

function CustomDrawerContent(props) {
  return (
    <DrawerContentScrollView {...props}>
      <DrawerItem
        label="Home"
        onPress={() => props.navigation.navigate('Home')}
      />
      <DrawerItem
        label="Profile"
        onPress={() => props.navigation.navigate('Profile')}
      />
    </DrawerContentScrollView>
  );
}

<Drawer.Navigator drawerContent={props => <CustomDrawerContent {...props} />}>
  <Drawer.Screen name="Home" component={HomeScreen} />
</Drawer.Navigator>
```

## Navegación Programática

### 1. Hooks de Navegación

```typescript
import { useNavigation } from '@react-navigation/native';

function ProfileButton() {
  const navigation = useNavigation();

  return (
    <Button
      title="Go to Profile"
      onPress={() => navigation.navigate('Profile')}
    />
  );
}
```

### 2. Parámetros de Navegación

```typescript
import { useRoute } from '@react-navigation/native';

function ProfileScreen() {
  const route = useRoute();
  const { userId } = route.params;

  return (
    <View>
      <Text>Profile ID: {userId}</Text>
    </View>
  );
}

// Navegar con parámetros
navigation.navigate('Profile', { userId: '123' });
```

### 3. Navegación Anidada

```typescript
function HomeStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen name="Home" component={HomeScreen} />
      <Stack.Screen name="Profile" component={ProfileScreen} />
    </Stack.Navigator>
  );
}

function App() {
  return (
    <NavigationContainer>
      <Tab.Navigator>
        <Tab.Screen name="HomeStack" component={HomeStack} />
        <Tab.Screen name="Settings" component={SettingsScreen} />
      </Tab.Navigator>
    </NavigationContainer>
  );
}
```

## Deep Linking

### 1. Configuración de Linking

```typescript
const linking = {
  prefixes: ['https://mychat.com', 'mychat://'],
  config: {
    screens: {
      Home: '',
      Profile: 'profile/:userId',
      Settings: 'settings',
    },
  },
};

<NavigationContainer linking={linking}>
  <Stack.Navigator>
    <Stack.Screen name="Home" component={HomeScreen} />
    <Stack.Screen name="Profile" component={ProfileScreen} />
  </Stack.Navigator>
</NavigationContainer>
```

### 2. URL Parsing

```typescript
import { useLinkTo } from '@react-navigation/native';

function LinkButton() {
  const linkTo = useLinkTo();

  return (
    <Button
      title="Go to Profile"
      onPress={() => linkTo('/profile/123')}
    />
  );
}
```

## Autenticación

### 1. Navegación Condicional

```typescript
import AsyncStorage from '@react-native-async-storage/async-storage';

function App() {
  const [isSignedIn, setIsSignedIn] = useState(false);

  useEffect(() => {
    // Restore token stored in `AsyncStorage`
    const bootstrapAsync = async () => {
      let userToken;
      try {
        userToken = await AsyncStorage.getItem('userToken');
      } catch (e) {
        // Restoring token failed
      }
      setIsSignedIn(userToken != null);
    };

    bootstrapAsync();
  }, []);

  return (
    <NavigationContainer>
      <Stack.Navigator>
        {isSignedIn ? (
          // Screens for signed in users
          <Stack.Group>
            <Stack.Screen name="Home" component={HomeScreen} />
            <Stack.Screen name="Profile" component={ProfileScreen} />
          </Stack.Group>
        ) : (
          // Auth screens
          <Stack.Group>
            <Stack.Screen name="SignIn" component={SignInScreen} />
            <Stack.Screen name="SignUp" component={SignUpScreen} />
          </Stack.Group>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
}
```

## Modales

### 1. Modal Stack

```typescript
<Stack.Navigator>
  <Stack.Group>
    <Stack.Screen name="Home" component={HomeScreen} />
    <Stack.Screen name="Profile" component={ProfileScreen} />
  </Stack.Group>
  <Stack.Group screenOptions={{ presentation: 'modal' }}>
    <Stack.Screen name="Settings" component={SettingsScreen} />
  </Stack.Group>
</Stack.Navigator>
```

### 2. Modal Personalizado

```typescript
<Stack.Screen 
  name="Modal" 
  component={ModalScreen}
  options={{
    presentation: 'modal',
    headerShown: false,
    cardStyle: { backgroundColor: 'rgba(0,0,0,0.5)' },
  }}
/>
```

## Transiciones y Animaciones

### 1. Transiciones Personalizadas

```typescript
<Stack.Navigator
  screenOptions={{
    cardStyleInterpolator: CardStyleInterpolators.forHorizontalIOS,
  }}
>
  <Stack.Screen name="Home" component={HomeScreen} />
</Stack.Navigator>
```

### 2. Animaciones de Gestos

```typescript
<Stack.Navigator
  screenOptions={{
    gestureEnabled: true,
    gestureDirection: 'horizontal',
  }}
>
  <Stack.Screen name="Home" component={HomeScreen} />
</Stack.Navigator>
```

## Mejores Prácticas

### 1. Estructura de Navegación

```typescript
// navigation/index.tsx
export function RootNavigator() {
  return (
    <NavigationContainer>
      <Stack.Navigator>
        <Stack.Screen name="Main" component={MainNavigator} />
        <Stack.Screen name="Auth" component={AuthNavigator} />
      </Stack.Navigator>
    </NavigationContainer>
  );
}

// navigation/MainNavigator.tsx
export function MainNavigator() {
  return (
    <Tab.Navigator>
      <Tab.Screen name="Home" component={HomeStack} />
      <Tab.Screen name="Profile" component={ProfileStack} />
    </Tab.Navigator>
  );
}
```

### 2. Tipado Estricto

```typescript
// types/navigation.ts
export type RootStackParamList = {
  Main: NavigatorScreenParams<MainTabParamList>;
  Auth: NavigatorScreenParams<AuthStackParamList>;
};

export type MainTabParamList = {
  Home: undefined;
  Profile: { userId: string };
};

// Usar en componentes
type Props = NativeStackScreenProps<RootStackParamList, 'Main'>;
```

### 3. Performance

```typescript
// Lazy loading de pantallas
const LazyScreen = lazy(() => import('./LazyScreen'));

<Stack.Screen 
  name="Lazy" 
  component={LazyScreen}
  options={{
    lazy: true,
  }}
/>
```

## Debugging

### 1. Navigation DevTools

```typescript
import { NavigationContainer } from '@react-navigation/native';

<NavigationContainer
  onStateChange={(state) => console.log('Navigation state changed', state)}
>
  {/* ... */}
</NavigationContainer>
```

### 2. Logging

```typescript
import { enableScreens } from 'react-native-screens';

enableScreens();

// En desarrollo
if (__DEV__) {
  import('./ReactotronConfig').then(() => console.log('Reactotron Configured'));
}
```

## Recursos Adicionales

- [React Navigation Docs](https://reactnavigation.org/)
- [React Navigation GitHub](https://github.com/react-navigation/react-navigation)
- [React Navigation Examples](https://github.com/react-navigation/react-navigation/tree/main/example)

## Compatibilidad

### Versiones Soportadas
- React Native: 0.70+
- React: 16.13+
- TypeScript: 4.1+

### Dependencias Requeridas
- react-native-screens
- react-native-safe-area-context
- react-native-gesture-handler (para Drawer)
- react-native-reanimated (para Drawer)
# Expo SDK 53 - Documentación General

**Versión del proyecto**: 53.0.4 (actualizada)  
**Fecha de actualización**: Enero 2025  
**Fuente**: [Expo Documentation](https://docs.expo.dev/)

## Descripción General

Expo es una plataforma de desarrollo universal que permite construir aplicaciones nativas para Android, iOS y web usando JavaScript y React. SDK 53 introduce mejoras significativas en performance y estabilidad.

## Características Principales de Expo SDK 53

### 🚀 Especificaciones Técnicas (Actualizado con Context7)

- **Expo SDK**: 53.0.4
- **React Native**: 0.79.1
- **React Native Web**: 0.20.0
- **Minimum Android**: 7+ (compileSdkVersion 35)
- **Minimum iOS**: 15.1+
- **Xcode**: 16.0+
- **Node.js**: 18+ (LTS)
- **Frecuencia de Release**: Tres veces por año

### 🔧 Instalación y Configuración (Actualizado con Context7)

```bash
# Instalar Expo CLI
npm install -g @expo/cli

# Crear nuevo proyecto (recomendado)
npx create-expo-app MyApp

# Para proyectos React Native existentes
npx install-expo-modules

# Instalar dependencias específicas de SDK 53
npx expo install expo-camera expo-contacts expo-sensors
```

### 📦 Importación de Módulos

```javascript
// Importación de módulos en SDK 53
import { CameraView } from 'expo-camera';
import * as Contacts from 'expo-contacts';
import { Gyroscope } from 'expo-sensors';
```

### 🌟 Características Únicas de SDK 53

- **Universal**: Funciona en cualquier aplicación React Native con el paquete `expo` instalado
- **Acceso a APIs**: Proporciona acceso a funcionalidades del dispositivo y sistema
- **Compatibilidad**: Amplio soporte para APIs de dispositivo (cámara, contactos, sensores)
- **Facilidad**: Instalación vía `npx expo install`

### 🚀 Opciones de Pre-Release

1. **Canary Releases**: Snapshot del branch main
   ```bash
   npm install expo@canary
   ```

2. **Beta Releases**: Versiones pre-release más estables
   ```bash
   npm install expo@beta
   ```

### 📦 Estructura de Proyecto

```
my-app/
├── app/                    # Expo Router (file-based routing)
│   ├── (tabs)/
│   │   ├── index.tsx
│   │   └── profile.tsx
│   ├── _layout.tsx
│   └── +not-found.tsx
├── components/
├── constants/
├── hooks/
├── assets/
├── app.json               # Configuración principal
├── metro.config.js        # Metro bundler config
└── package.json
```

## Expo Router (v5.0.3)

### Conceptos Clave

Expo Router utiliza **file-based routing** para crear navegación automática basada en la estructura de carpetas.

### 1. Configuración Básica

```jsx
// app.json
{
  "expo": {
    "name": "Mi App",
    "slug": "mi-app",
    "version": "1.0.0",
    "orientation": "portrait",
    "icon": "./assets/icon.png",
    "scheme": "myapp",
    "plugins": [
      [
        "expo-router",
        {
          "origin": "https://myapp.com/"
        }
      ]
    ],
    "experiments": {
      "typedRoutes": true
    }
  }
}
```

### 2. Layouts

```jsx
// app/_layout.tsx
import { Stack } from 'expo-router';

export default function RootLayout() {
  return (
    <Stack>
      <Stack.Screen name="index" options={{ title: 'Home' }} />
      <Stack.Screen name="profile" options={{ title: 'Profile' }} />
    </Stack>
  );
}
```

### 3. Navegación Programática

```jsx
import { router } from 'expo-router';

// Navegar a una pantalla
router.push('/profile');

// Navegar con parámetros
router.push('/user/123');

// Reemplazar pantalla actual
router.replace('/login');

// Ir atrás
router.back();
```

### 4. Parámetros de Ruta

```jsx
// app/user/[id].tsx
import { useLocalSearchParams } from 'expo-router';

export default function UserScreen() {
  const { id } = useLocalSearchParams();
  
  return (
    <View>
      <Text>User ID: {id}</Text>
    </View>
  );
}
```

### 5. Tabs Navigation

```jsx
// app/(tabs)/_layout.tsx
import { Tabs } from 'expo-router';

export default function TabLayout() {
  return (
    <Tabs>
      <Tabs.Screen
        name="index"
        options={{
          title: 'Home',
          tabBarIcon: ({ color }) => <HomeIcon color={color} />,
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ color }) => <ProfileIcon color={color} />,
        }}
      />
    </Tabs>
  );
}
```

## APIs Principales de Expo

### 1. Expo Camera

```jsx
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';

function CameraScreen() {
  const [facing, setFacing] = useState<CameraType>('back');
  const [permission, requestPermission] = useCameraPermissions();

  if (!permission) {
    return <View />;
  }

  if (!permission.granted) {
    return (
      <View style={styles.container}>
        <Button onPress={requestPermission} title="Grant permission" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <CameraView style={styles.camera} facing={facing}>
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.button} onPress={toggleCameraFacing}>
            <Text style={styles.text}>Flip Camera</Text>
          </TouchableOpacity>
        </View>
      </CameraView>
    </View>
  );
}
```

### 2. Expo Image

```jsx
import { Image } from 'expo-image';

<Image
  source={{ uri: 'https://example.com/image.jpg' }}
  style={{ width: 200, height: 200 }}
  placeholder={{ blurhash: 'L6PZfSi_.AyE_3t7t7R**0o#DgR4' }}
  contentFit="cover"
  transition={1000}
/>
```

### 3. Expo Image Picker

```jsx
import * as ImagePicker from 'expo-image-picker';

const pickImage = async () => {
  const result = await ImagePicker.launchImageLibraryAsync({
    mediaTypes: ImagePicker.MediaTypeOptions.Images,
    allowsEditing: true,
    aspect: [4, 3],
    quality: 1,
  });

  if (!result.canceled) {
    setImage(result.assets[0].uri);
  }
};
```

### 4. Expo Constants

```jsx
import Constants from 'expo-constants';

const { statusBarHeight } = Constants;
const { deviceName } = Constants.deviceName;
const { version } = Constants.expoConfig;
```

### 5. Expo Linear Gradient

```jsx
import { LinearGradient } from 'expo-linear-gradient';

<LinearGradient
  colors={['#4c669f', '#3b5998', '#192f6a']}
  style={styles.gradient}
>
  <Text style={styles.text}>Hello Gradient!</Text>
</LinearGradient>
```

## Plugins Principales

### 1. Configuración de Plugins

```json
{
  "expo": {
    "plugins": [
      [
        "expo-router",
        {
          "origin": "https://myapp.com/"
        }
      ],
      [
        "expo-image-picker",
        {
          "photosPermission": "Custom permission message"
        }
      ],
      [
        "expo-camera",
        {
          "cameraPermission": "Allow app to access camera"
        }
      ]
    ]
  }
}
```

### 2. Plugin de Notificaciones

```jsx
import * as Notifications from 'expo-notifications';

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: false,
    shouldSetBadge: false,
  }),
});
```

## Desarrollo y Build

### 1. Comandos de Desarrollo

```bash
# Iniciar development server
npx expo start

# Desarrollo con clearing cache
npx expo start --clear

# Desarrollo con túnel
npx expo start --tunnel

# Desarrollo específico para plataforma
npx expo start --ios
npx expo start --android
npx expo start --web
```

### 2. Build Process

```bash
# Build para desarrollo
npx expo build:android --type app-bundle
npx expo build:ios --type archive

# Build con EAS
npx expo install @expo/cli
npx eas build --platform android
npx eas build --platform ios
```

### 3. Configuración de Metro

```js
// metro.config.js
const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Personalizar configuración
config.resolver.assetExts.push('cjs');

module.exports = config;
```

## Mejores Prácticas

### 1. Estructura de Navegación

```
app/
├── (auth)/
│   ├── login.tsx
│   └── register.tsx
├── (tabs)/
│   ├── index.tsx
│   ├── profile.tsx
│   └── settings.tsx
├── modal.tsx
├── _layout.tsx
└── +not-found.tsx
```

### 2. Gestión de Estado

```jsx
// Con Context API
const AppContext = createContext();

export function AppProvider({ children }) {
  const [user, setUser] = useState(null);
  
  return (
    <AppContext.Provider value={{ user, setUser }}>
      {children}
    </AppContext.Provider>
  );
}
```

### 3. Manejo de Errores

```jsx
// app/_layout.tsx
import { ErrorBoundary } from 'expo-router';

export default function RootLayout() {
  return (
    <ErrorBoundary>
      <Stack>
        <Stack.Screen name="index" />
      </Stack>
    </ErrorBoundary>
  );
}
```

### 4. Performance Optimization

```jsx
// Lazy loading de componentes
const LazyComponent = lazy(() => import('./LazyComponent'));

// Optimización de imágenes
<Image
  source={{ uri: imageUrl }}
  style={{ width: 200, height: 200 }}
  placeholder={{ blurhash }}
  cachePolicy="memory-disk"
/>
```

## Debugging

### 1. Expo DevTools

```bash
# Abrir DevTools
npx expo start --devtools
```

### 2. Remote Debugging

```jsx
// Habilitar debugging remoto
import { LogBox } from 'react-native';

LogBox.ignoreLogs(['Warning: ...']);
```

### 3. Logging

```jsx
import { logger } from 'expo-dev-client';

logger.info('Debug message');
logger.error('Error message');
```

## Recursos Adicionales

- [Expo Documentation](https://docs.expo.dev/)
- [Expo Router Docs](https://docs.expo.dev/router/)
- [Expo GitHub](https://github.com/expo/expo)
- [Expo Community](https://github.com/expo/expo/discussions)

## Compatibilidad y Limitaciones

### Versiones Soportadas
- React: 19.0.0
- React Native: 0.79.1
- TypeScript: 5.8.3

### Consideraciones Importantes
- New Architecture habilitada (`"newArchEnabled": true`)
- Soporte para Typed Routes experimental
- Compatibilidad con Metro ES Module resolution
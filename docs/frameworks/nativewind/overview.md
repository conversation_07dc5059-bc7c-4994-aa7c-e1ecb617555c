# NativeWind 4.1.23 - Documentación General

**Versión del proyecto**: 4.1.23 (configurada completamente)  
**Fecha de actualización**: Enero 2025  
**Fuente**: [NativeWind Documentation](https://www.nativewind.dev/)

## Descripción General

NativeWind permite usar TailwindCSS en aplicaciones React Native, trayendo el flujo de trabajo utility-first que los desarrolladores web conocen y aman al desarrollo móvil nativo.

## Características Principales

### 🎨 Características Clave

- **Tailwind CSS**: Todas las utilidades de TailwindCSS disponibles en React Native
- **Utility-first**: Flujo de trabajo basado en clases de utilidad
- **Performance**: Optimizado para aplicaciones móviles nativas
- **TypeScript**: Soporte completo para TypeScript
- **Cross-platform**: Funciona en iOS, Android y Web
- **Hot Reload**: Compatibilidad completa con Fast Refresh

### 🚀 Ventajas

- **Familiar**: Misma sintaxis que TailwindCSS web
- **Productive**: Desarrollo más rápido con utilidades predefinidas
- **Consistent**: Diseño consistente entre plataformas
- **Maintainable**: CSS-in-JS sin el overhead
- **Responsive**: Diseño responsivo nativo

## Instalación

### 1. Instalar Dependencias (Actualizado con Context7)

```bash
# Instalar NativeWind y dependencias requeridas
npm install nativewind tailwindcss react-native-reanimated react-native-safe-area-context

# Versiones específicas para compatibilidad
npm install nativewind@^4.1.23 react-native-reanimated@~3.17.4 react-native-safe-area-context@5.4.0

# Instalar dependencias de desarrollo
npm install --dev tailwindcss@^3.4.17 prettier-plugin-tailwindcss@^0.5.11
```

### 🎯 Características Principales de NativeWind 4.1.23

- **Integración TailwindCSS**: Estilo cross-platform para móviles
- **Compatibilidad**: Funciona con Expo y React Native framework-less
- **Diseño Responsivo**: Soporte para responsive design
- **Dark Mode**: Integración completa con modo oscuro
- **Styling específico de plataforma**: Estilos por plataforma
- **Utilidades extensivas**: Soporte completo para clases utility de Tailwind

### 2. Configuración de TailwindCSS (Implementada en el proyecto)

```bash
# Crear archivo de configuración
npx tailwindcss init
```

```javascript
// tailwind.config.js - Configuración específica del proyecto Salonier
module.exports = {
  content: [
    "./App.{js,jsx,ts,tsx}",
    "./app/**/*.{js,jsx,ts,tsx}",
    "./components/**/*.{js,jsx,ts,tsx}",
    "./screens/**/*.{js,jsx,ts,tsx}",
    "./stores/**/*.{js,jsx,ts,tsx}",
    "./lib/**/*.{js,jsx,ts,tsx}",
  ],
  presets: [require("nativewind/preset")],
  theme: {
    extend: {
      colors: {
        // Colores específicos del proyecto Salonier
        primary: { /* sistema completo de colores */ },
        secondary: { /* colores secundarios */ },
        success: { /* colores de éxito */ },
        warning: { /* colores de advertencia */ },
        error: { /* colores de error */ },
        gray: { /* escala de grises personalizada */ },
      },
      fontFamily: {
        'sans': ['System'],
        'mono': ['Menlo', 'Monaco', 'monospace'],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      borderRadius: {
        '4xl': '2rem',
      },
      boxShadow: {
        'card': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        'card-hover': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      },
    },
  },
  plugins: [],
};
```

### 3. Configuración de Metro (Implementada en el proyecto)

```javascript
// metro.config.js - Configuración específica del proyecto Salonier
const { getDefaultConfig } = require('expo/metro-config');
const { withNativeWind } = require('nativewind/metro');

const config = getDefaultConfig(__dirname);

// Configuración para mayor estabilidad
config.server = {
  ...config.server,
  enhanceMiddleware: (middleware) => {
    return (req, res, next) => {
      // Sin timeout para evitar desconexiones
      req.setTimeout(0);
      return middleware(req, res, next);
    };
  },
};

// Resetear caché más agresivamente si hay problemas
config.resetCache = true;

// Configuración de NativeWind
module.exports = withNativeWind(config, {
  input: './global.css',
});
```

### 4. Configuración de Babel (Implementada en el proyecto)

```javascript
// babel.config.js - Configuración específica del proyecto Salonier
module.exports = function (api) {
  api.cache(true);
  return {
    presets: [
      ["babel-preset-expo", { jsxImportSource: "nativewind" }],
      "nativewind/babel",
    ],
    plugins: [
      // React Native Reanimated plugin must be last
      "react-native-reanimated/plugin",
    ],
  };
};
```

### 5. Crear CSS Global (Implementado en el proyecto)

```css
/* global.css - Archivo implementado en el proyecto */
@tailwind base;
@tailwind components;
@tailwind utilities;
```

**Nota**: Este archivo se importa en `app/_layout.tsx` con:
```typescript
import "../global.css";
```

### 6. Configuración de TypeScript (Implementado en el proyecto)

```typescript
// nativewind-env.d.ts - Archivo implementado en el proyecto
/// <reference types="nativewind/types" />
```

**Nota**: Este archivo proporciona definiciones de tipos para NativeWind y permite que TypeScript reconozca las propiedades `className` en componentes React Native.

## Uso Básico

### 1. Importar CSS Global (Actualizado con Context7)

```typescript
// App.tsx - Ejemplo oficial de NativeWind 4.1.23
import "./global.css";
import { Text, View } from "react-native";

export default function App() {
  return (
    <View className="flex-1 items-center justify-center bg-white">
      <Text className="text-xl font-bold text-blue-500">
        Welcome to Nativewind!
      </Text>
    </View>
  );
}
```

### 🎯 Configuración Paso a Paso

1. **Instalar dependencias** via npm
2. **Inicializar configuración Tailwind**
3. **Agregar preset Babel**
4. **Configurar Metro bundler**
5. **Importar archivo CSS**
6. **Modificar `app.json`**

### 🌟 Características Únicas Confirmadas

- **Experiencia de estilo nativa**: Aprovecha la familiaridad de las clases utility de Tailwind CSS
- **Integración TypeScript**: Definiciones de tipos completas
- **Responsive design**: Soporte para breakpoints
- **Dark mode**: Integración con sistemas de temas
- **Platform-specific styling**: Estilos específicos para iOS/Android

### 2. Componentes Básicos

```typescript
import { View, Text, TouchableOpacity } from "react-native";

export function Button({ title, onPress }) {
  return (
    <TouchableOpacity 
      className="bg-blue-500 px-6 py-3 rounded-lg active:bg-blue-600"
      onPress={onPress}
    >
      <Text className="text-white font-semibold text-center">
        {title}
      </Text>
    </TouchableOpacity>
  );
}

export function Card({ children }) {
  return (
    <View className="bg-white rounded-xl p-4 shadow-lg mx-4 my-2">
      {children}
    </View>
  );
}
```

### 3. Layout Flexbox

```typescript
function Layout() {
  return (
    <View className="flex-1 bg-gray-100">
      {/* Header */}
      <View className="bg-white shadow-sm px-4 py-3 pt-12">
        <Text className="text-lg font-bold text-gray-800">Header</Text>
      </View>
      
      {/* Main Content */}
      <View className="flex-1 p-4">
        <View className="flex-row justify-between items-center mb-4">
          <Text className="text-xl font-semibold">Title</Text>
          <TouchableOpacity className="bg-blue-500 px-3 py-1 rounded">
            <Text className="text-white text-sm">Action</Text>
          </TouchableOpacity>
        </View>
        
        <View className="bg-white rounded-lg p-4 flex-1">
          <Text className="text-gray-600">Content goes here...</Text>
        </View>
      </View>
      
      {/* Footer */}
      <View className="bg-white border-t border-gray-200 p-4">
        <Text className="text-center text-gray-500">Footer</Text>
      </View>
    </View>
  );
}
```

## Clases de Utilidad Principales

### 1. Layout

```typescript
// Flexbox
<View className="flex-1 flex-row justify-center items-center">
<View className="flex-col space-y-4">
<View className="w-full h-32">
<View className="absolute top-0 left-0 right-0">

// Positioning
<View className="relative">
<View className="absolute top-2 right-2">
<View className="z-10">
```

### 2. Spacing

```typescript
// Margin
<View className="m-4 mx-2 my-6 mt-8 mr-3 mb-4 ml-1">

// Padding
<View className="p-4 px-2 py-6 pt-8 pr-3 pb-4 pl-1">

// Space between children
<View className="space-y-4"> // Espacio vertical
<View className="space-x-2"> // Espacio horizontal
```

### 3. Colors

```typescript
// Background
<View className="bg-red-500 bg-blue-100 bg-gray-900 bg-white">

// Text
<Text className="text-red-500 text-blue-100 text-gray-900 text-white">

// Border
<View className="border-red-500 border-2">
```

### 4. Typography

```typescript
// Size
<Text className="text-xs text-sm text-base text-lg text-xl text-2xl">

// Weight
<Text className="font-thin font-normal font-medium font-semibold font-bold">

// Alignment
<Text className="text-left text-center text-right text-justify">
```

### 5. Borders

```typescript
// Border width
<View className="border border-2 border-4">

// Border radius
<View className="rounded rounded-lg rounded-xl rounded-full">

// Border style
<View className="border-solid border-dashed border-dotted">
```

## Responsive Design

### 1. Breakpoints

```typescript
// Mobile first approach
<View className="w-full md:w-1/2 lg:w-1/3">
<Text className="text-sm md:text-base lg:text-lg">

// Responsive padding
<View className="p-2 md:p-4 lg:p-6">
```

### 2. Platform Specific

```typescript
// Platform-specific styles
<View className="ios:bg-blue-500 android:bg-green-500">
<Text className="ios:text-white android:text-black">
```

## Temas Personalizados

### 1. Extender Configuración

```javascript
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          900: '#1e3a8a',
        },
        secondary: '#f59e0b',
      },
      fontFamily: {
        'custom': ['CustomFont'],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
    },
  },
};
```

### 2. Usar Colores Personalizados

```typescript
<View className="bg-primary-500">
<Text className="text-secondary font-custom">
<View className="p-18 m-88">
```

## Animaciones con Reanimated

### 1. Animaciones Básicas

```typescript
import Animated, { useSharedValue, useAnimatedStyle, withSpring } from 'react-native-reanimated';

function AnimatedBox() {
  const offset = useSharedValue(0);
  
  const animatedStyles = useAnimatedStyle(() => ({
    transform: [{ translateX: offset.value }],
  }));
  
  return (
    <Animated.View 
      className="w-20 h-20 bg-blue-500 rounded-lg"
      style={animatedStyles}
    />
  );
}
```

### 2. Gestos con Reanimated

```typescript
import { Gesture, GestureDetector } from 'react-native-gesture-handler';

function DraggableBox() {
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  
  const gesture = Gesture.Pan()
    .onUpdate((event) => {
      translateX.value = event.translationX;
      translateY.value = event.translationY;
    });
  
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
    ],
  }));
  
  return (
    <GestureDetector gesture={gesture}>
      <Animated.View 
        className="w-20 h-20 bg-red-500 rounded-lg"
        style={animatedStyle}
      />
    </GestureDetector>
  );
}
```

## Componentes Avanzados

### 1. Modal

```typescript
import { Modal } from 'react-native';

function CustomModal({ visible, onClose, children }) {
  return (
    <Modal visible={visible} transparent animationType="fade">
      <View className="flex-1 bg-black/50 justify-center items-center">
        <View className="bg-white rounded-xl p-6 mx-4 w-full max-w-sm">
          <TouchableOpacity 
            className="absolute top-2 right-2 p-2"
            onPress={onClose}
          >
            <Text className="text-gray-500 text-lg">×</Text>
          </TouchableOpacity>
          {children}
        </View>
      </View>
    </Modal>
  );
}
```

### 2. List Items

```typescript
function ListItem({ title, subtitle, onPress }) {
  return (
    <TouchableOpacity 
      className="flex-row items-center p-4 border-b border-gray-200 active:bg-gray-50"
      onPress={onPress}
    >
      <View className="w-12 h-12 bg-gray-300 rounded-full mr-3" />
      <View className="flex-1">
        <Text className="font-medium text-gray-900">{title}</Text>
        <Text className="text-sm text-gray-500">{subtitle}</Text>
      </View>
      <Text className="text-gray-400">›</Text>
    </TouchableOpacity>
  );
}
```

### 3. Form Components

```typescript
function Input({ label, placeholder, value, onChangeText, error }) {
  return (
    <View className="mb-4">
      <Text className="text-sm font-medium text-gray-700 mb-1">{label}</Text>
      <TextInput
        className={`border rounded-lg px-3 py-2 ${
          error ? 'border-red-500' : 'border-gray-300'
        }`}
        placeholder={placeholder}
        value={value}
        onChangeText={onChangeText}
      />
      {error && (
        <Text className="text-red-500 text-sm mt-1">{error}</Text>
      )}
    </View>
  );
}
```

## Dark Mode

### 1. Configuración

```javascript
// tailwind.config.js
module.exports = {
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        primary: {
          light: '#3b82f6',
          dark: '#1d4ed8',
        },
      },
    },
  },
};
```

### 2. Usar Dark Mode

```typescript
import { useColorScheme } from 'react-native';

function ThemedComponent() {
  const colorScheme = useColorScheme();
  
  return (
    <View className={`
      flex-1 p-4 
      ${colorScheme === 'dark' ? 'bg-gray-900' : 'bg-white'}
    `}>
      <Text className={`
        text-lg font-semibold
        ${colorScheme === 'dark' ? 'text-white' : 'text-gray-900'}
      `}>
        Hello World
      </Text>
    </View>
  );
}
```

## Debugging

### 1. Verificar Instalación

```bash
# Limpiar cache
npx react-native start --reset-cache

# Verificar dependencias
npm list nativewind tailwindcss
```

### 2. Troubleshooting Común

```typescript
// Verificar si las clases se aplican
import { NativeWindStyleSheet } from 'nativewind';

// En desarrollo, mostrar estilos aplicados
console.log(NativeWindStyleSheet.getSheet());
```

### 3. Logs de Desarrollo

```typescript
// Verificar si NativeWind está funcionando
import { nativewind } from 'nativewind';

console.log('NativeWind version:', nativewind.version);
```

## Mejores Prácticas

### 1. Organización de Estilos

```typescript
// Crear constantes para estilos comunes
const styles = {
  container: 'flex-1 bg-gray-100 p-4',
  card: 'bg-white rounded-xl p-4 shadow-lg',
  title: 'text-xl font-bold text-gray-900',
  button: 'bg-blue-500 px-6 py-3 rounded-lg active:bg-blue-600',
  buttonText: 'text-white font-semibold text-center',
};

// Usar en componentes
<View className={styles.container}>
  <View className={styles.card}>
    <Text className={styles.title}>Title</Text>
  </View>
</View>
```

### 2. Componentes Reutilizables

```typescript
// Crear componentes base
const Button = ({ variant = 'primary', size = 'md', children, ...props }) => {
  const baseClasses = 'rounded-lg font-semibold text-center';
  
  const variants = {
    primary: 'bg-blue-500 text-white active:bg-blue-600',
    secondary: 'bg-gray-200 text-gray-800 active:bg-gray-300',
  };
  
  const sizes = {
    sm: 'px-3 py-1 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
  };
  
  return (
    <TouchableOpacity 
      className={`${baseClasses} ${variants[variant]} ${sizes[size]}`}
      {...props}
    >
      <Text className="text-inherit">{children}</Text>
    </TouchableOpacity>
  );
};
```

### 3. Performance

```typescript
// Evitar recalcular clases en cada render
const buttonClasses = useMemo(() => 
  `bg-blue-500 px-6 py-3 rounded-lg ${isActive ? 'opacity-50' : ''}`,
  [isActive]
);

<TouchableOpacity className={buttonClasses}>
```

## Recursos Adicionales

- [NativeWind Documentation](https://www.nativewind.dev/)
- [TailwindCSS Documentation](https://tailwindcss.com/)
- [NativeWind GitHub](https://github.com/nativewind/nativewind)
- [React Native Reanimated](https://docs.swmansion.com/react-native-reanimated/)

## Compatibilidad

### Versiones Soportadas
- React Native: 0.70+
- Expo: SDK 48+
- TailwindCSS: 3.4+
- React Native Reanimated: 3.17+

### Limitaciones
- Algunas propiedades CSS no están disponibles en React Native
- Pseudo-clases limitadas comparado con web
- Animaciones requieren React Native Reanimated
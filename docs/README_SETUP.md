# 🚀 Guía de Configuración Inicial - Salonier

## Pasos para empezar

### 1. Instalar dependencias
```bash
npm install
```

### 2. Configurar Supabase (IMPORTANTE)

⚠️ **La aplicación no funcionará sin configurar Supabase primero**

#### Paso 1: Crear cuenta en Supabase
1. Ve a https://app.supabase.com
2. Crea una cuenta gratuita (con GitHub o email)

#### Paso 2: Crear un nuevo proyecto
1. Click en "New Project"
2. Completa los campos:
   - **Organization**: Selecciona o crea una
   - **Project name**: "Salonier" (o el nombre que prefieras)
   - **Database Password**: Genera una contraseña segura (guárdala!)
   - **Region**: Elige la más cercana a tu ubicación
3. Click en "Create new project"
4. Espera ~2 minutos mientras se crea el proyecto

#### Paso 3: Obtener las credenciales
1. Una vez creado, ve a **Settings** → **API**
2. Copia estos dos valores:
   - **Project URL**: Algo como `https://abcdefgh.supabase.co`
   - **anon public key**: Una clave larga que empieza con `eyJ...`

#### Paso 4: Configurar el archivo .env.local
1. Abre el archivo `.env.local` en la raíz del proyecto
2. Reemplaza los valores de ejemplo con los tuyos:
```env
EXPO_PUBLIC_SUPABASE_URL="https://tu-proyecto-real.supabase.co"
EXPO_PUBLIC_SUPABASE_ANON_KEY="eyJ...tu-clave-real-completa..."
```
3. Guarda el archivo

### 3. Ejecutar las migraciones de base de datos

Una vez configurado Supabase, necesitas crear las tablas:

1. Ve a tu proyecto en Supabase
2. Click en **SQL Editor** en el menú lateral
3. Click en "New query"
4. Copia y pega TODO el contenido del archivo `supabase/migrations/20250107120000_initial_schema.sql`
5. Click en "Run" (o Ctrl+Enter)
6. Deberías ver "Success. No rows returned"

### 4. Iniciar la aplicación

```bash
# Para iOS
npm run ios

# Para Android
npm run android

# Para Web
npm run web
```

## Solución de problemas comunes

### Error: "Invalid API key"
- No has configurado el archivo `.local`
- La clave anon está mal copiada (verifica que esté completa)
- Olvidaste reiniciar el servidor después de configurar

### Error: "Failed to fetch"
- Verifica tu conexión a internet
- Confirma que la URL del proyecto es correcta
- El proyecto de Supabase puede estar pausado (ve al dashboard)

### Error: "No salon ID found"
- Esto es normal antes de iniciar sesión, ignóralo
- Si persiste después del login, puede ser un problema de permisos

### La app se queda en blanco
- Revisa la consola del navegador (F12)
- Asegúrate de que las migraciones se ejecutaron correctamente
- Verifica que el archivo `.env.local` existe y tiene los valores correctos

### Error de registro con recursión infinita
- Asegúrate de ejecutar TODAS las migraciones, especialmente la 010
- Las políticas RLS han sido corregidas en las últimas migraciones

## Verificar que todo funciona

1. Abre la app
2. Click en "Regístrate" 
3. Crea una cuenta nueva
4. Si puedes crear la cuenta y entrar, ¡todo está funcionando! 🎉

## Próximos pasos

- Explora la aplicación
- Configura tu salón en Settings
- Añade algunos productos de inventario
- Crea tu primer cliente
- ¡Empieza a formular!

## ¿Necesitas ayuda?

- Revisa la documentación de Supabase: https://supabase.com/docs
- Busca en los issues del proyecto
- Contacta al equipo de desarrollo
# Guía de Solución de Problemas - Salonier

## Error: "Edge Function returned a non-2xx status code"

Este error indica que la Edge Function está devolviendo un código de error. Aquí están los pasos para solucionarlo:

### 1. Verificar los logs detallados

Con los cambios implementados, ahora deberías ver información más detallada en la consola:
- Tipo de error específico (FunctionsHttpError, FunctionsRelayError, etc.)
- Mensaje de error real del servidor
- Tamaño de la imagen enviada
- Si hay un problema con la función, el mensaje exacto aparecerá en los logs

### 2. Códigos de error comunes y soluciones:

#### **401 - No autorizado**
- **Causa**: Token de autenticación inválido o expirado
- **Solución**: 
  - Cerrar sesión y volver a iniciar
  - Verificar que el usuario esté correctamente autenticado

#### **413 - Payload demasiado grande**
- **Causa**: La imagen es demasiado grande (>6MB)
- **Solución**: 
  - La app ahora intenta automáticamente con una imagen más pequeña
  - Si persiste, toma fotos con menor resolución

#### **500 - Error del servidor**
- **Causa**: Error en la Edge Function
- **Posibles problemas**:
  - OPENAI_API_KEY no configurada
  - Error en el código de la función
  - Límite de rate de OpenAI alcanzado
- **Solución**:
  1. Verificar en Supabase Dashboard → Edge Functions → salonier-assistant → Logs
  2. Confirmar que OPENAI_API_KEY está configurada en Secrets
  3. Ejecutar el script de prueba: `./scripts/test-edge-function.sh`

#### **503 - Servicio no disponible**
- **Causa**: La Edge Function no está desplegada
- **Solución**: 
  - Desplegar la función: `./scripts/deploy-edge-function.sh`

### 3. Probar la Edge Function directamente

```bash
# Configurar variables de entorno
export EXPO_PUBLIC_SUPABASE_URL="tu-url-de-supabase"
export EXPO_PUBLIC_SUPABASE_ANON_KEY="tu-anon-key"

# Ejecutar prueba
./scripts/test-edge-function.sh
```

### 4. Verificar tamaños de imagen

La app ahora usa estos tamaños:
- **Diagnóstico**: 600px ancho, compresión 0.6
- **Color deseado**: 500px ancho, compresión 0.5

Si aún hay problemas, se intenta automáticamente con:
- **Diagnóstico retry**: 500px ancho, compresión 0.5
- **Color deseado retry**: 400px ancho, compresión 0.4

### 5. Revisar logs de Supabase

1. Ir a Supabase Dashboard
2. Edge Functions → salonier-assistant
3. Click en "Logs"
4. Buscar errores recientes

### 6. Verificar configuración de CORS

Si ves errores de CORS, verifica que la Edge Function tenga los headers correctos (ya están configurados en el código).

## Error: "Could not find the 'consent_data' column"

**Solución**: Ejecutar la migración SQL en Supabase:
```sql
-- En SQL Editor de Supabase
ALTER TABLE client_consents 
ADD COLUMN IF NOT EXISTS consent_data JSONB,
ADD COLUMN IF NOT EXISTS safety_checklist TEXT[],
ADD COLUMN IF NOT EXISTS skip_safety BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS user_agent TEXT;
```

## Auto-guardado y Drafts

### ¿Cómo funciona el auto-guardado?
El sistema guarda automáticamente tu progreso en:
- Después de cada análisis con IA
- Al capturar o seleccionar fotos
- Al generar fórmulas
- Al cambiar de marca/línea
- Al navegar entre pasos

### ¿Dónde se guardan los drafts?
Los drafts se guardan localmente en AsyncStorage del dispositivo. No se sincronizan con Supabase para mayor privacidad.

### ¿Cómo retomar un servicio interrumpido?
1. Ve a la pantalla principal
2. Aparecerá una notificación "📋 Tienes X servicios sin terminar"
3. Toca "Ver" para ver la lista
4. Selecciona el servicio que quieres continuar
5. El servicio se restaurará en el paso donde lo dejaste

### ¿Cuándo se eliminan los drafts?
- Automáticamente al completar un servicio exitosamente
- Manualmente al tocar "Descartar" en un draft pendiente
- Los drafts NO tienen fecha de expiración

## Validación de Stock

### ¿Cuándo se valida el stock?
- Automáticamente después de generar cada fórmula (solo en nivel "Control Total")
- Manualmente con el botón "Verificar Disponibilidad"

### ¿Qué hacer si no hay stock suficiente?
1. Aparecerá un banner rojo con los productos faltantes
2. Activa el switch "Proceder sin descontar del inventario"
3. El servicio continuará sin afectar tu inventario
4. Recuerda reabastecer los productos faltantes

### El stock no se actualiza después del servicio
- Verifica que NO tengas activado "Proceder sin descontar del inventario"
- Confirma que estás en nivel "Control Total" en configuración
- Revisa que la fórmula se haya parseado correctamente

## Otros problemas comunes

### La app no conecta con Supabase
- Verificar `.env.local` tiene las credenciales correctas
- Confirmar que el proyecto de Supabase no está pausado
- Revisar conexión a internet

### Las imágenes no se analizan
- Verificar permisos de cámara en el dispositivo
- Intentar con fotos más pequeñas/simples primero
- Revisar que OpenAI API key sea válida y tenga créditos

---

Si ninguna de estas soluciones funciona, revisa los issues en GitHub o contacta soporte.
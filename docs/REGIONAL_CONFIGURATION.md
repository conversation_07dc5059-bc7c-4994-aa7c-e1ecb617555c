# Sistema de Configuración Regional - Salonier Copilot

## 📋 Resumen

El sistema de configuración regional permite a Salonier Copilot adaptarse completamente a las necesidades específicas de cada país y región, incluyendo sistemas de medición, monedas, idiomas, y regulaciones locales del sector de coloración capilar.

## 🌍 Países Soportados

### Europa
- 🇪🇸 **España** - Sistema métrico, EUR, Español
- 🇫🇷 **Francia** - Sistema métrico, EUR, Francés
- 🇩🇪 **Alemania** - Sistema métrico, EUR, Alemán
- 🇮🇹 **Italia** - Sistema métrico, EUR, Italiano
- 🇬🇧 **Reino Unido** - Sistema métrico, GBP, Inglés
- 🇵🇹 **Portugal** - Sistema métrico, EUR, Portugués

### América del Norte
- 🇺🇸 **Estados Unidos** - Sistema imperial, USD, Inglés
- 🇨🇦 **Canadá** - Sistema mixto, CAD, Inglés/Francés
- 🇲🇽 **México** - Sistema métrico, MXN, Español

### América del Sur
- 🇧🇷 **Brasil** - Sistema métrico, BRL, Portugués
- 🇦🇷 **Argentina** - Sistema métrico, ARS, Español
- 🇨🇱 **Chile** - Sistema métrico, CLP, Español
- 🇨🇴 **Colombia** - Sistema métrico, COP, Español

## 🔧 Arquitectura del Sistema

### Tipos y Estructuras

```typescript
// types/regional.ts
export interface RegionalConfig {
  countryCode: CountryCode;
  countryName: string;
  region: Region;
  measurementSystem: MeasurementSystem;
  currency: Currency;
  currencySymbol: string;
  language: Language;
  dateFormat: string;
  timeFormat: '12h' | '24h';
  decimalSeparator: string;
  thousandsSeparator: string;
  volumeUnit: string;
  weightUnit: string;
  developerTerminology: string;
  colorTerminology: string;
  requiresAllergyTest: boolean;
  maxDeveloperVolume: number;
}
```

### Componentes Clave

1. **SearchableList** (`components/SearchableList.tsx`)
   - Componente reutilizable para listas con búsqueda
   - Soporta agrupación por regiones
   - Renderizado personalizable

2. **MockFormulationService** (`services/mockFormulationService.ts`)
   - Genera fórmulas adaptadas regionalmente
   - Convierte unidades automáticamente
   - Localización de instrucciones en 4 idiomas

3. **Salon Config Store** (`stores/salon-config-store.ts`)
   - Gestiona la configuración regional
   - Helpers para formateo de moneda y unidades
   - Persistencia con AsyncStorage

## 💱 Sistema Multi-Moneda

### Monedas Soportadas (26 total)

**Monedas Populares:**
- EUR (€) - Euro
- USD ($) - Dólar estadounidense
- GBP (£) - Libra esterlina
- CAD (C$) - Dólar canadiense
- AUD (A$) - Dólar australiano

**América Latina:**
- MXN - Peso mexicano
- BRL - Real brasileño
- ARS - Peso argentino
- CLP - Peso chileno
- COP - Peso colombiano
- PEN - Sol peruano

### Configuración de Moneda

```typescript
// En settings.tsx
const handleCurrencyChange = (currency: Currency) => {
  updatePricing({ 
    currency: currency, 
    currencySymbol: getCurrencySymbol(currency) 
  });
};
```

## 📏 Sistemas de Medición

### Sistema Métrico
- **Volumen**: ml (mililitros)
- **Peso**: g (gramos)
- **Países**: Todos excepto EEUU

### Sistema Imperial
- **Volumen**: fl oz (onzas fluidas)
- **Peso**: oz (onzas)
- **Países**: Estados Unidos

### Conversiones Automáticas

```typescript
// Conversión de volumen
const mlToFlOz = (ml: number) => ml * 0.033814;
const flOzToMl = (flOz: number) => flOz * 29.5735;

// Conversión de peso
const gToOz = (g: number) => g * 0.035274;
const ozToG = (oz: number) => oz * 28.3495;
```

## 🌐 Localización de Fórmulas

### Idiomas Soportados
- 🇪🇸 Español
- 🇬🇧 Inglés
- 🇵🇹 Portugués
- 🇫🇷 Francés

### Ejemplo de Fórmula Localizada

**Español (Sistema Métrico):**
```
Wella Illumina Color:

Fórmula para Balayage:
- Illumina Color 8/1 (30g)
- Illumina Color 8/69 (10g)
- Oxidante 30 vol (40ml) para medios/puntas
- Oxidante 20 vol (40ml) para raíces

Tiempo de proceso: 35 minutos
```

**Inglés (Sistema Imperial):**
```
Wella Illumina Color:

Formula for Balayage:
- Illumina Color 8/1 (1.1 oz)
- Illumina Color 8/69 (0.4 oz)
- Developer 30 vol (1.4 fl oz) for mids/ends
- Developer 20 vol (1.4 fl oz) for roots

Processing time: 35 minutes
```

## 🏛️ Regulaciones por País

### Límites de Oxidante
- **Europa**: Máximo 40 vol (12%)
- **Brasil**: Máximo 30 vol (9%)
- **Algunos países**: Requieren prueba de alergia obligatoria

### Implementación

```typescript
if (regionalConfig.maxDeveloperVolume < 40 && levelDifference > 3) {
  formula += `\n\n⚠️ Limitación legal de oxidante: ${regionalConfig.maxDeveloperVolume}vol`;
}
```

## 🔧 Cómo Agregar un Nuevo País

1. **Actualizar tipos** en `types/regional.ts`:
```typescript
export type CountryCode = 'ES' | 'FR' | ... | 'NL'; // Agregar código
```

2. **Agregar datos del país** en `mocks/countries-data.ts`:
```typescript
{
  code: 'NL',
  name: 'Netherlands',
  localName: 'Nederland',
  flag: '🇳🇱',
  config: {
    countryCode: 'NL',
    countryName: 'Países Bajos',
    region: 'Europe',
    measurementSystem: 'metric',
    currency: 'EUR',
    currencySymbol: '€',
    language: 'nl',
    // ... resto de configuración
  }
}
```

3. **Agregar traducciones** en `MockFormulationService`:
```typescript
nl: {
  allergyTestRequired: 'Allergietest vereist 48u voor',
  processingTime: 'Verwerkingstijd',
  // ... más traducciones
}
```

## 📱 Uso en la Aplicación

### Cambiar País
1. Ir a **Configuración** → **Configuración Regional**
2. Tocar en **País**
3. Buscar o seleccionar el país deseado
4. El sistema ajustará automáticamente:
   - Sistema de medición
   - Moneda predeterminada
   - Formato de fecha/hora
   - Idioma de fórmulas

### Cambiar Moneda Independientemente
1. En **Configuración Regional**
2. Tocar en **Moneda**
3. Seleccionar cualquier moneda disponible

## 🚀 Mejoras Futuras

1. **Más países**: Agregar soporte para más países europeos y asiáticos
2. **Más idiomas**: Alemán, italiano, holandés
3. **Conversión de precios**: Tasas de cambio en tiempo real
4. **Regulaciones dinámicas**: Actualización automática de regulaciones locales
5. **Formatos de dirección**: Adaptados por país para clientes

## 🧪 Testing

### Casos de Prueba Recomendados

1. **Cambio de país**:
   - Verificar que las unidades cambien correctamente
   - Confirmar que las fórmulas se generen en el idioma correcto
   - Validar formato de moneda

2. **Sistema de medición**:
   - Cambiar entre métrico e imperial
   - Verificar conversiones en fórmulas existentes
   - Confirmar precisión de cálculos

3. **Generación de fórmulas**:
   - Generar fórmula en diferentes configuraciones regionales
   - Verificar terminología correcta
   - Confirmar cumplimiento de regulaciones

## 📞 Soporte

Para agregar soporte para tu país o región, contacta al equipo de desarrollo o abre un issue en el repositorio.
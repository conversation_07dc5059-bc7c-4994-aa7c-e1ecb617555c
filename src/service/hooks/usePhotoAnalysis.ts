import { useState, useCallback } from 'react';
import { Alert } from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { useAIAnalysisStore } from '@/stores/ai-analysis-store';
import { usePhotoCaptureStore } from '@/stores/photo-capture-store';
import { CapturedPhoto, PhotoAngle, PhotoQuality, PHOTO_GUIDES } from '@/types/photo-capture';
import { DesiredPhoto, DesiredPhotoType } from '@/types/desired-photo';
import { DesiredColorAnalysisResult, DesiredCaptureStep } from '@/types/desired-analysis';
import { HairZone } from '@/types/hair-diagnosis';
import { uploadAndAnonymizeImage } from '@/utils/secure-image-upload';

interface ImageQualityCheck {
  isGoodLighting: boolean;
  isInFocus: boolean;
  hasGoodResolution: boolean;
  overallScore: number;
}

export const usePhotoAnalysis = () => {
  const [currentPhotoAngle, setCurrentPhotoAngle] = useState<PhotoAngle>(PhotoAngle.CROWN);
  const [imageQuality, setImageQuality] = useState<ImageQualityCheck | null>(null);
  const [isImageProcessing, setIsImageProcessing] = useState(false);
  const [isAnalyzingDesired, setIsAnalyzingDesired] = useState(false);

  // AI Analysis Store
  const { 
    isAnalyzing, 
    analysisResult, 
    privacyMode, 
    analyzeImage, 
    setPrivacyMode,
    clearAnalysis 
  } = useAIAnalysisStore();

  // Photo Capture Store
  const { pendingPhoto, clearPendingPhoto } = usePhotoCaptureStore();

  const performImageQualityCheck = useCallback((imageUri: string): ImageQualityCheck => {
    // Simulate on-device image quality analysis
    // In real implementation, this would use actual image processing
    const mockQuality: ImageQualityCheck = {
      isGoodLighting: Math.random() > 0.3,
      isInFocus: Math.random() > 0.2,
      hasGoodResolution: Math.random() > 0.1,
      overallScore: Math.random() * 100
    };
    
    return mockQuality;
  }, []);

  const takePhoto = useCallback(async (setImageFunc: (uri: string) => void) => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permisos necesarios', 'Se necesitan permisos de cámara para tomar fotos');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        quality: 0.5,
        allowsEditing: false,
        base64: false,
      });

      if (!result.canceled && result.assets[0]) {
        setImageFunc(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error al tomar foto:', error);
      Alert.alert(
        'Error', 
        'No se pudo acceder a la cámara. Por favor, usa la galería mientras resolvemos este problema.',
        [
          { text: 'Usar Galería', onPress: () => pickImage(setImageFunc) },
          { text: 'Cancelar' }
        ]
      );
    }
  }, []);

  const pickImage = useCallback(async (setImageFunc: (uri: string) => void) => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        quality: 0.5,
        allowsEditing: false,
        base64: false,
      });

      if (!result.canceled && result.assets[0]) {
        setImageFunc(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error al seleccionar imagen:', error);
      Alert.alert('Error', 'No se pudo acceder a la galería');
    }
  }, []);

  const pickMultipleImages = useCallback(async (
    hairPhotos: CapturedPhoto[], 
    setHairPhotos: (photos: CapturedPhoto[]) => void,
    onSave?: () => void
  ) => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsMultipleSelection: true,
        quality: 0.5,
        selectionLimit: 5 - hairPhotos.length,
        base64: false,
      });

      if (!result.canceled && result.assets) {
        const newPhotos: CapturedPhoto[] = result.assets.map((asset, index) => {
          const quality = performImageQualityCheck(asset.uri);
          const qualityObj: PhotoQuality = {
            lighting: quality.isGoodLighting ? 'good' : quality.overallScore > 60 ? 'fair' : 'poor',
            focus: quality.isInFocus ? 'good' : 'fair',
            stability: 'good',
            overall: quality.overallScore
          };
          
          // Try to guess angle based on existing photos
          const capturedAngles = hairPhotos.map(p => p.angle);
          const availableGuides = PHOTO_GUIDES.filter(g => !capturedAngles.includes(g.angle));
          const angle = availableGuides[index]?.angle || PhotoAngle.CROWN;
          
          return {
            id: Date.now().toString() + index,
            uri: asset.uri,
            angle,
            quality: qualityObj,
            timestamp: new Date()
          };
        });
        
        setHairPhotos([...hairPhotos, ...newPhotos]);
        
        if (newPhotos.some(p => p.quality.overall < 60)) {
          // Return warning message instead of showing toast directly
          return "⚠️ Algunas fotos tienen calidad mejorable";
        }
        
        onSave?.();
        return "✅ Fotos agregadas correctamente";
      }
    } catch (error) {
      console.error('Error al seleccionar múltiples imágenes:', error);
      Alert.alert('Error', 'No se pudieron cargar las imágenes');
    }
  }, [performImageQualityCheck]);

  const handleCameraCapture = useCallback((
    uri: string, 
    angle: PhotoAngle, 
    quality: PhotoQuality,
    hairPhotos: CapturedPhoto[],
    setHairPhotos: (photos: CapturedPhoto[]) => void,
    onSave?: () => void
  ) => {
    const newPhoto: CapturedPhoto = {
      id: Date.now().toString(),
      uri,
      angle,
      quality,
      timestamp: new Date()
    };
    
    // Replace photo if same angle exists
    const filtered = hairPhotos.filter(p => p.angle !== angle);
    setHairPhotos([...filtered, newPhoto]);
    
    onSave?.();
    
    // Check if there are more required photos to capture
    const capturedAngles = [...hairPhotos.map(p => p.angle), angle];
    const nextRequiredGuide = PHOTO_GUIDES.find(g => 
      g.required && !capturedAngles.includes(g.angle)
    );
    
    if (nextRequiredGuide) {
      // Continue to next required photo
      setCurrentPhotoAngle(nextRequiredGuide.angle);
    } else {
      // All required photos captured
      return "✅ Fotos capturadas correctamente";
    }
  }, []);

  const handleDesiredCameraCapture = useCallback((
    uri: string, 
    angle: PhotoAngle, 
    quality: PhotoQuality,
    desiredPhotos: DesiredPhoto[],
    setDesiredPhotos: (photos: DesiredPhoto[]) => void,
    currentDesiredPhotoType: DesiredPhotoType
  ) => {
    let photoType = currentDesiredPhotoType;
    
    // If no type specified, assign based on current count
    if (!photoType) {
      if (desiredPhotos.length === 0) {
        photoType = DesiredPhotoType.MAIN;
      } else if (desiredPhotos.length === 1) {
        photoType = DesiredPhotoType.DETAILS;
      } else {
        photoType = DesiredPhotoType.ALTERNATIVE;
      }
    }
    
    const newPhoto: DesiredPhoto = {
      id: Date.now().toString(),
      uri,
      type: photoType,
      timestamp: new Date()
    };
    
    // For the first 3 photos, replace if same type exists
    // For additional photos (4-5), just add them
    if (photoType && desiredPhotos.length < 3) {
      const filtered = desiredPhotos.filter(p => p.type !== photoType);
      setDesiredPhotos([...filtered, newPhoto]);
    } else {
      setDesiredPhotos([...desiredPhotos, newPhoto]);
    }
    
    return "✅ Foto capturada correctamente";
  }, []);

  const performAnalysis = useCallback(async (hairPhotos: CapturedPhoto[], clientId: string) => {
    try {
      console.log("Starting AI analysis with", hairPhotos.length, "photos");
      const primaryPhoto = hairPhotos.find(p => p.angle === PhotoAngle.CROWN) || hairPhotos[0];
      
      if (!clientId) {
        throw new Error("No se encontró información del cliente");
      }
      
      // Check if already anonymized (public URL)
      if (primaryPhoto.uri.startsWith('http://') || primaryPhoto.uri.startsWith('https://')) {
        // Already anonymized, use directly
        await analyzeImage(primaryPhoto.uri);
      } else {
        // Upload and anonymize first
        console.log("Uploading and anonymizing image...");
        const result = await uploadAndAnonymizeImage(primaryPhoto.uri, {
          clientId,
          photoType: 'before',
          onProgress: (progress) => {
            console.log(`Upload progress: ${progress}%`);
          }
        });
        
        if (!result.success || !result.publicUrl) {
          throw new Error(result.error || "Error al procesar la imagen");
        }
        
        console.log("Image anonymized, analyzing...");
        await analyzeImage(result.publicUrl);
      }
      
      console.log("AI analysis completed");
      return "✅ Análisis completo. Revisa y ajusta según tu criterio";
    } catch (error: any) {
      console.error("Error in analysis:", error);
      
      if (error.message === 'TIMEOUT_ERROR') {
        throw new Error('TIMEOUT_ERROR');
      } else if (error.message.includes('anonimizar')) {
        throw new Error("Error al procesar la imagen de forma segura. Por favor intenta nuevamente.");
      } else {
        throw new Error("No se pudo completar el análisis. Por favor intenta nuevamente.");
      }
    }
  }, [analyzeImage]);

  return {
    // State
    currentPhotoAngle,
    setCurrentPhotoAngle,
    imageQuality,
    setImageQuality,
    isImageProcessing,
    setIsImageProcessing,
    isAnalyzingDesired,
    setIsAnalyzingDesired,
    
    // AI Analysis Store
    isAnalyzing,
    analysisResult,
    privacyMode,
    setPrivacyMode,
    clearAnalysis,
    
    // Photo Capture Store
    pendingPhoto,
    clearPendingPhoto,
    
    // Functions
    performImageQualityCheck,
    takePhoto,
    pickImage,
    pickMultipleImages,
    handleCameraCapture,
    handleDesiredCameraCapture,
    performAnalysis
  };
};

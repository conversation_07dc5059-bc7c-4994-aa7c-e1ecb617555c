import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet, Image, TextInput, Switch, Alert } from 'react-native';
import { Camera, Upload, AlertTriangle, CheckCircle, XCircle } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { ServiceData } from '@/src/service/hooks/useServiceFlow';
import { usePhotoAnalysis } from '@/src/service/hooks/usePhotoAnalysis';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { InventoryConsumptionService } from '@/services/inventoryConsumptionService';
import { parseFormulaText, parseStructuredFormulaToProducts } from '@/utils/parseFormula';
import { uploadAndAnonymizeImage } from '@/utils/secure-image-upload';
import { FormulationConsumption } from '@/types/inventory';
import { ProductMappingModal } from '@/components/inventory/ProductMappingModal';
import { useInventoryStore } from '@/stores/inventory-store';
import { MaterialsSummaryCard } from '@/components/formulation/MaterialsSummaryCard';

interface CompletionStepProps {
  data: ServiceData;
  onUpdate: (updates: Partial<ServiceData>) => void;
  onNext: () => void;
  onBack: () => void;
  onSave?: () => void;
}

export const CompletionStep: React.FC<CompletionStepProps> = ({
  data,
  onUpdate,
  onNext,
  onBack,
  onSave
}) => {
  const [consumeInventory, setConsumeInventory] = useState(false);
  const [isConsumingInventory, setIsConsumingInventory] = useState(false);
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const [formulationAnalysis, setFormulationAnalysis] = useState<FormulationConsumption | null>(null);
  const [isAnalyzingFormula, setIsAnalyzingFormula] = useState(false);
  const [mappingModal, setMappingModal] = useState<{
    visible: boolean;
    aiProductName: string;
    suggestedProduct: any;
    confidence: number;
    onConfirm: (productId: string) => void;
  } | null>(null);

  const { takePhoto, pickImage } = usePhotoAnalysis();
  const { saveProductMapping } = useInventoryStore();

  const handleResultImageCapture = async (uri: string) => {
    if (!data.clientId) {
      Alert.alert("Error", "No se pudo identificar el cliente. Por favor, intenta nuevamente.");
      return;
    }

    setIsUploadingImage(true);
    
    try {
      // Subir imagen usando el sistema seguro
      const result = await uploadAndAnonymizeImage(uri, {
        clientId: data.clientId,
        photoType: 'after', // Las fotos 'after' no tienen filtro de privacidad
        onProgress: (progress) => {
          console.log(`Progreso de subida: ${progress}%`);
        }
      });

      if (result.success && result.publicUrl) {
        // Guardar URL pública en lugar de URI local
        onUpdate({ resultImage: result.publicUrl });
        onSave?.();
      } else {
        throw new Error(result.error || 'No se pudo subir la imagen');
      }
    } catch (error: any) {
      console.error('Error al subir imagen de resultado:', error);
      Alert.alert(
        "Error al subir imagen",
        error.message || "No se pudo subir la imagen. ¿Deseas intentar nuevamente?",
        [
          { text: "Cancelar", style: "cancel" },
          { 
            text: "Reintentar", 
            onPress: () => handleResultImageCapture(uri)
          }
        ]
      );
    } finally {
      setIsUploadingImage(false);
    }
  };

  const handleSatisfactionChange = (rating: number) => {
    onUpdate({ clientSatisfaction: rating });
    onSave?.();
  };

  const handleNotesChange = (notes: string) => {
    onUpdate({ resultNotes: notes });
    onSave?.();
  };

  const handleInventoryConsumption = async () => {
    if (!data.formula || !consumeInventory) return;

    setIsConsumingInventory(true);
    
    try {
      // Check if we have structured formula data (from JSON)
      let colorFormula;
      if (data.formulationData) {
        // Create a formula object with the structured data
        colorFormula = {
          brand: data.selectedBrand || 'Unknown',
          line: data.selectedLine || 'Unknown',
          formulationData: data.formulationData,
          formulaText: data.formula,
        };
      } else {
        // Fallback to parsing text formula
        colorFormula = parseFormulaText(data.formula);
      }
      
      const result = await InventoryConsumptionService.consumeFormulation(
        data.clientId || 'unknown',
        colorFormula,
        data.client?.name || 'Cliente',
        true // forceConsume = true cuando el usuario activa el switch
      );
      
      if (result.success && result.totalConsumed > 0) {
        // Éxito completo
        Alert.alert(
          "Inventario actualizado",
          `Se descontaron ${result.totalConsumed} productos correctamente:\n\n${result.consumedProducts.join('\n')}`,
          [{ text: "OK" }]
        );
      } else if (result.notFoundProducts.length > 0) {
        // Algunos productos no se encontraron
        let message = result.consumedProducts.length > 0
          ? `Se descontaron ${result.consumedProducts.length} productos:\n${result.consumedProducts.join('\n')}\n\nNo se encontraron en inventario:\n${result.notFoundProducts.join('\n')}`
          : `No se pudo descontar ningún producto.\n\nProductos no encontrados en inventario:\n${result.notFoundProducts.join('\n')}`;
        
        // Add helpful tip about structured data
        message += '\n\nSugerencia: Asegúrate de que los productos en el inventario tengan los campos estructurados correctos (marca, tipo, tono).';
        
        Alert.alert(
          result.consumedProducts.length > 0 ? "Inventario parcialmente actualizado" : "No se actualizó el inventario",
          message,
          [{ text: "OK" }]
        );
      } else if (result.errors.length > 0) {
        // Errores específicos
        Alert.alert(
          "Error al actualizar inventario",
          result.errors.join('\n'),
          [{ text: "OK" }]
        );
      }
    } catch (error) {
      console.error('Error consuming inventory:', error);
      Alert.alert(
        "Error",
        "No se pudo actualizar el inventario. Por favor, hazlo manualmente.",
        [{ text: "OK" }]
      );
    } finally {
      setIsConsumingInventory(false);
    }
  };

  const handleFinish = async () => {
    // Consume inventory if enabled
    if (consumeInventory && data.formula) {
      await handleInventoryConsumption();
    }
    
    // Proceed to finish
    onNext();
  };

  const salonConfig = useSalonConfigStore.getState();
  const showInventoryControl = data.formula && salonConfig.configuration.inventoryControlLevel === 'control-total';
  
  // Analizar la fórmula cuando cambie
  useEffect(() => {
    const analyzeFormula = async () => {
      if (!data.formula || !showInventoryControl) {
        setFormulationAnalysis(null);
        return;
      }
      
      setIsAnalyzingFormula(true);
      try {
        const analysis = await InventoryConsumptionService.calculateFormulationCostFromText(data.formula);
        setFormulationAnalysis(analysis);
      } catch (error) {
        console.error('Error analyzing formula:', error);
      } finally {
        setIsAnalyzingFormula(false);
      }
    };
    
    analyzeFormula();
  }, [data.formula, showInventoryControl]);

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.stepContainer}>
        <Text style={styles.stepTitle}>Resultado Final</Text>
        {data.client && (
          <Text style={styles.clientName}>Cliente: {data.client?.name || 'Cliente'}</Text>
        )}

        <Text style={styles.sectionTitle}>Fotos del resultado final</Text>
        <View style={styles.photoContainer}>
          {data.resultImage ? (
            <Image source={{ uri: data.resultImage }} style={styles.photoPreview} />
          ) : (
            <View style={styles.photoPlaceholder}>
              <Camera size={40} color={Colors.light.gray} />
              <Text style={styles.photoPlaceholderText}>
                {isUploadingImage ? "Subiendo imagen..." : "Fotografía del resultado"}
              </Text>
            </View>
          )}
          <View style={styles.photoButtons}>
            <TouchableOpacity 
              style={[styles.photoButton, isUploadingImage && styles.photoButtonDisabled]}
              onPress={() => takePhoto(handleResultImageCapture)}
              disabled={isUploadingImage}
            >
              <Camera size={16} color="white" />
              <Text style={styles.photoButtonText}>
                {isUploadingImage ? "Subiendo..." : "Usar Cámara"}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.photoButton, isUploadingImage && styles.photoButtonDisabled]}
              onPress={() => pickImage(handleResultImageCapture)}
              disabled={isUploadingImage}
            >
              <Upload size={16} color="white" />
              <Text style={styles.photoButtonText}>
                {isUploadingImage ? "Subiendo..." : "Seleccionar"}
              </Text>
            </TouchableOpacity>
          </View>
          <Text style={styles.photoTip}>
            🔒 PRIVACIDAD: Las imágenes del resultado final se suben de forma segura al servidor para su almacenamiento.
          </Text>
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Satisfacción del cliente (1-5)</Text>
          <View style={styles.satisfactionContainer}>
            {[1, 2, 3, 4, 5].map((rating) => (
              <TouchableOpacity
                key={rating}
                style={[
                  styles.satisfactionButton,
                  data.clientSatisfaction === rating && styles.satisfactionButtonActive
                ]}
                onPress={() => handleSatisfactionChange(rating)}
              >
                <Text style={[
                  styles.satisfactionButtonText,
                  data.clientSatisfaction === rating && styles.satisfactionButtonTextActive
                ]}>
                  {rating}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Notas finales</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={data.resultNotes}
            onChangeText={handleNotesChange}
            placeholder="Observaciones sobre el resultado, ajustes realizados, recomendaciones para el cliente, etc."
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>
        
        {/* Materials Summary Card - unify inventory display */}
        {data.formula && (
          <MaterialsSummaryCard
            formulationData={data.formulationData}
            formulaText={data.formula}
            selectedBrand={data.selectedBrand || ''}
            selectedLine={data.selectedLine || ''}
          />
        )}
        
        {/* Inventory Consumption Section - Simplified */}
        {showInventoryControl && (
          <View style={styles.inventoryConsumptionSection}>
            <View style={styles.inventoryConsumptionHeader}>
              <Text style={styles.inventoryConsumptionTitle}>Descontar del Inventario</Text>
              <Switch
                trackColor={{ false: Colors.light.lightGray, true: Colors.light.primary }}
                thumbColor="white"
                ios_backgroundColor={Colors.light.lightGray}
                onValueChange={setConsumeInventory}
                value={consumeInventory}
              />
            </View>
            
            {consumeInventory && (
              <Text style={styles.inventoryConsumptionInfo}>
                Los productos disponibles se descontarán del inventario al finalizar el servicio.
              </Text>
            )}
          </View>
        )}

        {/* Finish Button */}
        <TouchableOpacity 
          style={[styles.finishButton, isConsumingInventory && styles.finishButtonDisabled]}
          onPress={handleFinish}
          disabled={isConsumingInventory}
        >
          <Text style={styles.finishButtonText}>
            {isConsumingInventory ? "Guardando servicio..." : "Finalizar Servicio"}
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  stepContainer: {
    padding: 15,
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 5,
  },
  clientName: {
    fontSize: 16,
    color: Colors.light.gray,
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 10,
  },
  photoContainer: {
    backgroundColor: "white",
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
  },
  photoPreview: {
    width: "100%",
    height: 200,
    borderRadius: 8,
    marginBottom: 15,
  },
  photoPlaceholder: {
    width: "100%",
    height: 200,
    borderRadius: 8,
    backgroundColor: "#F0F0F5",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 15,
  },
  photoPlaceholderText: {
    marginTop: 10,
    color: Colors.light.gray,
  },
  photoButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  photoButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.light.primary,
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 15,
    flex: 1,
    marginHorizontal: 5,
    justifyContent: "center",
  },
  photoButtonText: {
    color: "white",
    fontWeight: "600",
    marginLeft: 5,
  },
  photoButtonDisabled: {
    backgroundColor: Colors.light.gray,
    opacity: 0.7,
  },
  photoTip: {
    fontSize: 12,
    color: Colors.light.success,
    marginTop: 10,
    textAlign: "center",
    fontWeight: "500",
  },
  formGroup: {
    marginBottom: 15,
  },
  label: {
    fontSize: 15,
    fontWeight: "500",
    marginBottom: 8,
  },
  satisfactionContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  satisfactionButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: Colors.light.border,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "white",
  },
  satisfactionButtonActive: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  satisfactionButtonText: {
    fontSize: 18,
    fontWeight: "bold",
  },
  satisfactionButtonTextActive: {
    color: "white",
  },
  input: {
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 15,
  },
  textArea: {
    minHeight: 100,
    paddingTop: 12,
    textAlignVertical: "top",
  },
  inventoryConsumptionSection: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  inventoryConsumptionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  inventoryConsumptionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.light.text,
  },
  inventoryConsumptionInfo: {
    fontSize: 14,
    color: Colors.light.gray,
    marginTop: 8,
  },
  finishButton: {
    backgroundColor: Colors.light.success,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: "center",
    marginTop: 20,
    shadowColor: Colors.light.success,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  finishButtonDisabled: {
    backgroundColor: Colors.light.gray,
    shadowOpacity: 0,
    elevation: 0,
  },
  finishButtonText: {
    color: "white",
    fontWeight: "700",
    fontSize: 16,
  },
});

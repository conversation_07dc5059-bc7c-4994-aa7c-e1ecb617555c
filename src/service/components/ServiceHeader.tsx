import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { router } from 'expo-router';
import { ChevronLeft } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { STEPS } from '@/src/service/hooks/useServiceFlow';
import { AutoSaveIndicator } from './AutoSaveIndicator';

interface ServiceHeaderProps {
  currentStep: number;
  clientName?: string;
  lastSaved?: Date;
  isSaving?: boolean;
  onBack?: () => void;
}

export const ServiceHeader: React.FC<ServiceHeaderProps> = ({
  currentStep,
  clientName,
  lastSaved,
  isSaving,
  onBack
}) => {
  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      router.back();
    }
  };

  const currentStepTitle = STEPS[currentStep]?.title || "Servicio";

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={handleBack}
        >
          <ChevronLeft size={24} color={Colors.light.text} />
        </TouchableOpacity>
        
        <View style={styles.titleContainer}>
          <Text style={styles.title}>{currentStepTitle}</Text>
          {clientName && (
            <Text style={styles.subtitle}>{clientName}</Text>
          )}
        </View>
        
        <AutoSaveIndicator 
          lastSaved={lastSaved}
          isSaving={isSaving}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 15,
    minHeight: 60,
  },
  backButton: {
    padding: 5,
    marginRight: 10,
  },
  titleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    color: Colors.light.gray,
    marginTop: 2,
    textAlign: 'center',
  },
});

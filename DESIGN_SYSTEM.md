# Sistema de Diseño Premium - Salonier

## Visión General
El sistema de diseño de Salonier está inspirado en la elegancia y profesionalismo de los salones de belleza de alta gama. Utiliza una paleta de colores cálidos que evocan sofisticación y confianza.

## Paleta de Colores

### Colores Principales
- **Dorado Primario**: `#D4A574` - Elegancia y lujo
- **Dorado Oscuro**: `#B8941F` - Para estados hover/pressed
- **Dorado <PERSON>laro**: `#E6C757` - Acentos sutiles

### Colores Secundarios
- **Naranja <PERSON>**: `#E8975B` - Energía y vitalidad
- **Sienna**: `#CD853F` - Acentos tierra

### Fondos
- **Crema**: `#FFF8E7` - Fondo principal de la aplicación
- **Beige Claro**: `#F5E6D3` - Fondo de tarjetas y superficies
- **Negro Premium**: `#1C1C1C` - Contraste y elegancia

### Texto
- **Texto Principal**: `#2C2416` - Marrón oscuro para máxima legibilidad
- **Texto Secundario**: `#8D7B68` - Gris cálido para información secundaria
- **Texto Claro**: `#FFFFFF` - Para usar sobre fondos oscuros

### Estados
- **Success**: `#4CAF50` - Verde natural
- **Warning**: `#F9A825` - Amarillo dorado
- **Error**: `#D32F2F` - Rojo suave
- **Info**: `#5C6BC0` - Azul lavanda

## Tipografía

### Escala de Tamaños
```typescript
xs: 12px    // Texto muy pequeño, etiquetas
sm: 14px    // Texto secundario
base: 16px  // Texto principal
lg: 18px    // Subtítulos
xl: 20px    // Títulos pequeños
2xl: 24px   // Títulos medianos
3xl: 30px   // Títulos grandes
4xl: 36px   // Títulos extra grandes
```

### Pesos de Fuente
```typescript
regular: '400'    // Texto normal
medium: '500'     // Énfasis ligero
semibold: '600'   // Énfasis medio
bold: '700'       // Énfasis fuerte
extrabold: '800'  // Máximo énfasis
```

## Espaciado
Sistema basado en múltiplos de 4px:
```typescript
xs: 4px     // Espacios mínimos
sm: 8px     // Espacios pequeños
md: 16px    // Espacios estándar
lg: 24px    // Espacios grandes
xl: 32px    // Espacios extra grandes
2xl: 48px   // Espacios muy grandes
3xl: 64px   // Espacios máximos
```

## Bordes Redondeados
```typescript
sm: 8px     // Bordes sutiles
md: 12px    // Bordes estándar
lg: 16px    // Bordes pronunciados
xl: 24px    // Bordes muy redondeados
full: 9999px // Completamente circular
```

## Sombras
Sistema de elevación sutil:
```typescript
none: Sin sombra
sm: shadowOpacity: 0.05, shadowRadius: 2  // Elevación mínima
md: shadowOpacity: 0.1, shadowRadius: 4   // Elevación media
lg: shadowOpacity: 0.15, shadowRadius: 8  // Elevación alta
xl: shadowOpacity: 0.2, shadowRadius: 16  // Elevación máxima
```

## Componentes Base

### BaseCard
Tarjeta reutilizable con fondo beige y sombras sutiles:
- Variantes: `default`, `elevated`, `flat`
- Fondo: `Colors.light.card` (#F5E6D3)
- Bordes redondeados: `radius.lg`
- Padding: `spacing.md`

### BaseButton
Botón con feedback háptico y múltiples variantes:
- Variantes: `primary`, `secondary`, `ghost`, `danger`
- Tamaños: `small`, `medium`, `large`
- Feedback háptico en press
- Animaciones suaves

### BaseHeader
Encabezado consistente para pantallas:
- Navegación integrada
- Título centrado
- Acciones opcionales
- Fondo premium

### BaseProgress
Barra de progreso personalizada:
- Colores graduales
- Indicador de porcentaje
- Animaciones fluidas

## Mejores Prácticas

### 1. Consistencia Visual
- Siempre usa los colores del sistema, nunca valores hardcodeados
- Mantén el espaciado consistente usando las constantes
- Aplica sombras sutiles para crear jerarquía

### 2. Accesibilidad
- Mantén contraste adecuado entre texto y fondo
- Usa tamaños de fuente legibles (mínimo 12px)
- Proporciona feedback visual y háptico

### 3. Rendimiento
- Usa sombras con moderación
- Optimiza imágenes y assets
- Minimiza re-renders innecesarios

### 4. Experiencia Premium
- Transiciones suaves (300ms estándar)
- Micro-interacciones significativas
- Espaciado generoso para sensación de lujo

## Implementación

### Importaciones Básicas
```typescript
import Colors from "@/constants/colors";
import { typography, spacing, radius, shadows } from "@/constants/theme";
import { BaseCard, BaseButton, BaseHeader } from "@/components/base";
```

### Ejemplo de Uso
```typescript
// Estilo de contenedor
container: {
  flex: 1,
  backgroundColor: Colors.light.background,
  padding: spacing.md,
}

// Estilo de tarjeta
card: {
  backgroundColor: Colors.light.card,
  borderRadius: radius.lg,
  padding: spacing.md,
  ...shadows.sm,
}

// Estilo de texto
title: {
  fontSize: typography.sizes.xl,
  fontWeight: typography.weights.semibold,
  color: Colors.light.text,
  marginBottom: spacing.sm,
}
```

## Evolución del Sistema
Este sistema de diseño es un documento vivo que evoluciona con las necesidades de Salonier. Todas las actualizaciones deben mantener la coherencia con la visión premium y profesional de la marca.
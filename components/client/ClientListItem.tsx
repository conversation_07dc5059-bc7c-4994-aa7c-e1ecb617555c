import React from 'react';
import { StyleSheet, Text, View, TouchableOpacity, Platform } from 'react-native';
import { Link, router } from 'expo-router';
import { Eye, Edit, Trash2, AlertTriangle, Phone, Calendar } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { typography, spacing, radius } from '@/constants/theme';
import { Client } from '@/stores/client-store';

interface ClientListItemProps {
  client: Client;
  warnings: string[];
  recommendations: string[];
  profile: any | null;
  canDelete: boolean;
  onDelete: (id: string, name: string) => void;
  onView: (client: Client) => void;
}

const getRiskColor = (riskLevel: string) => {
  switch (riskLevel) {
    case 'alto': return Colors.light.error;
    case 'medio': return Colors.light.warning;
    default: return Colors.light.success;
  }
};

const linkStyle = Platform.select({
  web: { textDecoration: 'none' } as const,
  default: {}
}) as any;

const ClientListItem = React.memo<ClientListItemProps>(({ 
  client, 
  warnings, 
  recommendations, 
  profile,
  canDelete,
  onDelete,
  onView 
}) => {
  return (
    <View style={styles.clientCard}>
      <View style={styles.clientAvatar}>
        <Text style={styles.clientInitial}>{client.name.charAt(0)}</Text>
        {warnings.length > 0 && (
          <View style={styles.warningBadge}>
            <Text style={styles.warningBadgeText}>{warnings.length}</Text>
          </View>
        )}
      </View>
      
      <View style={styles.clientInfo}>
        <View style={styles.clientHeader}>
          <Text style={styles.clientName}>{client.name}</Text>
          {profile && (
            <View style={[
              styles.riskBadge,
              { backgroundColor: getRiskColor(profile.riskLevel) + "20" }
            ]}>
              <Text style={[
                styles.riskText,
                { color: getRiskColor(profile.riskLevel) }
              ]}>
                {profile.riskLevel.toUpperCase()}
              </Text>
            </View>
          )}
        </View>
        
        <View style={styles.clientDetails}>
          <View style={styles.detailRow}>
            <Phone size={14} color={Colors.light.gray} />
            <Text style={styles.clientPhone}>{client.phone}</Text>
          </View>
          <View style={styles.detailRow}>
            <Calendar size={14} color={Colors.light.gray} />
            <Text style={styles.clientLastVisit}>Última visita: {client.lastVisit || '10 Mayo 2023'}</Text>
          </View>
        </View>
        
        {warnings.length > 0 && (
          <View style={styles.warningInfo}>
            <AlertTriangle size={12} color={Colors.light.error} />
            <Text style={styles.warningText} numberOfLines={1}>
              {warnings[0]}
            </Text>
          </View>
        )}
      </View>
      
      <View style={styles.actionsContainer}>
        <TouchableOpacity 
          style={[styles.actionButton, styles.viewButton]} 
          onPress={() => onView(client)}
        >
          <Eye size={16} color={Colors.light.primary} />
        </TouchableOpacity>
        <Link href={`/client/edit/${client.id}`} style={linkStyle}>
          <View style={[styles.actionButton, styles.editButton]}>
            <Edit size={16} color={Colors.light.accent} />
          </View>
        </Link>
        {canDelete && (
          <TouchableOpacity 
            style={[styles.actionButton, styles.deleteButton]} 
            onPress={() => onDelete(client.id, client.name)}
          >
            <Trash2 size={16} color={Colors.light.error} />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}, (prevProps, nextProps) => {
  // Custom comparison for better performance
  return (
    prevProps.client.id === nextProps.client.id &&
    prevProps.client.name === nextProps.client.name &&
    prevProps.client.phone === nextProps.client.phone &&
    prevProps.warnings.length === nextProps.warnings.length &&
    prevProps.recommendations.length === nextProps.recommendations.length &&
    prevProps.profile?.riskLevel === nextProps.profile?.riskLevel &&
    prevProps.canDelete === nextProps.canDelete
  );
});

ClientListItem.displayName = 'ClientListItem';

const styles = StyleSheet.create({
  clientCard: {
    backgroundColor: "white",
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    flexDirection: "row",
    alignItems: "center",
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  clientAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: Colors.light.primary + "15",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
    position: "relative",
  },
  clientInitial: {
    fontSize: 20,
    fontWeight: "700",
    color: Colors.light.primary,
  },
  warningBadge: {
    position: "absolute",
    top: -4,
    right: -4,
    backgroundColor: Colors.light.error,
    borderRadius: radius.full,
    width: 20,
    height: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  warningBadgeText: {
    color: "white",
    fontSize: 11,
    fontWeight: "600",
  },
  clientInfo: {
    flex: 1,
  },
  clientHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  clientName: {
    fontSize: 18,
    fontWeight: "700",
    color: Colors.light.text,
    flex: 1,
  },
  riskBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 6,
  },
  riskText: {
    fontSize: 10,
    fontWeight: "600",
  },
  clientDetails: {
    gap: 4,
    marginBottom: 8,
  },
  detailRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  clientPhone: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  clientLastVisit: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  warningInfo: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.light.error + "10",
    borderRadius: 6,
    paddingHorizontal: 8,
    paddingVertical: 4,
    gap: 4,
  },
  warningText: {
    fontSize: 11,
    color: Colors.light.error,
    fontWeight: "500",
    flex: 1,
  },
  actionsContainer: {
    flexDirection: "column",
    gap: 8,
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: Colors.light.primary + "10",
  },
  viewButton: {
    backgroundColor: Colors.light.primary + "10",
  },
  editButton: {
    backgroundColor: Colors.light.accent + "10",
  },
  deleteButton: {
    backgroundColor: Colors.light.error + "10",
  },
});

export default ClientListItem;
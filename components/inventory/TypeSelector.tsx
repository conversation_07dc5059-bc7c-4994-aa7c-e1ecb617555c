import React, { useState, useMemo } from 'react';
import { View, Text, TouchableOpacity, TextInput, StyleSheet, ScrollView } from 'react-native';
import { ProductType, productTypes, searchProductTypes, productCategories } from '@/constants/reference-data/product-types-data';
import { Ionicons } from '@expo/vector-icons';
import { typography, spacing, radius } from '@/constants/theme';
import Colors from '@/constants/colors';

interface TypeSelectorProps {
  selectedType: string | null;
  onTypeSelect: (type: string | null) => void;
  placeholder?: string;
  disabled?: boolean;
}

export function TypeSelector({ selectedType, onTypeSelect, placeholder = "Seleccionar tipo de producto", disabled = false }: TypeSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const filteredTypes = useMemo(() => {
    if (!searchQuery.trim()) {
      // Group by category when not searching
      return productCategories.map(category => ({
        title: category.label,
        data: productTypes.filter(type => type.category === category.id)
      }));
    }
    
    // Flat list when searching
    const searchResults = searchProductTypes(searchQuery);
    return [{
      title: 'Resultados de búsqueda',
      data: searchResults
    }];
  }, [searchQuery]);

  const selectedTypeData = productTypes.find(type => type.id === selectedType);

  const handleTypeSelect = (type: ProductType) => {
    onTypeSelect(type.id);
    setIsOpen(false);
    setSearchQuery('');
  };

  const handleClear = () => {
    onTypeSelect(null);
    setSearchQuery('');
  };

  const renderTypeItem = ({ item }: { item: ProductType }) => (
    <TouchableOpacity
      style={styles.typeItem}
      onPress={() => handleTypeSelect(item)}
    >
      <View style={styles.typeInfo}>
        <Text style={styles.typeName}>{item.name}</Text>
        {item.description && (
          <Text style={styles.typeDescription}>{item.description}</Text>
        )}
      </View>
      {selectedType === item.id && (
        <Ionicons name="checkmark-circle" size={20} color={Colors.light.primary} />
      )}
    </TouchableOpacity>
  );

  const renderSectionHeader = ({ section }: { section: { title: string } }) => (
    <View style={styles.sectionHeader}>
      <Text style={styles.sectionTitle}>{section.title}</Text>
    </View>
  );

  if (isOpen) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Seleccionar Tipo de Producto</Text>
          <TouchableOpacity onPress={() => setIsOpen(false)}>
            <Ionicons name="close" size={24} color="#666" />
          </TouchableOpacity>
        </View>
        
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Buscar tipo de producto..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            autoFocus
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color="#666" />
            </TouchableOpacity>
          )}
        </View>

        <ScrollView style={styles.typeList} showsVerticalScrollIndicator={true}>
          {filteredTypes.map((section, sectionIndex) => (
            <View key={sectionIndex}>
              {renderSectionHeader({ section })}
              {section.data.map((item) => (
                <View key={item.id}>
                  {renderTypeItem({ item })}
                </View>
              ))}
            </View>
          ))}
        </ScrollView>

        {selectedType && (
          <TouchableOpacity style={styles.clearButton} onPress={handleClear}>
            <Text style={styles.clearButtonText}>Limpiar selección</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }

  return (
    <TouchableOpacity
      style={[styles.selector, disabled && styles.selectorDisabled]}
      onPress={() => !disabled && setIsOpen(true)}
      disabled={disabled}
    >
      <View style={styles.selectorContent}>
        <Text style={[styles.selectorText, !selectedType && styles.placeholderText]}>
          {selectedTypeData ? selectedTypeData.name : placeholder}
        </Text>
        <Ionicons 
          name="chevron-down" 
          size={20} 
          color={disabled ? Colors.light.gray : Colors.light.text} 
        />
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    maxHeight: 500,
    shadowColor: '#D4A574',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  title: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  searchIcon: {
    marginRight: spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: typography.sizes.base,
    color: Colors.light.text,
    paddingVertical: spacing.xs,
  },
  typeList: {
    maxHeight: 300,
  },
  sectionHeader: {
    backgroundColor: '#F5F5F7',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  sectionTitle: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.textSecondary,
    textTransform: 'uppercase',
  },
  typeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm + 2,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border + '20',
  },
  typeInfo: {
    flex: 1,
    marginRight: spacing.sm,
  },
  typeName: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
  },
  typeDescription: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
    marginTop: 2,
  },
  clearButton: {
    padding: spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  clearButtonText: {
    fontSize: typography.sizes.base,
    color: Colors.light.primary,
    fontWeight: typography.weights.medium,
    textAlign: 'center',
  },
  selector: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingHorizontal: spacing.md,
    paddingVertical: 14,
    borderWidth: 2,
    borderColor: '#D4A574', // Borde dorado visible
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  selectorDisabled: {
    opacity: 0.6,
  },
  selectorContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  selectorText: {
    fontSize: typography.sizes.base,
    color: Colors.light.text,
    flex: 1,
  },
  placeholderText: {
    color: Colors.light.textSecondary,
  },
});
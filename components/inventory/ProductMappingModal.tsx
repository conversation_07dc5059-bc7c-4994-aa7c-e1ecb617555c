import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Product } from '@/types/inventory';
import { useInventoryStore } from '@/stores/inventory-store';
import { useTheme } from '@/hooks/useTheme';

interface ProductMappingModalProps {
  visible: boolean;
  onClose: () => void;
  aiProductName: string;
  suggestedProduct: Product | null;
  confidence: number;
  onConfirm: (productId: string) => void;
  onCreateNew: () => void;
}

export const ProductMappingModal: React.FC<ProductMappingModalProps> = ({
  visible,
  onClose,
  aiProductName,
  suggestedProduct,
  confidence,
  onConfirm,
  onCreateNew,
}) => {
  const { colors } = useTheme();
  const { products, searchProducts } = useInventoryStore();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProductId, setSelectedProductId] = useState(suggestedProduct?.id || '');

  const searchResults = searchQuery.length > 0 
    ? searchProducts(searchQuery)
    : suggestedProduct 
      ? [suggestedProduct, ...products.filter(p => p.id !== suggestedProduct.id).slice(0, 4)]
      : products.slice(0, 5);

  const handleConfirm = () => {
    if (selectedProductId) {
      onConfirm(selectedProductId);
    }
  };

  const getConfidenceColor = () => {
    if (confidence >= 90) return '#4CAF50';
    if (confidence >= 70) return '#FF9800';
    return '#F44336';
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={[styles.modalContent, { backgroundColor: colors.surface }]}>
          <View style={styles.header}>
            <Text style={[styles.title, { color: colors.text.primary }]}>
              Confirmar Producto
            </Text>
            <TouchableOpacity onPress={onClose}>
              <MaterialIcons name="close" size={24} color={colors.text.secondary} />
            </TouchableOpacity>
          </View>

          <View style={styles.aiProductSection}>
            <Text style={[styles.sectionLabel, { color: colors.text.secondary }]}>
              La IA sugiere:
            </Text>
            <Text style={[styles.aiProductName, { color: colors.text.primary }]}>
              {aiProductName}
            </Text>
            {confidence > 0 && (
              <View style={styles.confidenceContainer}>
                <Text style={[styles.confidenceLabel, { color: colors.text.secondary }]}>
                  Confianza:
                </Text>
                <View style={[styles.confidenceBadge, { backgroundColor: getConfidenceColor() }]}>
                  <Text style={styles.confidenceText}>{confidence}%</Text>
                </View>
              </View>
            )}
          </View>

          <View style={styles.divider} />

          <View style={styles.inventorySection}>
            <Text style={[styles.sectionLabel, { color: colors.text.secondary }]}>
              Selecciona del inventario:
            </Text>

            <ScrollView style={styles.productList} showsVerticalScrollIndicator={false}>
              {searchResults.map((product) => (
                <TouchableOpacity
                  key={product.id}
                  style={[
                    styles.productItem,
                    {
                      backgroundColor: colors.background,
                      borderColor: selectedProductId === product.id ? colors.primary : colors.border,
                      borderWidth: selectedProductId === product.id ? 2 : 1,
                    },
                  ]}
                  onPress={() => setSelectedProductId(product.id)}
                >
                  <View style={styles.productInfo}>
                    <Text style={[styles.productName, { color: colors.text.primary }]}>
                      {product.displayName || product.name}
                    </Text>
                    <Text style={[styles.productDetails, { color: colors.text.secondary }]}>
                      {product.brand} • {product.shade || product.colorCode || 'Sin tono'}
                    </Text>
                    <Text style={[styles.stockInfo, { color: colors.text.secondary }]}>
                      Stock: {product.currentStock} {product.unitType}
                    </Text>
                  </View>
                  {selectedProductId === product.id && (
                    <MaterialIcons name="check-circle" size={24} color={colors.primary} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>

          <View style={styles.actions}>
            <TouchableOpacity
              style={[styles.button, styles.secondaryButton, { borderColor: colors.border }]}
              onPress={onCreateNew}
            >
              <MaterialIcons name="add" size={20} color={colors.primary} />
              <Text style={[styles.buttonText, { color: colors.primary }]}>
                Crear Nuevo
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.button,
                styles.primaryButton,
                { 
                  backgroundColor: colors.primary,
                  opacity: selectedProductId ? 1 : 0.5,
                },
              ]}
              onPress={handleConfirm}
              disabled={!selectedProductId}
            >
              <Text style={[styles.buttonText, { color: '#FFFFFF' }]}>
                Confirmar
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  aiProductSection: {
    marginBottom: 20,
  },
  sectionLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 8,
    textTransform: 'uppercase',
  },
  aiProductName: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  confidenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  confidenceLabel: {
    fontSize: 14,
  },
  confidenceBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  confidenceText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  divider: {
    height: 1,
    backgroundColor: '#E0E0E0',
    marginVertical: 20,
  },
  inventorySection: {
    flex: 1,
    marginBottom: 20,
  },
  productList: {
    maxHeight: 250,
  },
  productItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  productDetails: {
    fontSize: 14,
    marginBottom: 4,
  },
  stockInfo: {
    fontSize: 12,
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
  },
  button: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 8,
  },
  primaryButton: {
    backgroundColor: '#FF6B6B',
  },
  secondaryButton: {
    borderWidth: 1,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
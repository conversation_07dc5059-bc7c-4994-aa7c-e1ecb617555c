import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
  Modal,
} from 'react-native';
import { Search, MessageCircle, X, Archive, Star, MoreVertical } from 'lucide-react-native';
import { SwipeableRow } from '@/components/base/SwipeableRow';
import Colors from '@/constants/colors';
import { spacing, typography, radius } from '@/constants/theme';
import { ChatConversation, useChatStore } from '@/stores/chat-store';
import { logger } from '@/utils/logger';

interface ConversationsListProps {
  conversations: ChatConversation[];
  activeConversationId: string | null;
  isLoading: boolean;
  onSelectConversation: (id: string) => void;
  onArchiveConversation: (id: string) => void;
  onNewConversation: () => void;
  onToggleFavorite?: (id: string) => void;
}

export default function ConversationsList({
  conversations,
  activeConversationId,
  isLoading,
  onSelectConversation,
  onArchiveConversation,
  onNewConversation,
  onToggleFavorite,
}: ConversationsListProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const [showActionMenu, setShowActionMenu] = useState(false);
  const [renameModalVisible, setRenameModalVisible] = useState(false);
  const [newTitle, setNewTitle] = useState('');

  // Filter and sort conversations
  const filteredConversations = useMemo(() => {
    let filtered = conversations;
    
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = conversations.filter(conv => 
        conv.title.toLowerCase().includes(query) ||
        conv.contextType?.toLowerCase().includes(query) ||
        conv.lastMessage?.toLowerCase().includes(query)
      );
    }
    
    // Sort: favorites first, then by date
    return filtered.sort((a, b) => {
      if (a.isFavorite && !b.isFavorite) return -1;
      if (!a.isFavorite && b.isFavorite) return 1;
      return (b.lastMessageAt || b.updatedAt).getTime() - (a.lastMessageAt || a.updatedAt).getTime();
    });
  }, [conversations, searchQuery]);

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      // Today - show time
      return date.toLocaleTimeString('es-ES', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } else if (diffDays === 1) {
      return 'Ayer';
    } else if (diffDays < 7) {
      return date.toLocaleDateString('es-ES', { weekday: 'long' });
    } else {
      return date.toLocaleDateString('es-ES', { 
        day: 'numeric', 
        month: 'short' 
      });
    }
  };


  const handleArchive = (id: string) => {
    Alert.alert(
      'Archivar conversación',
      '¿Estás seguro de que quieres archivar esta conversación?',
      [
        { text: 'Cancelar', style: 'cancel' },
        { 
          text: 'Archivar', 
          style: 'destructive',
          onPress: () => {
            onArchiveConversation(id);
            logger.info('Conversation archived', { id });
          }
        }
      ]
    );
  };

  const handleToggleFavorite = (id: string) => {
    if (onToggleFavorite) {
      onToggleFavorite(id);
    }
  };

  const handleLongPress = (conversation: ChatConversation) => {
    setSelectedConversation(conversation.id);
    setNewTitle(conversation.title);
    setShowActionMenu(true);
  };

  const handleRename = async () => {
    if (!selectedConversation || !newTitle.trim()) return;
    
    const { updateConversation } = useChatStore.getState();
    await updateConversation(selectedConversation, { title: newTitle.trim() });
    setRenameModalVisible(false);
    setSelectedConversation(null);
  };

  const handleDeletePermanently = (id: string) => {
    Alert.alert(
      'Eliminar conversación',
      'Esta acción no se puede deshacer. ¿Estás seguro?',
      [
        { text: 'Cancelar', style: 'cancel' },
        { 
          text: 'Eliminar', 
          style: 'destructive',
          onPress: async () => {
            const { updateConversation } = useChatStore.getState();
            await updateConversation(id, { status: 'archived' });
            logger.info('Conversation permanently deleted', { id });
          }
        }
      ]
    );
  };

  const renderConversation = ({ item }: { item: ChatConversation }) => {
    const isActive = item.id === activeConversationId;
    const lastMessageDate = item.lastMessageAt || item.updatedAt;
    
    const conversationContent = (
      <TouchableOpacity
        style={[styles.conversationItem, isActive && styles.activeConversation]}
        onPress={() => onSelectConversation(item.id)}
        onLongPress={() => handleLongPress(item)}
        activeOpacity={0.7}
      >
        <View style={styles.conversationIcon}>
          <MessageCircle 
            size={20} 
            color={isActive ? Colors.light.primary : Colors.light.textSecondary} 
          />
          {item.isFavorite && (
            <View style={styles.favoriteIndicator}>
              <Star size={12} color={Colors.light.warning} fill={Colors.light.warning} />
            </View>
          )}
        </View>
        
        <View style={styles.conversationContent}>
          <Text 
            style={[styles.conversationTitle, isActive && styles.activeText]} 
            numberOfLines={1}
          >
            {item.title}
          </Text>
          
          {item.lastMessage && (
            <Text style={styles.lastMessage} numberOfLines={1}>
              {item.lastMessage}
            </Text>
          )}
          
          <View style={styles.conversationMeta}>
            {item.contextType && (
              <Text style={styles.contextType}>
                {item.contextType === 'client' && 'Cliente'}
                {item.contextType === 'service' && 'Servicio'}
                {item.contextType === 'formula' && 'Fórmula'}
                {item.contextType === 'inventory' && 'Inventario'}
              </Text>
            )}
            
            <Text style={styles.messageCount}>
              {item.messageCount || 0} mensajes
            </Text>
          </View>
        </View>
        
        <View style={styles.conversationRight}>
          <Text style={styles.conversationDate}>
            {formatDate(lastMessageDate)}
          </Text>
        </View>
      </TouchableOpacity>
    );

    return (
      <SwipeableRow
        onSwipeRight={() => handleArchive(item.id)}
        onSwipeLeft={() => handleToggleFavorite(item.id)}
        leftActionIcon={<Archive size={20} color={Colors.light.surface} />}
        rightActionIcon={item.isFavorite ? <Star size={20} color={Colors.light.surface} fill={Colors.light.surface} /> : <Star size={20} color={Colors.light.surface} />}
        rightActionColor={Colors.light.warning}
        leftActionColor={Colors.light.primary}
      >
        {conversationContent}
      </SwipeableRow>
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Conversaciones</Text>
        <TouchableOpacity 
          style={styles.newButton}
          onPress={onNewConversation}
        >
          <Text style={styles.newButtonText}>Nueva</Text>
        </TouchableOpacity>
      </View>

      {/* Search */}
      <View style={styles.searchContainer}>
        <Search size={20} color={Colors.light.textSecondary} />
        <TextInput
          style={styles.searchInput}
          placeholder="Buscar conversaciones..."
          placeholderTextColor={Colors.light.textSecondary}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <X size={20} color={Colors.light.textSecondary} />
          </TouchableOpacity>
        )}
      </View>

      {/* Conversations List */}
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.light.primary} />
        </View>
      ) : filteredConversations.length === 0 ? (
        <View style={styles.emptyContainer}>
          <MessageCircle size={48} color={Colors.light.textSecondary} />
          <Text style={styles.emptyTitle}>
            {searchQuery ? 'Sin resultados' : 'Sin conversaciones'}
          </Text>
          <Text style={styles.emptyText}>
            {searchQuery 
              ? 'Intenta con otros términos de búsqueda'
              : 'Inicia una nueva conversación con el asistente'
            }
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredConversations}
          keyExtractor={(item) => item.id}
          renderItem={renderConversation}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
        />
      )}
      
      {/* Action Menu Modal */}
      <Modal
        visible={showActionMenu}
        transparent
        animationType="fade"
        onRequestClose={() => setShowActionMenu(false)}
      >
        <TouchableOpacity 
          style={styles.modalOverlay} 
          activeOpacity={1}
          onPress={() => setShowActionMenu(false)}
        >
          <View style={styles.actionSheet}>
            <TouchableOpacity
              style={styles.actionItem}
              onPress={() => {
                setShowActionMenu(false);
                setRenameModalVisible(true);
              }}
            >
              <Text style={styles.actionText}>✏️ Renombrar conversación</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.actionItem}
              onPress={() => {
                setShowActionMenu(false);
                if (selectedConversation) {
                  handleToggleFavorite(selectedConversation);
                }
              }}
            >
              <Text style={styles.actionText}>⭐ Marcar como favorito</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.actionItem}
              onPress={() => {
                setShowActionMenu(false);
                // TODO: Implement copy history
                Alert.alert('Próximamente', 'Esta función estará disponible pronto');
              }}
            >
              <Text style={styles.actionText}>📋 Copiar historial</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.actionItem, styles.destructiveAction]}
              onPress={() => {
                setShowActionMenu(false);
                if (selectedConversation) {
                  handleDeletePermanently(selectedConversation);
                }
              }}
            >
              <Text style={[styles.actionText, styles.destructiveText]}>🗑️ Eliminar permanentemente</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.actionItem, styles.cancelAction]}
              onPress={() => setShowActionMenu(false)}
            >
              <Text style={styles.actionText}>Cancelar</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>
      
      {/* Rename Modal */}
      <Modal
        visible={renameModalVisible}
        transparent
        animationType="slide"
      >
        <View style={styles.modalOverlay}>
          <View style={styles.renameModal}>
            <Text style={styles.modalTitle}>Renombrar conversación</Text>
            <TextInput
              style={styles.renameInput}
              value={newTitle}
              onChangeText={setNewTitle}
              placeholder="Nuevo título"
              placeholderTextColor={Colors.light.textSecondary}
              autoFocus
            />
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => {
                  setRenameModalVisible(false);
                  setSelectedConversation(null);
                }}
              >
                <Text style={styles.buttonText}>Cancelar</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.confirmButton]}
                onPress={handleRename}
              >
                <Text style={[styles.buttonText, styles.confirmButtonText]}>Guardar</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  headerTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  newButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    backgroundColor: Colors.light.primary,
    borderRadius: radius.md,
  },
  newButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.surface,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: Colors.light.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  searchInput: {
    flex: 1,
    marginHorizontal: spacing.sm,
    fontSize: typography.sizes.md,
    color: Colors.light.text,
  },
  listContent: {
    paddingVertical: spacing.xs,
  },
  conversationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    backgroundColor: Colors.light.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  activeConversation: {
    backgroundColor: `${Colors.light.primary}10`,
  },
  conversationIcon: {
    width: 40,
    height: 40,
    borderRadius: radius.full,
    backgroundColor: Colors.light.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.sm,
    position: 'relative',
  },
  favoriteIndicator: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: Colors.light.surface,
    borderRadius: radius.full,
    padding: 2,
  },
  conversationContent: {
    flex: 1,
    marginRight: spacing.sm,
  },
  conversationTitle: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  activeText: {
    color: Colors.light.primary,
  },
  lastMessage: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginTop: 2,
    marginBottom: spacing.xs,
  },
  conversationMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  contextType: {
    fontSize: typography.sizes.xs,
    color: Colors.light.primary,
    backgroundColor: `${Colors.light.primary}10`,
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: radius.sm,
  },
  messageCount: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
  },
  conversationRight: {
    alignItems: 'flex-end',
  },
  conversationDate: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xxl,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.xxl,
  },
  emptyTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginTop: spacing.md,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: typography.sizes.md,
    color: Colors.light.textSecondary,
    marginTop: spacing.sm,
    textAlign: 'center',
    lineHeight: 22,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionSheet: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    marginHorizontal: spacing.xl,
    padding: spacing.sm,
    width: '90%',
    maxWidth: 400,
  },
  actionItem: {
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderRadius: radius.md,
  },
  actionText: {
    fontSize: typography.sizes.md,
    color: Colors.light.text,
  },
  destructiveAction: {
    backgroundColor: `${Colors.light.error}10`,
  },
  destructiveText: {
    color: Colors.light.error,
  },
  cancelAction: {
    marginTop: spacing.xs,
    backgroundColor: Colors.light.background,
  },
  renameModal: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    padding: spacing.xl,
    width: '90%',
    maxWidth: 400,
  },
  modalTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  renameInput: {
    backgroundColor: Colors.light.background,
    borderRadius: radius.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: typography.sizes.md,
    color: Colors.light.text,
    marginBottom: spacing.lg,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  modalButton: {
    flex: 1,
    paddingVertical: spacing.sm,
    borderRadius: radius.md,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: Colors.light.background,
  },
  confirmButton: {
    backgroundColor: Colors.light.primary,
  },
  buttonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
  },
  confirmButtonText: {
    color: Colors.light.surface,
  },
});
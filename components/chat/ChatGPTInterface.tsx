import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Alert,
  Image,
  Dimensions,
} from 'react-native';
import { Send, Camera, Image as ImageIcon, Sparkles, Zap, Users, Package, Menu, X } from 'lucide-react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import * as ImagePicker from 'expo-image-picker';
import Colors from '@/constants/colors';
import { spacing, typography, radius } from '@/constants/theme';
import { useChatStore, ChatMessage } from '@/stores/chat-store';
import { useAuthStore } from '@/stores/auth-store';
import { useClientStore } from '@/stores/client-store';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import SmartSuggestions from './SmartSuggestions';
import TypingIndicator from './TypingIndicator';
import ConversationsList from './ConversationsList';
import ImagePickerModal from './ImagePickerModal';

interface ChatGPTInterfaceProps {
  conversationId?: string;
  contextType?: 'general' | 'client' | 'service' | 'formula' | 'inventory';
  contextId?: string;
  contextData?: any;
  onClose?: () => void;
  isModal?: boolean;
}

export default function ChatGPTInterface({
  conversationId: propConversationId,
  contextType,
  contextId,
  contextData,
  onClose,
  isModal = false,
}: ChatGPTInterfaceProps) {
  const insets = useSafeAreaInsets();
  const scrollViewRef = useRef<ScrollView>(null);
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [showSidebar, setShowSidebar] = useState(false);
  const [showImagePicker, setShowImagePicker] = useState(false);
  const { user } = useAuthStore();

  // Check if device is tablet
  const { width } = Dimensions.get('window');
  const IS_TABLET = width >= 768;

  // Chat store
  const {
    conversations,
    messages,
    activeConversationId,
    isSending,
    isLoading,
    error,
    sendMessage,
    loadMessages,
    loadConversations,
    setActiveConversation,
    createConversation,
    archiveConversation,
    deleteConversation,
    toggleFavorite,
  } = useChatStore();

  // Context stores
  const { clients } = useClientStore();
  const { salonConfig } = useSalonConfigStore();

  const currentMessages = activeConversationId ? messages[activeConversationId] || [] : [];

  // Initialize sidebar for tablets
  useEffect(() => {
    if (IS_TABLET) {
      setShowSidebar(true);
    }
  }, [IS_TABLET]);

  // Initialize conversation
  useEffect(() => {
    const initializeChat = async () => {
      // Load conversations first
      await loadConversations();

      if (propConversationId) {
        setActiveConversation(propConversationId);
        await loadMessages(propConversationId);
      } else if (contextType && contextId) {
        // Check if conversation exists for this context
        const existingConv = conversations.find(
          conv => conv.contextType === contextType && conv.contextId === contextId
        );

        if (existingConv) {
          setActiveConversation(existingConv.id);
          await loadMessages(existingConv.id);
        } else {
          // Create new conversation with context
          const title = generateWelcomeTitle();
          const newConv = await createConversation({
            title,
            contextType,
            contextId,
            metadata: { contextData: contextData || {} },
          });
          if (newConv) {
            setActiveConversation(newConv.id);
          }
        }
      } else if (!activeConversationId && conversations.length > 0) {
        // Select the most recent conversation
        const mostRecent = conversations[0];
        setActiveConversation(mostRecent.id);
        await loadMessages(mostRecent.id);
      }
    };

    initializeChat();
  }, [propConversationId, contextType, contextId]);

  // Generate contextual welcome title
  const generateWelcomeTitle = () => {
    if (contextType === 'client' && contextData?.name) {
      return `Consulta sobre ${contextData.name}`;
    }
    if (contextType === 'service') {
      return 'Consulta sobre servicio';
    }
    if (contextType === 'formula') {
      return 'Consulta sobre fórmula';
    }
    if (contextType === 'inventory') {
      return 'Consulta de inventario';
    }
    return 'Nueva conversación';
  };

  // Generate intelligent conversation starters
  const getConversationStarters = () => {
    const starters = [];
    
    // Context-based starters
    if (contextType === 'client' && contextData) {
      starters.push(`¿Qué técnica recomiendas para ${contextData.name}?`);
      starters.push(`Historial de fórmulas para ${contextData.name}`);
    }
    
    // General professional starters
    starters.push('¿Cómo corrijo un color naranja?');
    starters.push('Fórmula para rubio ceniza nivel 8');
    starters.push('¿Qué volumen de peróxido usar?');
    starters.push('Técnicas para cabello dañado');
    
    // Inventory-based starters
    if (salonConfig?.lowStockProducts?.length > 0) {
      starters.push('¿Qué productos necesito reponer?');
    }
    
    return starters.slice(0, 4);
  };

  // Handle message send
  const handleSend = async () => {
    if (!message.trim() || isSending) return;

    const messageToSend = message.trim();
    setMessage('');

    try {
      await sendMessage(messageToSend, activeConversationId);
      
      // Scroll to bottom after sending
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert('Error', 'No se pudo enviar el mensaje. Inténtalo de nuevo.');
    }
  };

  // Handle typing
  const handleTextChange = (text: string) => {
    setMessage(text);
    setIsTyping(text.length > 0);
  };

  // Handle suggestion selection
  const handleSuggestionSelect = (suggestion: string) => {
    setMessage(suggestion);
  };

  // Handle conversation selection
  const handleSelectConversation = async (id: string) => {
    setActiveConversation(id);
    await loadMessages(id);
    if (!IS_TABLET) {
      setShowSidebar(false);
    }
  };

  // Handle new conversation
  const handleCreateNewConversation = async () => {
    const title = 'Nueva conversación';
    const newConv = await createConversation({ title });
    if (newConv) {
      setActiveConversation(newConv.id);
      if (!IS_TABLET) {
        setShowSidebar(false);
      }
    }
  };

  // Toggle sidebar
  const toggleSidebar = () => {
    setShowSidebar(!showSidebar);
  };

  // Image picker handlers
  const handleCameraSelect = async () => {
    console.log('handleCameraSelect called');
    handleImagePicker('camera');
  };

  const handleGallerySelect = async () => {
    console.log('handleGallerySelect called');
    handleImagePicker('library');
  };

  const handleCloseImagePicker = () => {
    console.log('handleCloseImagePicker called');
    setShowImagePicker(false);
  };

  // Quick Actions handlers
  const handleQuickAction = async (action: string) => {
    switch (action) {
      case 'photo-analysis':
        setShowImagePicker(true);
        break;
      case 'quick-formula':
        setMessage('Genera una fórmula rápida para cabello nivel 6 natural');
        await handleSend();
        break;
      case 'client-consultation':
        if (clients.length > 0) {
          const recentClient = clients[0];
          setMessage(`Consulta sobre mi cliente ${recentClient.name}: historial y recomendaciones`);
          await handleSend();
        } else {
          setMessage('¿Cómo puedo gestionar mejor el historial de mis clientes?');
          await handleSend();
        }
        break;
      case 'inventory-check':
        setMessage('Revisa mi inventario actual y productos con stock bajo');
        await handleSend();
        break;
      default:
        break;
    }
  };

  // Image picker handler
  const handleImagePicker = async (source: 'camera' | 'library') => {
    console.log('handleImagePicker called with source:', source);

    try {
      if (source === 'camera') {
        console.log('Requesting camera permissions...');
        const { status: cameraStatus } = await ImagePicker.requestCameraPermissionsAsync();
        console.log('Camera permission status:', cameraStatus);

        if (cameraStatus !== 'granted') {
          Alert.alert('Permisos requeridos', 'Necesitamos acceso a tu cámara para tomar fotos.');
          return;
        }

        // Check if camera is available
        const { status: cameraAvailable } = await ImagePicker.getCameraPermissionsAsync();
        console.log('Camera available status:', cameraAvailable);

        console.log('Launching camera...');

        // Add timeout and more detailed logging
        const cameraPromise = ImagePicker.launchCameraAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsEditing: false,
          quality: 0.8,
          base64: false,
          exif: false,
        });

        console.log('Camera promise created, waiting for result...');

        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => {
            console.log('Camera timeout reached');
            reject(new Error('Camera timeout after 5 seconds'));
          }, 5000)
        );

        const result = await Promise.race([cameraPromise, timeoutPromise]);
        console.log('Camera operation completed');

        console.log('Camera result:', result);
        if (!result.canceled && result.assets && result.assets[0]) {
          console.log('Camera image selected, processing...');
          const asset = result.assets[0];
          console.log('Asset details:', {
            uri: asset.uri,
            width: asset.width,
            height: asset.height,
            type: asset.type
          });
          await handleImageUpload(asset);
        } else {
          console.log('Camera was canceled or no asset returned');
        }
      } else {
        console.log('Requesting media library permissions...');
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        console.log('Media library permission status:', status);

        if (status !== 'granted') {
          Alert.alert('Permisos requeridos', 'Necesitamos acceso a tu galería para subir fotos.');
          return;
        }

        console.log('Launching image library...');
        const result = await ImagePicker.launchImageLibraryAsync({
          quality: 0.8,
        });

        console.log('Library result:', result);
        if (!result.canceled && result.assets && result.assets[0]) {
          console.log('Library image selected, processing...');
          const asset = result.assets[0];
          console.log('Asset details:', {
            uri: asset.uri,
            width: asset.width,
            height: asset.height,
            type: asset.type
          });
          await handleImageUpload(asset);
        } else {
          console.log('Library was canceled or no asset returned');
        }
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', `Error: ${error.message || error}`);
    }
  };

  // Handle image upload and analysis
  const handleImageUpload = async (asset: any) => {
    try {
      // Create a message with the image
      const imageDataUrl = `data:image/jpeg;base64,${asset.base64}`;

      // Send message with image attachment
      const attachments = [{
        type: 'image' as const,
        url: imageDataUrl,
        mimeType: 'image/jpeg',
      }];

      await sendMessage('Analiza esta imagen de cabello y proporciona un diagnóstico detallado', activeConversationId, attachments);

      // Scroll to bottom after sending
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    } catch (error) {
      console.error('Error uploading image:', error);
      Alert.alert('Error', 'No se pudo procesar la imagen. Inténtalo de nuevo.');
    }
  };

  // Render welcome screen
  const renderWelcomeScreen = () => {
    const starters = getConversationStarters();
    
    return (
      <View style={styles.welcomeContainer}>
        <View style={styles.welcomeHeader}>
          <Sparkles size={48} color={Colors.light.primary} />
          <Text style={styles.welcomeTitle}>Salonier Assistant</Text>
          <Text style={styles.welcomeSubtitle}>
            Tu experto en colorimetría capilar
          </Text>
        </View>

        <View style={styles.startersContainer}>
          <Text style={styles.startersTitle}>¿En qué puedo ayudarte?</Text>
          {starters.map((starter, index) => (
            <TouchableOpacity
              key={index}
              style={styles.starterButton}
              onPress={() => {
                setMessage(starter);
                handleSend();
              }}
            >
              <Text style={styles.starterText}>{starter}</Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActionsContainer}>
          <Text style={styles.quickActionsTitle}>Acciones rápidas</Text>
          <View style={styles.quickActionsGrid}>
            <TouchableOpacity
              style={styles.quickAction}
              onPress={() => handleQuickAction('photo-analysis')}
              activeOpacity={0.7}
            >
              <Camera size={24} color={Colors.light.primary} />
              <Text style={styles.quickActionText}>Analizar foto</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.quickAction}
              onPress={() => handleQuickAction('quick-formula')}
              activeOpacity={0.7}
            >
              <Zap size={24} color={Colors.light.primary} />
              <Text style={styles.quickActionText}>Generar fórmula</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.quickAction}
              onPress={() => handleQuickAction('client-consultation')}
              activeOpacity={0.7}
            >
              <Users size={24} color={Colors.light.primary} />
              <Text style={styles.quickActionText}>Consultar cliente</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.quickAction}
              onPress={() => handleQuickAction('inventory-check')}
              activeOpacity={0.7}
            >
              <Package size={24} color={Colors.light.primary} />
              <Text style={styles.quickActionText}>Ver inventario</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  // Render message bubble
  const renderMessage = (msg: ChatMessage, index: number) => {
    const isUser = msg.role === 'user';

    return (
      <View
        key={msg.id}
        style={[
          styles.messageContainer,
          isUser ? styles.userMessageContainer : styles.assistantMessageContainer,
        ]}
      >
        {!isUser && (
          <View style={styles.assistantAvatar}>
            <Sparkles size={16} color={Colors.light.primary} />
          </View>
        )}

        <View
          style={[
            styles.messageBubble,
            isUser ? styles.userBubble : styles.assistantBubble,
          ]}
        >
          {/* Render attachments if any */}
          {msg.attachments && msg.attachments.length > 0 && (
            <View style={styles.attachmentsContainer}>
              {msg.attachments.map((attachment, attachIndex) => (
                <View key={attachIndex} style={styles.attachmentContainer}>
                  {attachment.type === 'image' && (
                    <Image
                      source={{ uri: attachment.url }}
                      style={styles.attachmentImage}
                      resizeMode="cover"
                    />
                  )}
                </View>
              ))}
            </View>
          )}

          {/* Render message text */}
          {msg.content && (
            <Text
              style={[
                styles.messageText,
                isUser ? styles.userMessageText : styles.assistantMessageText,
              ]}
            >
              {msg.content}
            </Text>
          )}
        </View>
      </View>
    );
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={isModal ? 0 : insets.top}
    >
      {/* Sidebar for tablets or when shown */}
      {(IS_TABLET || showSidebar) && (
        <>
          {!IS_TABLET && showSidebar && (
            <TouchableOpacity
              style={styles.sidebarBackdrop}
              activeOpacity={1}
              onPress={() => setShowSidebar(false)}
            />
          )}
          <View style={[styles.sidebar, !IS_TABLET && styles.sidebarOverlay]}>
            <ConversationsList
              conversations={conversations}
              activeConversationId={activeConversationId}
              isLoading={isLoading}
              onSelectConversation={handleSelectConversation}
              onArchiveConversation={archiveConversation}
              onDeleteConversation={deleteConversation}
              onNewConversation={handleCreateNewConversation}
              onToggleFavorite={toggleFavorite}
            />
          </View>
        </>
      )}

      {/* Main chat area */}
      <View style={[styles.mainContent, (IS_TABLET || showSidebar) && styles.mainContentWithSidebar]}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            {!IS_TABLET && (
              <TouchableOpacity onPress={toggleSidebar} style={styles.menuButton}>
                <Menu size={24} color={Colors.light.text} />
              </TouchableOpacity>
            )}
            <Sparkles size={24} color={Colors.light.primary} />
            <View style={styles.headerTextContainer}>
              <Text style={styles.headerTitle}>Salonier Assistant</Text>
              {contextType && (
                <Text style={styles.headerSubtitle}>
                  {contextType === 'client' && 'Consulta sobre cliente'}
                  {contextType === 'service' && 'Consulta sobre servicio'}
                  {contextType === 'formula' && 'Consulta sobre fórmula'}
                  {contextType === 'inventory' && 'Consulta de inventario'}
                </Text>
              )}
            </View>
            {isModal && onClose && (
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <X size={24} color={Colors.light.text} />
              </TouchableOpacity>
            )}
          </View>
        </View>

      {/* Messages */}
      <ScrollView
        ref={scrollViewRef}
        style={styles.messagesContainer}
        contentContainerStyle={styles.messagesContent}
        showsVerticalScrollIndicator={false}
      >
        {currentMessages.length === 0 ? (
          renderWelcomeScreen()
        ) : (
          <>
            {currentMessages.map((msg, index) => renderMessage(msg, index))}
            <TypingIndicator visible={isSending} />
          </>
        )}
      </ScrollView>

      {/* Smart Suggestions */}
      <SmartSuggestions
        input={message}
        onSuggestionSelect={handleSuggestionSelect}
        contextType={contextType}
        contextData={contextData}
      />

      {/* Input */}
      <View style={styles.inputContainer}>
        <View style={styles.inputWrapper}>
          <TouchableOpacity
            style={styles.attachButton}
            onPress={() => setShowImagePicker(true)}
            disabled={isSending}
          >
            <Camera size={20} color={isSending ? Colors.light.textSecondary : Colors.light.primary} />
          </TouchableOpacity>

          <TextInput
            style={styles.input}
            value={message}
            onChangeText={handleTextChange}
            placeholder="Escribe tu consulta sobre colorimetría..."
            placeholderTextColor={Colors.light.textSecondary}
            multiline
            maxHeight={100}
            editable={!isSending}
          />

          <TouchableOpacity
            style={[
              styles.sendButton,
              (message.trim() && !isSending) ? styles.sendButtonActive : styles.sendButtonInactive
            ]}
            onPress={handleSend}
            disabled={!message.trim() || isSending}
          >
            {isSending ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <Send size={20} color="white" />
            )}
          </TouchableOpacity>
        </View>
      </View>
      </View>

      {/* Image Picker Modal */}
      <ImagePickerModal
        visible={showImagePicker}
        onClose={handleCloseImagePicker}
        onSelectCamera={handleCameraSelect}
        onSelectGallery={handleGallerySelect}
      />
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
    flexDirection: 'row',
  },

  // Sidebar
  sidebar: {
    width: 300,
    backgroundColor: Colors.light.surface,
    borderRightWidth: 1,
    borderRightColor: Colors.light.border,
  },
  sidebarOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    zIndex: 1000,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 2, height: 0 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  sidebarBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 999,
  },

  // Main content
  mainContent: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  mainContentWithSidebar: {
    marginLeft: 0, // Will be handled by flexDirection: 'row'
  },

  // Header
  header: {
    backgroundColor: Colors.light.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuButton: {
    padding: spacing.sm,
    marginRight: spacing.sm,
  },
  headerTextContainer: {
    marginLeft: spacing.sm,
    flex: 1,
  },
  closeButton: {
    padding: spacing.sm,
    marginLeft: spacing.sm,
  },
  headerTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  headerSubtitle: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginTop: 2,
  },

  // Welcome Screen
  welcomeContainer: {
    flex: 1,
    padding: spacing.lg,
    justifyContent: 'center',
  },
  welcomeHeader: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  welcomeTitle: {
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    marginTop: spacing.md,
    textAlign: 'center',
  },
  welcomeSubtitle: {
    fontSize: typography.sizes.md,
    color: Colors.light.textSecondary,
    marginTop: spacing.xs,
    textAlign: 'center',
  },

  // Conversation Starters
  startersContainer: {
    marginBottom: spacing.xl,
  },
  startersTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.md,
  },
  starterButton: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  starterText: {
    fontSize: typography.sizes.md,
    color: Colors.light.text,
  },

  // Quick Actions
  quickActionsContainer: {
    marginTop: spacing.lg,
  },
  quickActionsTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.md,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickAction: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.md,
    padding: spacing.md,
    alignItems: 'center',
    width: '48%',
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  quickActionText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.text,
    marginTop: spacing.xs,
    textAlign: 'center',
  },

  // Messages
  messagesContainer: {
    flex: 1,
  },
  messagesContent: {
    padding: spacing.md,
    paddingBottom: spacing.xl,
  },
  messageContainer: {
    marginBottom: spacing.md,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  userMessageContainer: {
    justifyContent: 'flex-end',
  },
  assistantMessageContainer: {
    justifyContent: 'flex-start',
  },

  // Assistant Avatar
  assistantAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.light.primary + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
    marginTop: spacing.xs,
  },

  // Message Bubbles
  messageBubble: {
    maxWidth: '80%',
    borderRadius: radius.lg,
    padding: spacing.md,
  },
  userBubble: {
    backgroundColor: Colors.light.primary,
    marginLeft: spacing.xl,
  },
  assistantBubble: {
    backgroundColor: Colors.light.surface,
    borderWidth: 1,
    borderColor: Colors.light.border,
    marginRight: spacing.xl,
  },
  messageText: {
    fontSize: typography.sizes.md,
    lineHeight: typography.sizes.md * 1.4,
  },
  userMessageText: {
    color: 'white',
  },
  assistantMessageText: {
    color: Colors.light.text,
  },

  // Attachments
  attachmentsContainer: {
    marginBottom: spacing.sm,
  },
  attachmentContainer: {
    marginBottom: spacing.xs,
  },
  attachmentImage: {
    width: 200,
    height: 150,
    borderRadius: radius.md,
    backgroundColor: Colors.light.border,
  },





  // Input
  inputContainer: {
    backgroundColor: Colors.light.surface,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: Colors.light.background,
    borderRadius: radius.lg,
    borderWidth: 1,
    borderColor: Colors.light.border,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
  },
  attachButton: {
    padding: spacing.sm,
    marginRight: spacing.xs,
  },
  input: {
    flex: 1,
    fontSize: typography.sizes.md,
    color: Colors.light.text,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.xs,
    maxHeight: 100,
  },
  sendButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: spacing.xs,
  },
  sendButtonActive: {
    backgroundColor: Colors.light.primary,
  },
  sendButtonInactive: {
    backgroundColor: Colors.light.textSecondary,
  },
});

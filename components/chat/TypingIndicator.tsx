import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Easing,
} from 'react-native';
import { Sparkles } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { spacing, typography, radius } from '@/constants/theme';

interface TypingIndicatorProps {
  visible: boolean;
  message?: string;
}

export default function TypingIndicator({ 
  visible, 
  message = "Salonier está escribiendo..." 
}: TypingIndicatorProps) {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const dot1Anim = useRef(new Animated.Value(0)).current;
  const dot2Anim = useRef(new Animated.Value(0)).current;
  const dot3Anim = useRef(new Animated.Value(0)).current;
  const sparkleRotation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (visible) {
      // Fade in animation
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();

      // Sparkle rotation animation
      Animated.loop(
        Animated.timing(sparkleRotation, {
          toValue: 1,
          duration: 2000,
          easing: Easing.linear,
          useNativeDriver: true,
        })
      ).start();

      // Dots animation
      const animateDots = () => {
        const dotAnimation = (animValue: Animated.Value, delay: number) => {
          return Animated.loop(
            Animated.sequence([
              Animated.delay(delay),
              Animated.timing(animValue, {
                toValue: 1,
                duration: 400,
                easing: Easing.bezier(0.4, 0.0, 0.6, 1.0),
                useNativeDriver: true,
              }),
              Animated.timing(animValue, {
                toValue: 0,
                duration: 400,
                easing: Easing.bezier(0.4, 0.0, 0.6, 1.0),
                useNativeDriver: true,
              }),
            ])
          );
        };

        Animated.parallel([
          dotAnimation(dot1Anim, 0),
          dotAnimation(dot2Anim, 200),
          dotAnimation(dot3Anim, 400),
        ]).start();
      };

      animateDots();
    } else {
      // Fade out animation
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  }, [visible]);

  const sparkleRotationInterpolate = sparkleRotation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const getDotStyle = (animValue: Animated.Value) => ({
    transform: [
      {
        scale: animValue.interpolate({
          inputRange: [0, 1],
          outputRange: [0.8, 1.2],
        }),
      },
      {
        translateY: animValue.interpolate({
          inputRange: [0, 1],
          outputRange: [0, -4],
        }),
      },
    ],
    opacity: animValue.interpolate({
      inputRange: [0, 1],
      outputRange: [0.5, 1],
    }),
  });

  if (!visible) return null;

  return (
    <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
      <View style={styles.messageContainer}>
        <View style={styles.avatarContainer}>
          <Animated.View
            style={[
              styles.avatar,
              {
                transform: [{ rotate: sparkleRotationInterpolate }],
              },
            ]}
          >
            <Sparkles size={16} color={Colors.light.primary} />
          </Animated.View>
        </View>
        
        <View style={styles.bubbleContainer}>
          <View style={styles.bubble}>
            <View style={styles.textContainer}>
              <Text style={styles.message}>{message}</Text>
            </View>
            
            <View style={styles.dotsContainer}>
              <Animated.View style={[styles.dot, getDotStyle(dot1Anim)]} />
              <Animated.View style={[styles.dot, getDotStyle(dot2Anim)]} />
              <Animated.View style={[styles.dot, getDotStyle(dot3Anim)]} />
            </View>
          </View>
        </View>
      </View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  messageContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  avatarContainer: {
    marginRight: spacing.sm,
    marginTop: spacing.xs,
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.light.primary + '20',
    alignItems: 'center',
    justifyContent: 'center',
  },
  bubbleContainer: {
    flex: 1,
    maxWidth: '80%',
  },
  bubble: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    borderWidth: 1,
    borderColor: Colors.light.border,
    padding: spacing.md,
    shadowColor: Colors.light.text,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  textContainer: {
    marginBottom: spacing.sm,
  },
  message: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    fontStyle: 'italic',
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  dot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: Colors.light.primary,
    marginRight: spacing.xs,
  },
});

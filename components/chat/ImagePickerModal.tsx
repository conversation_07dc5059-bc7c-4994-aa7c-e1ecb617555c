import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Alert,
} from 'react-native';
import { Camera, Image as ImageIcon, X } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { spacing, typography, radius } from '@/constants/theme';

interface ImagePickerModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectCamera: () => void;
  onSelectGallery: () => void;
}

export default function ImagePickerModal({
  visible,
  onClose,
  onSelectCamera,
  onSelectGallery,
}: ImagePickerModalProps) {
  const handleCameraPress = () => {
    onClose();
    setTimeout(() => {
      onSelectCamera();
    }, 100);
  };

  const handleGalleryPress = () => {
    onClose();
    setTimeout(() => {
      onSelectGallery();
    }, 100);
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          <View style={styles.header}>
            <Text style={styles.title}>Seleccionar imagen</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color={Colors.light.text} />
            </TouchableOpacity>
          </View>

          <Text style={styles.subtitle}>
            Elige una opción para analizar el cabello
          </Text>

          <View style={styles.optionsContainer}>
            <TouchableOpacity
              style={styles.option}
              onPress={handleCameraPress}
              activeOpacity={0.7}
            >
              <View style={styles.optionIcon}>
                <Camera size={32} color={Colors.light.primary} />
              </View>
              <Text style={styles.optionTitle}>Tomar foto</Text>
              <Text style={styles.optionDescription}>
                Usa la cámara para capturar una imagen del cabello
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.option}
              onPress={handleGalleryPress}
              activeOpacity={0.7}
            >
              <View style={styles.optionIcon}>
                <ImageIcon size={32} color={Colors.light.primary} />
              </View>
              <Text style={styles.optionTitle}>Elegir de galería</Text>
              <Text style={styles.optionDescription}>
                Selecciona una imagen existente de tu galería
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.tipsContainer}>
            <Text style={styles.tipsTitle}>💡 Consejos para mejores resultados:</Text>
            <Text style={styles.tip}>• Buena iluminación natural</Text>
            <Text style={styles.tip}>• Cabello limpio y desenredado</Text>
            <Text style={styles.tip}>• Enfoque claro en el color</Text>
            <Text style={styles.tip}>• Múltiples ángulos si es posible</Text>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  container: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    padding: spacing.lg,
    width: '100%',
    maxWidth: 400,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  title: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  closeButton: {
    padding: spacing.xs,
  },
  subtitle: {
    fontSize: typography.sizes.md,
    color: Colors.light.textSecondary,
    marginBottom: spacing.lg,
    textAlign: 'center',
  },
  optionsContainer: {
    marginBottom: spacing.lg,
  },
  option: {
    backgroundColor: Colors.light.background,
    borderRadius: radius.md,
    padding: spacing.lg,
    marginBottom: spacing.md,
    borderWidth: 1,
    borderColor: Colors.light.border,
    alignItems: 'center',
  },
  optionIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: Colors.light.primary + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.md,
  },
  optionTitle: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  optionDescription: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    lineHeight: typography.sizes.sm * 1.4,
  },
  tipsContainer: {
    backgroundColor: Colors.light.primary + '10',
    borderRadius: radius.md,
    padding: spacing.md,
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.primary,
  },
  tipsTitle: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.sm,
  },
  tip: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginBottom: spacing.xs,
    paddingLeft: spacing.sm,
  },
});

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Modal,
  TextInput,
  ActivityIndicator,
  Dimensions,
  Platform
} from 'react-native';
// import { LinearGradient } from 'expo-linear-gradient'; // Removed for consistency
import { Edit3, Eye, X, Play } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { VisualFormulationData, ParseResult } from '@/types/visual-formulation';
import { parseVisualFormula } from '@/utils/visualFormulaParser';
import ConversionBadge from './ConversionBadge';
import InstructionsFlow from './InstructionsFlow';

interface FormulaVisualizationProps {
  formulaText: string;
  onFormulaChange?: (newText: string) => void;
  editable?: boolean;
  context?: {
    clientName?: string;
    serviceDate?: string;
    currentLevel?: number;
    targetLevel?: number;
    currentTone?: string;
    targetTone?: string;
    levelDifference?: number;
    correctionNeeded?: boolean;
  };
}

const { width: screenWidth } = Dimensions.get('window');

export default function FormulaVisualization({
  formulaText,
  onFormulaChange,
  editable = true,
  context
}: FormulaVisualizationProps) {
  const [visualData, setVisualData] = useState<VisualFormulationData | null>(null);
  const [parseErrors, setParseErrors] = useState<string[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editedText, setEditedText] = useState(formulaText);
  const [isLoading, setIsLoading] = useState(true);
  const [showInstructionsFlow, setShowInstructionsFlow] = useState(false);

  useEffect(() => {
    console.log('[FormulaVisualization] formulaText changed:', formulaText?.substring(0, 50));
    parseFormula();
  }, [formulaText]);

  const parseFormula = async () => {
    setIsLoading(true);
    try {
      const result: ParseResult = parseVisualFormula(formulaText, {
        language: 'es',
        measurementSystem: 'metric',
        includeDefaults: true
      }, context);

      if (result.success && result.data) {
        console.log('[FormulaVisualization] Parse success, zones:', result.data.zones.length);
        setVisualData(result.data);
        setParseErrors(result.warnings || []);
      } else {
        console.log('[FormulaVisualization] Parse failed:', result.errors);
        setParseErrors(result.errors || ['Error al procesar la fórmula']);
      }
    } catch (error) {
      setParseErrors(['Error inesperado al procesar la fórmula']);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveEdit = () => {
    if (onFormulaChange) {
      onFormulaChange(editedText);
    }
    setIsEditing(false);
    parseFormula();
  };

  const handleCancelEdit = () => {
    setEditedText(formulaText);
    setIsEditing(false);
  };

  if (isLoading) {
    console.log('[FormulaVisualization] Rendering loading state');
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.light.primary} />
        <Text style={styles.loadingText}>Procesando fórmula...</Text>
      </View>
    );
  }

  if (!visualData) {
    console.log('[FormulaVisualization] Rendering error state, visualData is null');
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorTitle}>Error al visualizar la fórmula</Text>
        {parseErrors.map((error, index) => (
          <Text key={index} style={styles.errorText}>{error}</Text>
        ))}
        {editable && (
          <TouchableOpacity 
            style={styles.editButton} 
            onPress={() => setIsEditing(true)}
          >
            <Edit3 size={16} color="white" />
            <Text style={styles.editButtonText}>Editar Texto</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }
  
  console.log('[FormulaVisualization] Rendering main view with Play button');

  return (
    <View style={styles.container}>
      {/* Header with client info and edit button */}
      <View style={[styles.header, { backgroundColor: Colors.light.primary }]}>
        <View style={styles.headerContent}>
          <View>
            <Text style={styles.headerTitle}>FÓRMULA DE COLORACIÓN PROFESIONAL</Text>
            {visualData.clientName && (
              <Text style={styles.clientInfo}>
                Cliente: {visualData.clientName} | {visualData.serviceDate || new Date().toLocaleDateString()}
              </Text>
            )}
          </View>
          {editable && (
            <TouchableOpacity 
              style={styles.editIconButton}
              onPress={() => setIsEditing(true)}
            >
              <Edit3 size={20} color="white" />
            </TouchableOpacity>
          )}
        </View>
        {visualData.conversion && <ConversionBadge conversion={visualData.conversion} />}
      </View>

      {/* Prominent Play Button */}
      <TouchableOpacity 
        style={styles.mainPlayButton}
        onPress={() => setShowInstructionsFlow(true)}
        activeOpacity={0.8}
      >
        <View style={[styles.playButtonGradient, { backgroundColor: Colors.light.primary }]}>
          <Play size={28} color="white" />
          <Text style={styles.playButtonText}>Ver Instrucciones Paso a Paso</Text>
        </View>
      </TouchableOpacity>


      {/* Edit Modal */}
      <Modal
        visible={isEditing}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={handleCancelEdit}
      >
        <View style={styles.editModal}>
          <View style={styles.editHeader}>
            <TouchableOpacity onPress={handleCancelEdit}>
              <X size={24} color={Colors.light.gray} />
            </TouchableOpacity>
            <Text style={styles.editTitle}>Editar Fórmula</Text>
            <TouchableOpacity onPress={handleSaveEdit}>
              <Text style={styles.saveButton}>Guardar</Text>
            </TouchableOpacity>
          </View>
          <TextInput
            style={styles.editTextInput}
            value={editedText}
            onChangeText={setEditedText}
            multiline
            textAlignVertical="top"
            placeholder="Ingrese la fórmula aquí..."
          />
        </View>
      </Modal>

      {/* Instructions Flow Modal */}
      {showInstructionsFlow && visualData && (
        <Modal
          visible={showInstructionsFlow}
          animationType="slide"
          presentationStyle="fullScreen"
          onRequestClose={() => setShowInstructionsFlow(false)}
        >
          <InstructionsFlow
            formulaData={visualData}
            onClose={() => setShowInstructionsFlow(false)}
            onComplete={() => setShowInstructionsFlow(false)}
          />
        </Modal>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f0f2f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: Colors.light.gray,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.error,
    marginBottom: 10,
  },
  errorText: {
    fontSize: 14,
    color: Colors.light.gray,
    textAlign: 'center',
    marginBottom: 5,
  },
  header: {
    paddingTop: Platform.OS === 'ios' ? 50 : 30,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  clientInfo: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.8)',
    marginTop: 5,
  },
  editIconButton: {
    padding: 10,
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: 20,
  },
  mainPlayButton: {
    marginHorizontal: 20,
    marginVertical: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  playButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
  },
  playButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 12,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    marginTop: 20,
  },
  editButtonText: {
    color: 'white',
    marginLeft: 8,
    fontWeight: '500',
  },
  editModal: {
    flex: 1,
    backgroundColor: 'white',
  },
  editHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  editTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
  },
  saveButton: {
    fontSize: 16,
    color: Colors.light.primary,
    fontWeight: '600',
  },
  editTextInput: {
    flex: 1,
    padding: 20,
    fontSize: 16,
    lineHeight: 24,
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
  },
});
import React from 'react';
import { StyleSheet, ViewStyle } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import Animated from 'react-native-reanimated';

const AnimatedScrollView = Animated.createAnimatedComponent(ScrollView);

interface ScrollableContentProps {
  children: React.ReactNode;
  style?: ViewStyle;
  contentContainerStyle?: ViewStyle;
  showsVerticalScrollIndicator?: boolean;
  refreshControl?: React.ReactElement;
  onScroll?: (event: any) => void;
  scrollEventThrottle?: number;
  bounces?: boolean;
  keyboardShouldPersistTaps?: 'always' | 'never' | 'handled';
}

export const ScrollableContent: React.FC<ScrollableContentProps> = ({
  children,
  style,
  contentContainerStyle,
  showsVerticalScrollIndicator = false,
  refreshControl,
  onScroll,
  scrollEventThrottle = 16,
  bounces = true,
  keyboardShouldPersistTaps = 'handled',
}) => {
  return (
    <AnimatedScrollView
      style={[styles.container, style]}
      contentContainerStyle={contentContainerStyle}
      showsVerticalScrollIndicator={showsVerticalScrollIndicator}
      refreshControl={refreshControl}
      onScroll={onScroll}
      scrollEventThrottle={scrollEventThrottle}
      bounces={bounces}
      keyboardShouldPersistTaps={keyboardShouldPersistTaps}
      // Important: These props ensure smooth scrolling
      directionalLockEnabled={true}
      automaticallyAdjustContentInsets={false}
      contentInsetAdjustmentBehavior="automatic"
    >
      {children}
    </AnimatedScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
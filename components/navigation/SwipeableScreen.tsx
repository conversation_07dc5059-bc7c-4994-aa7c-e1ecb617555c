import React from 'react';
import { StyleSheet, ViewStyle } from 'react-native';
import {
  GestureHandlerRootView,
  GestureDetector,
} from 'react-native-gesture-handler';
import Animated from 'react-native-reanimated';
import { useGestureHandler } from '@/hooks/useGestureHandler';

interface SwipeableScreenProps {
  children: React.ReactNode;
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  swipeEnabled?: boolean;
  scrollPriority?: 'vertical' | 'horizontal' | 'both';
  style?: ViewStyle;
  showSwipeIndicator?: boolean;
}

export const SwipeableScreen: React.FC<SwipeableScreenProps> = ({
  children,
  onSwipeLeft,
  onSwipeRight,
  onSwipeUp,
  onSwipeDown,
  swipeEnabled = true,
  scrollPriority = 'vertical',
  style,
  showSwipeIndicator = false,
}) => {
  const {
    gesture,
    animatedStyle,
    GestureHandlerRootView: GHRootView,
  } = useGestureHandler({
    onSwipeLeft,
    onSwipeRight,
    onSwipeUp,
    onSwipeDown,
    swipeEnabled,
    scrollPriority,
  });

  return (
    <GHRootView style={[styles.container, style]}>
      <GestureDetector gesture={gesture}>
        <Animated.View style={[styles.content, animatedStyle]}>
          {children}
          
          {showSwipeIndicator && onSwipeLeft && (
            <SwipeIndicator direction="left" />
          )}
          {showSwipeIndicator && onSwipeRight && (
            <SwipeIndicator direction="right" />
          )}
        </Animated.View>
      </GestureDetector>
    </GHRootView>
  );
};

// Optional: Visual indicator for swipe availability
const SwipeIndicator: React.FC<{ direction: 'left' | 'right' }> = ({ direction }) => {
  const animatedValue = React.useRef(new Animated.Value(0)).current;
  
  React.useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: true,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1500,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);
  
  const translateX = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: direction === 'left' ? [0, -20] : [0, 20],
  });
  
  const opacity = animatedValue.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.3, 0.6, 0.3],
  });
  
  return (
    <Animated.View
      style={[
        styles.indicator,
        direction === 'left' ? styles.indicatorLeft : styles.indicatorRight,
        {
          opacity,
          transform: [{ translateX }],
        },
      ]}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  indicator: {
    position: 'absolute',
    top: '50%',
    width: 4,
    height: 60,
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    borderRadius: 2,
  },
  indicatorLeft: {
    left: 10,
  },
  indicatorRight: {
    right: 10,
  },
});
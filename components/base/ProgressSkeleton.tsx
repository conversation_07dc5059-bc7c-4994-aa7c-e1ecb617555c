import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, ViewStyle, Dimensions } from 'react-native';
import Colors from '@/constants/colors';

interface SkeletonProps {
  width?: number | string;
  height?: number;
  borderRadius?: number;
  style?: ViewStyle;
  variant?: 'text' | 'rectangular' | 'circular';
  animation?: 'pulse' | 'wave' | 'none';
}

export const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = 20,
  borderRadius,
  style,
  variant = 'text',
  animation = 'pulse'
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;
  
  useEffect(() => {
    if (animation === 'none') return;
    
    const animationConfig = animation === 'pulse' 
      ? Animated.loop(
          Animated.sequence([
            Animated.timing(animatedValue, {
              toValue: 1,
              duration: 1000,
              useNativeDriver: true,
            }),
            Animated.timing(animatedValue, {
              toValue: 0,
              duration: 1000,
              useNativeDriver: true,
            }),
          ])
        )
      : Animated.loop(
          Animated.timing(animatedValue, {
            toValue: 1,
            duration: 1500,
            useNativeDriver: true,
          })
        );
    
    animationConfig.start();
    
    return () => animationConfig.stop();
  }, [animation]);
  
  const opacity = animation === 'pulse' 
    ? animatedValue.interpolate({
        inputRange: [0, 1],
        outputRange: [0.3, 0.7],
      })
    : 1;
  
  const getVariantStyles = () => {
    switch (variant) {
      case 'circular':
        return {
          width: typeof width === 'number' ? width : height,
          height: height,
          borderRadius: height / 2,
        };
      case 'rectangular':
        return {
          width,
          height,
          borderRadius: borderRadius || 8,
        };
      default:
        return {
          width,
          height,
          borderRadius: borderRadius || 4,
        };
    }
  };
  
  return (
    <View style={[styles.container, getVariantStyles(), style]}>
      <Animated.View
        style={[
          styles.skeleton,
          { opacity },
          getVariantStyles(),
        ]}
      />
      {animation === 'wave' && (
        <Animated.View
          style={[
            styles.wave,
            {
              transform: [
                {
                  translateX: animatedValue.interpolate({
                    inputRange: [0, 1],
                    outputRange: [-Dimensions.get('window').width, Dimensions.get('window').width],
                  }),
                },
              ],
            },
          ]}
        />
      )}
    </View>
  );
};

interface SkeletonGroupProps {
  count?: number;
  gap?: number;
  children?: React.ReactNode;
}

export const SkeletonGroup: React.FC<SkeletonGroupProps> = ({
  count = 3,
  gap = 12,
  children
}) => {
  if (children) {
    return (
      <View style={[styles.group, { gap }]}>
        {children}
      </View>
    );
  }
  
  return (
    <View style={[styles.group, { gap }]}>
      {Array.from({ length: count }).map((_, index) => (
        <Skeleton key={index} />
      ))}
    </View>
  );
};

// Pre-built skeleton templates for common UI patterns
export const SkeletonTemplates = {
  ListItem: () => (
    <View style={skeletonTemplateStyles.listItem}>
      <Skeleton variant="circular" width={48} height={48} />
      <View style={skeletonTemplateStyles.listItemContent}>
        <Skeleton width="60%" height={16} />
        <Skeleton width="40%" height={14} style={{ marginTop: 4 }} />
      </View>
    </View>
  ),
  
  Card: () => (
    <View style={skeletonTemplateStyles.card}>
      <Skeleton variant="rectangular" height={120} />
      <View style={skeletonTemplateStyles.cardContent}>
        <Skeleton width="80%" height={20} />
        <Skeleton width="100%" height={14} style={{ marginTop: 8 }} />
        <Skeleton width="90%" height={14} style={{ marginTop: 4 }} />
      </View>
    </View>
  ),
  
  ServiceStep: () => (
    <View style={skeletonTemplateStyles.serviceStep}>
      <Skeleton variant="rectangular" height={200} />
      <SkeletonGroup gap={16}>
        <Skeleton width="70%" height={24} />
        <Skeleton width="100%" height={16} />
        <Skeleton width="95%" height={16} />
        <Skeleton width="85%" height={16} />
      </SkeletonGroup>
    </View>
  ),
  
  InventoryItem: () => (
    <View style={skeletonTemplateStyles.inventoryItem}>
      <Skeleton variant="rectangular" width={60} height={60} />
      <View style={skeletonTemplateStyles.inventoryContent}>
        <Skeleton width="70%" height={18} />
        <Skeleton width="50%" height={14} style={{ marginTop: 4 }} />
        <Skeleton width="30%" height={16} style={{ marginTop: 8 }} />
      </View>
    </View>
  ),
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    backgroundColor: Colors.light.card,
  },
  skeleton: {
    backgroundColor: '#E5E5E7',
  },
  wave: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    width: '100%',
  },
  group: {
    flexDirection: 'column',
  },
});

const skeletonTemplateStyles = StyleSheet.create({
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: Colors.light.card,
    borderRadius: 12,
    marginBottom: 8,
  },
  listItemContent: {
    flex: 1,
    marginLeft: 12,
  },
  card: {
    backgroundColor: Colors.light.card,
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
  },
  cardContent: {
    padding: 16,
  },
  serviceStep: {
    padding: 16,
  },
  inventoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: Colors.light.card,
    borderRadius: 12,
    marginBottom: 8,
  },
  inventoryContent: {
    flex: 1,
    marginLeft: 12,
  },
});

export default { Skeleton, SkeletonGroup, SkeletonTemplates };
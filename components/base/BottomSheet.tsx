import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  PanResponder,
  Animated,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { X } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { typography, spacing, radius } from '@/constants/theme';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { height: SCREEN_HEIGHT } = Dimensions.get('window');

interface BottomSheetProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  height?: number | 'auto' | 'full';
  showHandle?: boolean;
  closeOnOverlayPress?: boolean;
  showCloseButton?: boolean;
  scrollable?: boolean;
  contentHandlesScroll?: boolean;
  disableSwipeToClose?: boolean;
}

export const BottomSheet: React.FC<BottomSheetProps> = ({
  visible,
  onClose,
  title,
  children,
  height = 'auto',
  showHandle = true,
  closeOnOverlayPress = true,
  showCloseButton = true,
  scrollable = true,
  contentHandlesScroll = false,
  disableSwipeToClose = false,
}) => {
  const insets = useSafeAreaInsets();
  const translateY = useRef(new Animated.Value(SCREEN_HEIGHT)).current;
  const bgOpacity = useRef(new Animated.Value(0)).current;
  
  const getSheetHeight = () => {
    if (height === 'full') return SCREEN_HEIGHT * 0.9;
    if (height === 'auto') return SCREEN_HEIGHT * 0.6;
    return height;
  };

  const sheetHeight = getSheetHeight();
  
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => !disableSwipeToClose,
      onMoveShouldSetPanResponder: (_, gestureState) => {
        return !disableSwipeToClose && Math.abs(gestureState.dy) > 5;
      },
      onPanResponderMove: (_, gestureState) => {
        if (!disableSwipeToClose && gestureState.dy > 0) {
          translateY.setValue(gestureState.dy);
        }
      },
      onPanResponderRelease: (_, gestureState) => {
        if (!disableSwipeToClose && gestureState.dy > 50 && gestureState.vy > 0.3) {
          closeSheet();
        } else {
          Animated.spring(translateY, {
            toValue: 0,
            useNativeDriver: true,
            tension: 50,
            friction: 10,
          }).start();
        }
      },
    })
  ).current;

  useEffect(() => {
    if (visible) {
      openSheet();
    }
  }, [visible]);

  const openSheet = () => {
    Animated.parallel([
      Animated.timing(bgOpacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.spring(translateY, {
        toValue: 0,
        useNativeDriver: true,
        tension: 50,
        friction: 10,
      }),
    ]).start();
  };

  const closeSheet = () => {
    Animated.parallel([
      Animated.timing(bgOpacity, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(translateY, {
        toValue: SCREEN_HEIGHT,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onClose();
    });
  };

  const handleOverlayPress = () => {
    if (closeOnOverlayPress) {
      closeSheet();
    }
  };

  const ContentWrapper = (scrollable && !contentHandlesScroll) ? ScrollView : View;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      statusBarTranslucent
    >
      <TouchableOpacity
        style={styles.overlay}
        activeOpacity={1}
        onPress={handleOverlayPress}
      >
        <Animated.View
          style={[
            styles.backdrop,
            {
              opacity: bgOpacity,
            },
          ]}
        />
      </TouchableOpacity>

      <Animated.View
        style={[
          styles.sheet,
          {
            height: height === 'auto' ? undefined : sheetHeight,
            maxHeight: height === 'auto' ? SCREEN_HEIGHT * 0.9 : sheetHeight,
            transform: [{ translateY }],
            paddingBottom: insets.bottom,
          },
        ]}
      >
        {showHandle && (
          <View style={styles.handleContainer} {...panResponder.panHandlers}>
            <View style={styles.handle} />
          </View>
        )}

        {(title || showCloseButton) && (
          <View style={styles.header}>
            {title && <Text style={styles.title}>{title}</Text>}
            {showCloseButton && (
              <TouchableOpacity
                style={styles.closeButton}
                onPress={closeSheet}
              >
                <X size={24} color={Colors.light.text} />
              </TouchableOpacity>
            )}
          </View>
        )}

        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          style={{ flex: height === 'auto' ? 0 : 1 }}
        >
          <ContentWrapper
            style={[
              styles.content,
              height === 'auto' && { flexGrow: 0 },
              contentHandlesScroll && { flex: 1, paddingHorizontal: 0, paddingVertical: 0 },
            ]}
            {...((scrollable && !contentHandlesScroll) ? {
              showsVerticalScrollIndicator: false,
              bounces: true,
            } : {})}
          >
            {children}
          </ContentWrapper>
        </KeyboardAvoidingView>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'flex-end',
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  sheet: {
    backgroundColor: Colors.light.background,
    borderTopLeftRadius: radius.xl,
    borderTopRightRadius: radius.xl,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 20,
  },
  handleContainer: {
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  handle: {
    width: 40,
    height: 4,
    backgroundColor: Colors.light.gray,
    borderRadius: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  title: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    flex: 1,
  },
  closeButton: {
    padding: spacing.xs,
  },
  content: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    flex: 1,
  },
});

export default BottomSheet;
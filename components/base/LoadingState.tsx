import React from 'react';
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
import Colors from '@/constants/colors';
import { typography, spacing } from '@/constants/theme';
import { a11y } from '@/utils/accessibility';

interface LoadingStateProps {
  message?: string;
  fullScreen?: boolean;
}

export function LoadingState({ message, fullScreen = true }: LoadingStateProps) {
  const loadingMessage = message || 'Cargando';
  
  return (
    <View 
      style={[styles.container, fullScreen && styles.fullScreen]}
      {...a11y.loading(loadingMessage)}
    >
      <ActivityIndicator 
        size="large" 
        color={Colors.light.primary}
        accessibilityElementsHidden={true}
      />
      {message && (
        <Text 
          style={styles.message}
          accessibilityLiveRegion="polite"
        >
          {message}
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },
  fullScreen: {
    flex: 1,
    backgroundColor: '#F5F5F7',
  },
  message: {
    marginTop: spacing.md,
    ...typography.body,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },
});
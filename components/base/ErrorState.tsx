import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { AlertCircle } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { typography, spacing, radius } from '@/constants/theme';
import { BaseButton } from './BaseButton';
import { a11y } from '@/utils/accessibility';

interface ErrorStateProps {
  title?: string;
  message: string;
  onRetry?: () => void;
  fullScreen?: boolean;
}

export function ErrorState({ 
  title = 'Algo salió mal', 
  message, 
  onRetry, 
  fullScreen = true 
}: ErrorStateProps) {
  const errorMessage = `${title}: ${message}`;
  
  return (
    <View 
      style={[styles.container, fullScreen && styles.fullScreen]}
      {...a11y.error(errorMessage)}
    >
      <View 
        style={styles.iconContainer}
        accessibilityElementsHidden={true}
      >
        <AlertCircle size={48} color={Colors.light.error} />
      </View>
      <Text 
        style={styles.title}
        {...a11y.header(title, 2)}
      >
        {title}
      </Text>
      <Text 
        style={styles.message}
        accessibilityLiveRegion="assertive"
      >
        {message}
      </Text>
      {onRetry && (
        <BaseButton 
          title="Reintentar" 
          onPress={onRetry}
          style={styles.retryButton}
          accessibilityHint="Toca para intentar de nuevo"
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },
  fullScreen: {
    flex: 1,
    backgroundColor: '#F5F5F7',
  },
  iconContainer: {
    marginBottom: spacing.lg,
  },
  title: {
    ...typography.h3,
    color: Colors.light.text,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  message: {
    ...typography.body,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.xl,
    paddingHorizontal: spacing.md,
  },
  retryButton: {
    minWidth: 120,
  },
});
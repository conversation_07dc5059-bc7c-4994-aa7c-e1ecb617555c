import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  Animated,
  TextInputProps,
  ViewStyle,
} from 'react-native';
import { AlertCircle, CheckCircle, Info } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { typography, spacing, radius } from '@/constants/theme';
import { a11y } from '@/utils/accessibility';

interface ValidatedInputProps extends Omit<TextInputProps, 'style'> {
  label: string;
  error?: string;
  warning?: string;
  info?: string;
  success?: boolean;
  required?: boolean;
  containerStyle?: ViewStyle;
  inputStyle?: ViewStyle;
  showValidation?: boolean;
  helperText?: string;
}

export const ValidatedInput: React.FC<ValidatedInputProps> = ({
  label,
  error,
  warning,
  info,
  success,
  required,
  value,
  onChangeText,
  onBlur,
  containerStyle,
  inputStyle,
  showValidation = true,
  helperText,
  placeholder,
  ...textInputProps
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const animatedValue = React.useRef(new Animated.Value(0)).current;
  const shakeAnimation = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: isFocused || value ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [isFocused, value]);

  React.useEffect(() => {
    if (error && showValidation) {
      // Shake animation on error
      Animated.sequence([
        Animated.timing(shakeAnimation, {
          toValue: 10,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(shakeAnimation, {
          toValue: -10,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(shakeAnimation, {
          toValue: 10,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(shakeAnimation, {
          toValue: 0,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [error, showValidation]);

  const getBorderColor = () => {
    if (!showValidation) return Colors.light.border;
    if (error) return Colors.light.error;
    if (warning) return Colors.light.warning;
    if (success) return Colors.light.success;
    if (isFocused) return Colors.light.primary;
    return Colors.light.border;
  };

  const getIcon = () => {
    if (!showValidation) return null;
    if (error) return <AlertCircle size={20} color={Colors.light.error} />;
    if (warning) return <AlertCircle size={20} color={Colors.light.warning} />;
    if (success) return <CheckCircle size={20} color={Colors.light.success} />;
    if (info) return <Info size={20} color={Colors.light.info} />;
    return null;
  };

  const getMessage = () => {
    if (error) return error;
    if (warning) return warning;
    if (info) return info;
    if (helperText) return helperText;
    return null;
  };

  const getMessageColor = () => {
    if (error) return Colors.light.error;
    if (warning) return Colors.light.warning;
    if (info) return Colors.light.info;
    return Colors.light.textSecondary;
  };

  const labelTranslateY = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [20, 0],
  });

  const labelScale = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 0.85],
  });

  const labelColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [Colors.light.textSecondary, getBorderColor()],
  });

  const a11yLabel = `${label}${required ? ', obligatorio' : ''}`;
  const a11yHint = getMessage() || placeholder || undefined;
  const a11yProps = a11y.input(a11yLabel, a11yHint, value);

  return (
    <Animated.View 
      style={[
        styles.container, 
        containerStyle,
        { transform: [{ translateX: shakeAnimation }] }
      ]}
    >
      <View 
        style={[
          styles.inputContainer,
          { borderColor: getBorderColor() },
          isFocused && styles.inputContainerFocused,
        ]}
      >
        <Animated.Text
          style={[
            styles.label,
            {
              transform: [
                { translateY: labelTranslateY },
                { scale: labelScale },
              ],
              color: labelColor,
            },
          ]}
        >
          {label} {required && <Text style={styles.required}>*</Text>}
        </Animated.Text>
        
        <View style={styles.inputWrapper}>
          <TextInput
            style={[styles.input, inputStyle]}
            value={value}
            onChangeText={onChangeText}
            onFocus={() => setIsFocused(true)}
            onBlur={(e) => {
              setIsFocused(false);
              onBlur?.(e);
            }}
            placeholder={isFocused ? placeholder : ''}
            placeholderTextColor={Colors.light.gray}
            {...a11yProps}
            accessibilityState={{
              required,
              invalid: !!error,
            }}
            {...textInputProps}
          />
          
          {showValidation && (
            <View style={styles.iconContainer}>
              {getIcon()}
            </View>
          )}
        </View>
      </View>
      
      {getMessage() && showValidation && (
        <Animated.Text
          style={[
            styles.message,
            { color: getMessageColor() },
          ]}
          accessibilityLiveRegion={error ? 'assertive' : 'polite'}
        >
          {getMessage()}
        </Animated.Text>
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: spacing.lg,
  },
  inputContainer: {
    borderWidth: 2,
    borderRadius: radius.md,
    backgroundColor: Colors.light.surface,
    paddingHorizontal: spacing.md,
    minHeight: 56,
    justifyContent: 'center',
  },
  inputContainerFocused: {
    backgroundColor: Colors.light.background,
  },
  label: {
    position: 'absolute',
    left: spacing.md,
    backgroundColor: Colors.light.surface,
    paddingHorizontal: spacing.xs,
    ...typography.body,
  },
  required: {
    color: Colors.light.error,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  input: {
    flex: 1,
    paddingVertical: spacing.md,
    ...typography.body,
    color: Colors.light.text,
  },
  iconContainer: {
    marginLeft: spacing.sm,
  },
  message: {
    marginTop: spacing.xs,
    marginLeft: spacing.md,
    ...typography.caption,
  },
});
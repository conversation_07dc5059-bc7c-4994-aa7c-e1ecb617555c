import React, { useRef } from 'react';
import {
  View,
  StyleSheet,
  Animated,
  PanResponder,
  ViewStyle,
  Dimensions,
} from 'react-native';
import Colors from '@/constants/colors';
import { spacing, radius } from '@/constants/theme';

interface SwipeableRowProps {
  children: React.ReactNode;
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  leftActionIcon?: React.ReactNode;
  rightActionIcon?: React.ReactNode;
  leftActionColor?: string;
  rightActionColor?: string;
  swipeThreshold?: number;
  style?: ViewStyle;
  enabled?: boolean;
}

const SCREEN_WIDTH = Dimensions.get('window').width;
const SWIPE_THRESHOLD = SCREEN_WIDTH * 0.25;

export function SwipeableRow({
  children,
  onSwipeLeft,
  onSwipeRight,
  leftActionIcon,
  rightActionIcon,
  leftActionColor = Colors.light.error,
  rightActionColor = Colors.light.primary,
  swipeThreshold = SWIPE_THRESHOLD,
  style,
  enabled = true,
}: SwipeableRowProps) {
  const translateX = useRef(new Animated.Value(0)).current;
  const actionScale = useRef(new Animated.Value(0)).current;

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => enabled,
      onMoveShouldSetPanResponder: (_, gestureState) => {
        return enabled && Math.abs(gestureState.dx) > 10;
      },
      onPanResponderMove: (_, gestureState) => {
        translateX.setValue(gestureState.dx);
        
        // Scale action icon based on swipe distance
        const scale = Math.min(Math.abs(gestureState.dx) / swipeThreshold, 1);
        actionScale.setValue(scale);
      },
      onPanResponderRelease: (_, gestureState) => {
        const isRightSwipe = gestureState.dx > swipeThreshold;
        const isLeftSwipe = gestureState.dx < -swipeThreshold;

        if (isRightSwipe && onSwipeRight) {
          // Animate out to the right
          Animated.timing(translateX, {
            toValue: SCREEN_WIDTH,
            duration: 300,
            useNativeDriver: true,
          }).start(() => {
            onSwipeRight();
            // Reset position
            translateX.setValue(0);
            actionScale.setValue(0);
          });
        } else if (isLeftSwipe && onSwipeLeft) {
          // Animate out to the left
          Animated.timing(translateX, {
            toValue: -SCREEN_WIDTH,
            duration: 300,
            useNativeDriver: true,
          }).start(() => {
            onSwipeLeft();
            // Reset position
            translateX.setValue(0);
            actionScale.setValue(0);
          });
        } else {
          // Snap back to center
          Animated.parallel([
            Animated.spring(translateX, {
              toValue: 0,
              friction: 5,
              useNativeDriver: true,
            }),
            Animated.spring(actionScale, {
              toValue: 0,
              friction: 5,
              useNativeDriver: true,
            }),
          ]).start();
        }
      },
    })
  ).current;

  const leftActionOpacity = translateX.interpolate({
    inputRange: [0, swipeThreshold],
    outputRange: [0, 1],
    extrapolate: 'clamp',
  });

  const rightActionOpacity = translateX.interpolate({
    inputRange: [-swipeThreshold, 0],
    outputRange: [1, 0],
    extrapolate: 'clamp',
  });

  return (
    <View style={[styles.container, style]}>
      {/* Left Action */}
      {onSwipeRight && leftActionIcon && (
        <Animated.View
          style={[
            styles.action,
            styles.leftAction,
            { backgroundColor: rightActionColor },
            {
              opacity: leftActionOpacity,
              transform: [{ scale: actionScale }],
            },
          ]}
        >
          {leftActionIcon}
        </Animated.View>
      )}

      {/* Right Action */}
      {onSwipeLeft && rightActionIcon && (
        <Animated.View
          style={[
            styles.action,
            styles.rightAction,
            { backgroundColor: leftActionColor },
            {
              opacity: rightActionOpacity,
              transform: [{ scale: actionScale }],
            },
          ]}
        >
          {rightActionIcon}
        </Animated.View>
      )}

      {/* Main Content */}
      <Animated.View
        style={[
          styles.content,
          { transform: [{ translateX }] },
        ]}
        {...panResponder.panHandlers}
      >
        {children}
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    backgroundColor: Colors.light.surface,
  },
  content: {
    backgroundColor: Colors.light.surface,
  },
  action: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    width: 80,
    justifyContent: 'center',
    alignItems: 'center',
  },
  leftAction: {
    left: 0,
  },
  rightAction: {
    right: 0,
  },
});
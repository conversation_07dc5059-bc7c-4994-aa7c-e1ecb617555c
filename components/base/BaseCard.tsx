import React from 'react';
import {
  View,
  StyleSheet,
  ViewStyle,
  TouchableOpacity,
  Animated,
} from 'react-native';
import Colors from '@/constants/colors';
import { shadows, radius, spacing } from '@/constants/theme';

interface BaseCardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  variant?: 'light' | 'dark' | 'beige';
  shadow?: keyof typeof shadows;
  onPress?: () => void;
  animated?: boolean;
  borderRadius?: keyof typeof radius;
  padding?: keyof typeof spacing;
}

export const BaseCard: React.FC<BaseCardProps> = ({
  children,
  style,
  variant = 'light',
  shadow = 'md',
  onPress,
  animated = true,
  borderRadius = 'lg',
  padding = 'md',
}) => {
  const scaleAnim = React.useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    if (animated && onPress) {
      Animated.spring(scaleAnim, {
        toValue: 0.98,
        useNativeDriver: true,
        speed: 20,
        bounciness: 0,
      }).start();
    }
  };

  const handlePressOut = () => {
    if (animated && onPress) {
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
        speed: 20,
        bounciness: 0,
      }).start();
    }
  };

  const cardStyle = [
    styles.base,
    styles[variant],
    shadows[shadow],
    {
      borderRadius: radius[borderRadius],
      padding: spacing[padding],
    },
    style,
  ];

  if (onPress) {
    return (
      <TouchableOpacity
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}
      >
        <Animated.View
          style={[cardStyle, animated && { transform: [{ scale: scaleAnim }] }]}
        >
          {children}
        </Animated.View>
      </TouchableOpacity>
    );
  }

  return <View style={cardStyle}>{children}</View>;
};

const styles = StyleSheet.create({
  base: {
    overflow: 'hidden',
  },
  light: {
    backgroundColor: Colors.light.card,
  },
  dark: {
    backgroundColor: Colors.light.cardDark,
  },
  beige: {
    backgroundColor: Colors.light.surface,
  },
});
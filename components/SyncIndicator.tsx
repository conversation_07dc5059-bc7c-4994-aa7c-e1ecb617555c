import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ActivityIndicator, Modal } from 'react-native';
import { Cloud, CloudOff, AlertCircle, Check } from 'lucide-react-native';
import { useSyncQueueStore } from '@/stores/sync-queue-store';
import Colors from '@/constants/colors';

export function SyncIndicator() {
  const [showDetails, setShowDetails] = useState(false);
  const { isOnline, isSyncing, lastSyncError, lastSyncTime, getQueueStatus } = useSyncQueueStore();
  const queueStatus = getQueueStatus();
  const totalPending = queueStatus.pending + queueStatus.syncing + queueStatus.failed;

  // Determinar el estado del icono
  const getIcon = () => {
    if (!isOnline) {
      return <CloudOff size={20} color={Colors.light.gray} />;
    }
    
    if (isSyncing) {
      return <ActivityIndicator size="small" color={Colors.light.primary} />;
    }
    
    if (lastSyncError && queueStatus.failed > 0) {
      return (
        <View>
          <Cloud size={20} color={Colors.light.warning} />
          <View style={styles.badge}>
            <Text style={styles.badgeText}>!</Text>
          </View>
        </View>
      );
    }
    
    if (totalPending > 0) {
      return (
        <View>
          <Cloud size={20} color={Colors.light.primary} />
          <View style={styles.badge}>
            <Text style={styles.badgeText}>{totalPending}</Text>
          </View>
        </View>
      );
    }
    
    return <Cloud size={20} color={Colors.light.success} />;
  };

  // Formatear el tiempo de última sincronización
  const getLastSyncText = () => {
    if (!lastSyncTime) return 'Nunca sincronizado';
    
    const now = Date.now();
    const diff = now - lastSyncTime;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    
    if (minutes < 1) return 'Hace un momento';
    if (minutes < 60) return `Hace ${minutes} minuto${minutes > 1 ? 's' : ''}`;
    if (hours < 24) return `Hace ${hours} hora${hours > 1 ? 's' : ''}`;
    
    return new Date(lastSyncTime).toLocaleDateString('es-ES');
  };

  const getStatusText = () => {
    if (!isOnline) return 'Sin conexión';
    if (isSyncing) return 'Sincronizando...';
    if (lastSyncError && queueStatus.failed > 0) return 'Error de sincronización';
    if (totalPending > 0) return `${totalPending} pendiente${totalPending > 1 ? 's' : ''}`;
    return 'Sincronizado';
  };

  const getStatusColor = () => {
    if (!isOnline) return Colors.light.gray;
    if (isSyncing) return Colors.light.primary;
    if (lastSyncError && queueStatus.failed > 0) return Colors.light.warning;
    if (totalPending > 0) return Colors.light.primary;
    return Colors.light.success;
  };

  return (
    <>
      <TouchableOpacity 
        style={styles.indicator} 
        onPress={() => setShowDetails(true)}
        activeOpacity={0.7}
      >
        {getIcon()}
      </TouchableOpacity>

      <Modal
        visible={showDetails}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowDetails(false)}
      >
        <TouchableOpacity 
          style={styles.modalOverlay} 
          activeOpacity={1} 
          onPress={() => setShowDetails(false)}
        >
          <View style={styles.modalContent}>
            <View style={[styles.statusHeader, { backgroundColor: getStatusColor() + '20' }]}>
              <View style={styles.statusIcon}>
                {getIcon()}
              </View>
              <Text style={[styles.statusTitle, { color: getStatusColor() }]}>
                {getStatusText()}
              </Text>
            </View>

            <View style={styles.detailsContainer}>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Estado de conexión:</Text>
                <Text style={styles.detailValue}>
                  {isOnline ? 'Conectado' : 'Sin conexión'}
                </Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Última sincronización:</Text>
                <Text style={styles.detailValue}>{getLastSyncText()}</Text>
              </View>

              {totalPending > 0 && (
                <>
                  <View style={styles.separator} />
                  <Text style={styles.sectionTitle}>Operaciones pendientes</Text>
                  
                  {queueStatus.pending > 0 && (
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>En cola:</Text>
                      <Text style={styles.detailValue}>{queueStatus.pending}</Text>
                    </View>
                  )}
                  
                  {queueStatus.syncing > 0 && (
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Sincronizando:</Text>
                      <Text style={styles.detailValue}>{queueStatus.syncing}</Text>
                    </View>
                  )}
                  
                  {queueStatus.failed > 0 && (
                    <View style={styles.detailRow}>
                      <Text style={[styles.detailLabel, { color: Colors.light.error }]}>
                        Con error:
                      </Text>
                      <Text style={[styles.detailValue, { color: Colors.light.error }]}>
                        {queueStatus.failed}
                      </Text>
                    </View>
                  )}
                </>
              )}

              {lastSyncError && (
                <>
                  <View style={styles.separator} />
                  <View style={styles.errorContainer}>
                    <AlertCircle size={16} color={Colors.light.error} />
                    <Text style={styles.errorText}>{lastSyncError}</Text>
                  </View>
                </>
              )}

              <View style={styles.separator} />
              <Text style={styles.infoText}>
                Los cambios se guardan automáticamente y se sincronizan cuando hay conexión disponible.
              </Text>
            </View>

            <TouchableOpacity 
              style={styles.closeButton} 
              onPress={() => setShowDetails(false)}
            >
              <Text style={styles.closeButtonText}>Cerrar</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  indicator: {
    padding: 8,
  },
  badge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: Colors.light.primary,
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '700',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 16,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 5,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  statusIcon: {
    marginRight: 12,
  },
  statusTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  detailsContainer: {
    padding: 20,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.text,
  },
  separator: {
    height: 1,
    backgroundColor: Colors.light.border,
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 12,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.error + '10',
    padding: 12,
    borderRadius: 8,
  },
  errorText: {
    fontSize: 12,
    color: Colors.light.error,
    marginLeft: 8,
    flex: 1,
  },
  infoText: {
    fontSize: 12,
    color: Colors.light.gray,
    lineHeight: 18,
    textAlign: 'center',
  },
  closeButton: {
    backgroundColor: Colors.light.primary,
    margin: 20,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  closeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
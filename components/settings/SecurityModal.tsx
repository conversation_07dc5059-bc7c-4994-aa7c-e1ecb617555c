import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { X, Shield, Download, Trash2, Lock, Eye, Database } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { typography, spacing, radius } from '@/constants/theme';
import { BaseCard, BaseButton } from '@/components/base';
import { useAuthStore } from '@/stores/auth-store';
import { useClientStore } from '@/stores/client-store';
import { useClientHistoryStore } from '@/stores/client-history-store';

interface SecurityModalProps {
  visible: boolean;
  onClose: () => void;
}

export default function SecurityModal({ visible, onClose }: SecurityModalProps) {
  const { user } = useAuthStore();
  const { clients } = useClientStore();
  
  const [isExporting, setIsExporting] = useState(false);
  const [servicesCount, setServicesCount] = useState(0);

  const handleExportData = async () => {
    setIsExporting(true);
    try {
      // In a real app, this would generate and download a JSON/CSV file
      const exportData = {
        user: {
          name: user?.name,
          email: user?.email,
          exportDate: new Date().toISOString(),
        },
        clientsCount: clients?.length || 0,
        servicesCount: servicesCount,
      };
      
      Alert.alert(
        'Exportación Lista',
        `Se han preparado ${clients?.length || 0} clientes y ${servicesCount} servicios para exportar.`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert('Error', 'No se pudo exportar los datos');
    } finally {
      setIsExporting(false);
    }
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      '⚠️ Eliminar Cuenta',
      'Esta acción es permanente y eliminará todos tus datos, incluyendo:\n\n• Perfil de usuario\n• Clientes\n• Historiales de servicio\n• Configuración\n\n¿Estás seguro de que deseas continuar?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Eliminar Todo',
          style: 'destructive',
          onPress: () => {
            Alert.alert(
              'Confirmación Final',
              'Por favor, escribe "ELIMINAR" para confirmar',
              [
                { text: 'Cancelar', style: 'cancel' },
                {
                  text: 'Confirmar',
                  style: 'destructive',
                  onPress: () => {
                    // In real app, this would delete the account
                    console.log('Account deletion requested');
                  },
                },
              ]
            );
          },
        },
      ]
    );
  };

  const securityOptions = [
    {
      id: 'export',
      title: 'Exportar Mis Datos',
      description: 'Descarga todos tus datos en formato JSON',
      icon: Download,
      iconBg: Colors.light.success + '15',
      iconColor: Colors.light.success,
      action: handleExportData,
      isLoading: isExporting,
    },
    {
      id: 'privacy',
      title: 'Política de Privacidad',
      description: 'Cómo protegemos tus datos',
      icon: Eye,
      iconBg: Colors.light.info + '15',
      iconColor: Colors.light.info,
      action: () => {
        // In real app, this would open privacy policy
        Alert.alert('Política de Privacidad', 'Tus datos están seguros con nosotros');
      },
    },
    {
      id: 'backup',
      title: 'Copias de Seguridad',
      description: 'Backup automático cada 24 horas',
      icon: Database,
      iconBg: Colors.light.primary + '15',
      iconColor: Colors.light.primary,
      action: () => {
        Alert.alert(
          'Copias de Seguridad',
          'Tus datos se respaldan automáticamente cada 24 horas en servidores seguros'
        );
      },
    },
    {
      id: 'delete',
      title: 'Eliminar Mi Cuenta',
      description: 'Elimina permanentemente todos tus datos',
      icon: Trash2,
      iconBg: Colors.light.error + '15',
      iconColor: Colors.light.error,
      action: handleDeleteAccount,
      isDangerous: true,
    },
  ];

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose}>
            <X size={24} color={Colors.light.gray} />
          </TouchableOpacity>
          <Text style={styles.title}>Seguridad y Privacidad</Text>
          <View style={{ width: 24 }} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Icon Section */}
          <View style={styles.iconSection}>
            <View style={styles.iconContainer}>
              <Shield size={48} color={Colors.light.success} />
            </View>
            <Text style={styles.subtitle}>
              Tu información está protegida con los más altos estándares de seguridad
            </Text>
          </View>

          {/* Security Features */}
          <View style={styles.featuresSection}>
            <Text style={styles.sectionTitle}>Características de Seguridad</Text>
            <BaseCard variant="light" shadow="sm" style={styles.featuresCard}>
              <View style={styles.featureItem}>
                <Lock size={20} color={Colors.light.success} />
                <Text style={styles.featureText}>Encriptación de extremo a extremo</Text>
              </View>
              <View style={styles.featureItem}>
                <Shield size={20} color={Colors.light.success} />
                <Text style={styles.featureText}>Cumplimiento GDPR y LOPD</Text>
              </View>
              <View style={styles.featureItem}>
                <Database size={20} color={Colors.light.success} />
                <Text style={styles.featureText}>Backups automáticos diarios</Text>
              </View>
            </BaseCard>
          </View>

          {/* Security Options */}
          <View style={styles.optionsSection}>
            <Text style={styles.sectionTitle}>Opciones de Seguridad</Text>
            
            {securityOptions.map((option, index) => (
              <TouchableOpacity
                key={option.id}
                onPress={option.action}
                disabled={option.isLoading}
              >
                <BaseCard 
                  variant="light" 
                  shadow="sm" 
                  style={{
                    ...styles.optionCard,
                    ...(option.isDangerous ? styles.dangerousCard : {}),
                    ...(index === securityOptions.length - 1 ? styles.lastOptionCard : {})
                  }}
                >
                  <View style={styles.optionContent}>
                    <View style={[styles.optionIcon, { backgroundColor: option.iconBg }]}>
                      <option.icon size={24} color={option.iconColor} />
                    </View>
                    
                    <View style={styles.optionInfo}>
                      <Text style={[
                        styles.optionTitle,
                        option.isDangerous && styles.dangerousText
                      ]}>
                        {option.title}
                      </Text>
                      <Text style={styles.optionDescription}>
                        {option.description}
                      </Text>
                    </View>
                    
                    {option.isLoading && (
                      <Text style={styles.loadingText}>Procesando...</Text>
                    )}
                  </View>
                </BaseCard>
              </TouchableOpacity>
            ))}
          </View>

          {/* Data Summary */}
          <View style={styles.summarySection}>
            <Text style={styles.sectionTitle}>Resumen de Datos</Text>
            <BaseCard variant="light" shadow="sm">
              <View style={styles.summaryItem}>
                <Text style={styles.summaryLabel}>Clientes registrados:</Text>
                <Text style={styles.summaryValue}>{clients?.length || 0}</Text>
              </View>
              <View style={styles.summaryDivider} />
              <View style={styles.summaryItem}>
                <Text style={styles.summaryLabel}>Servicios realizados:</Text>
                <Text style={styles.summaryValue}>{servicesCount}</Text>
              </View>
              <View style={styles.summaryDivider} />
              <View style={styles.summaryItem}>
                <Text style={styles.summaryLabel}>Última actualización:</Text>
                <Text style={styles.summaryValue}>Hace 2 horas</Text>
              </View>
            </BaseCard>
          </View>

          {/* Info Note */}
          <View style={styles.infoNote}>
            <Text style={styles.infoNoteText}>
              💡 Para solicitudes especiales de privacidad o más información, <NAME_EMAIL>
            </Text>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  title: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  content: {
    flex: 1,
  },
  iconSection: {
    alignItems: 'center',
    paddingTop: spacing.xl,
    paddingBottom: spacing.lg,
    paddingHorizontal: spacing.lg,
  },
  iconContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: Colors.light.success + '15',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  subtitle: {
    fontSize: typography.sizes.base,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  featuresSection: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.md,
  },
  featuresCard: {
    paddingVertical: spacing.sm,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.sm,
    gap: spacing.md,
  },
  featureText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.text,
  },
  optionsSection: {
    paddingHorizontal: spacing.lg,
  },
  optionCard: {
    marginBottom: spacing.sm,
  },
  dangerousCard: {
    borderWidth: 1,
    borderColor: Colors.light.error + '20',
  },
  lastOptionCard: {
    marginBottom: 0,
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  optionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  optionInfo: {
    flex: 1,
  },
  optionTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  dangerousText: {
    color: Colors.light.error,
  },
  optionDescription: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    lineHeight: 18,
  },
  loadingText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.primary,
    fontWeight: typography.weights.medium,
  },
  summarySection: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
  },
  summaryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.md,
  },
  summaryLabel: {
    fontSize: typography.sizes.base,
    color: Colors.light.textSecondary,
  },
  summaryValue: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  summaryDivider: {
    height: 1,
    backgroundColor: Colors.light.border,
  },
  infoNote: {
    backgroundColor: Colors.light.info + '10',
    marginHorizontal: spacing.lg,
    marginBottom: spacing.xl,
    padding: spacing.md,
    borderRadius: radius.md,
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.info,
  },
  infoNoteText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.text,
    lineHeight: 20,
  },
});
import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { X, MapPin, Building2 } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { typography, spacing, radius } from '@/constants/theme';
import { BaseCard } from '@/components/base';

interface BusinessModalProps {
  visible: boolean;
  onClose: () => void;
  businessData: {
    businessName: string;
    streetAddress: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  onSave: (data: any) => void;
}

export default function BusinessModal({ visible, onClose, businessData, onSave }: BusinessModalProps) {
  const [formData, setFormData] = useState(businessData);

  const handleSave = () => {
    onSave(formData);
    onClose();
  };

  const updateField = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <KeyboardAvoidingView 
        style={styles.container} 
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      >
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose}>
            <X size={24} color={Colors.light.gray} />
          </TouchableOpacity>
          <Text style={styles.title}>Mi Negocio</Text>
          <TouchableOpacity onPress={handleSave}>
            <Text style={styles.saveText}>Guardar</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Business Icon */}
          <View style={styles.iconSection}>
            <View style={styles.iconContainer}>
              <Building2 size={48} color={Colors.light.secondary} />
            </View>
          </View>

          {/* Form Fields */}
          <View style={styles.formSection}>
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Nombre del Negocio</Text>
              <TextInput
                style={styles.input}
                value={formData.businessName}
                onChangeText={(value) => updateField('businessName', value)}
                placeholder="Mi Salón de Belleza"
                placeholderTextColor={Colors.light.gray}
              />
            </View>

            {/* Address Section */}
            <BaseCard variant="light" shadow="sm" style={styles.addressCard}>
              <View style={styles.addressHeader}>
                <MapPin size={20} color={Colors.light.primary} />
                <Text style={styles.addressTitle}>Dirección del Salón</Text>
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.label}>Calle y Número</Text>
                <TextInput
                  style={styles.input}
                  value={formData.streetAddress}
                  onChangeText={(value) => updateField('streetAddress', value)}
                  placeholder="Calle Principal 123"
                  placeholderTextColor={Colors.light.gray}
                />
              </View>

              <View style={styles.rowInputs}>
                <View style={[styles.inputGroup, styles.halfInput]}>
                  <Text style={styles.label}>Ciudad</Text>
                  <TextInput
                    style={styles.input}
                    value={formData.city}
                    onChangeText={(value) => updateField('city', value)}
                    placeholder="Madrid"
                    placeholderTextColor={Colors.light.gray}
                  />
                </View>

                <View style={[styles.inputGroup, styles.halfInput]}>
                  <Text style={styles.label}>Código Postal</Text>
                  <TextInput
                    style={styles.input}
                    value={formData.postalCode}
                    onChangeText={(value) => updateField('postalCode', value)}
                    placeholder="28001"
                    placeholderTextColor={Colors.light.gray}
                    keyboardType="numeric"
                  />
                </View>
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.label}>Provincia/Estado</Text>
                <TextInput
                  style={styles.input}
                  value={formData.state}
                  onChangeText={(value) => updateField('state', value)}
                  placeholder="Madrid"
                  placeholderTextColor={Colors.light.gray}
                />
              </View>
            </BaseCard>

            {/* Help Text */}
            <View style={styles.helpSection}>
              <Text style={styles.helpText}>
                💡 La dirección del salón aparecerá en tus recibos y reportes. 
                Mantenla actualizada para una mejor organización.
              </Text>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  title: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  saveText: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.primary,
  },
  content: {
    flex: 1,
  },
  iconSection: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  iconContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: Colors.light.secondary + '15',
    justifyContent: 'center',
    alignItems: 'center',
  },
  formSection: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.xl,
  },
  inputGroup: {
    marginBottom: 24,
  },
  label: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.sm,
  },
  input: {
    backgroundColor: '#F5F5F7',
    borderRadius: radius.md,
    paddingHorizontal: spacing.lg,
    paddingVertical: 18,
    fontSize: typography.sizes.base,
    color: Colors.light.text,
  },
  addressCard: {
    marginBottom: spacing.lg,
  },
  addressHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
    gap: spacing.sm,
  },
  addressTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  rowInputs: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  halfInput: {
    flex: 1,
  },
  helpSection: {
    backgroundColor: Colors.light.accent + '10',
    padding: spacing.md,
    borderRadius: radius.md,
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.accent,
  },
  helpText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.text,
    lineHeight: 20,
  },
});
import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { X, Camera, Plus } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { typography, spacing, radius } from '@/constants/theme';
import { BaseButton } from '@/components/base';

interface ProfileModalProps {
  visible: boolean;
  onClose: () => void;
  userData: {
    name: string;
    email: string;
    phone?: string;
    licenseNumber?: string;
    yearsExperience?: string;
    specializations?: string[];
  };
  onSave: (data: any) => void;
}

export default function ProfileModal({ visible, onClose, userData, onSave }: ProfileModalProps) {
  const [name, setName] = useState(userData.name || '');
  const [phone, setPhone] = useState(userData.phone || '');
  const [licenseNumber, setLicenseNumber] = useState(userData.licenseNumber || '');
  const [yearsExperience, setYearsExperience] = useState(userData.yearsExperience || '');
  const [specializations, setSpecializations] = useState<string[]>(userData.specializations || []);
  const [newSpecialization, setNewSpecialization] = useState('');

  const addSpecialization = () => {
    if (newSpecialization.trim() && !specializations.includes(newSpecialization.trim())) {
      setSpecializations([...specializations, newSpecialization.trim()]);
      setNewSpecialization('');
    }
  };

  const removeSpecialization = (spec: string) => {
    setSpecializations(specializations.filter(s => s !== spec));
  };

  const handleSave = () => {
    onSave({
      name,
      phone,
      licenseNumber,
      yearsExperience,
      specializations,
    });
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <KeyboardAvoidingView 
        style={styles.container} 
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      >
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose}>
            <X size={24} color={Colors.light.gray} />
          </TouchableOpacity>
          <Text style={styles.title}>Mi Perfil</Text>
          <TouchableOpacity onPress={handleSave}>
            <Text style={styles.saveText}>Guardar</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Avatar Section */}
          <TouchableOpacity style={styles.avatarSection}>
            <View style={styles.avatar}>
              <Text style={styles.avatarText}>{name.charAt(0) || 'E'}</Text>
              <View style={styles.cameraButton}>
                <Camera size={16} color="white" />
              </View>
            </View>
            <Text style={styles.changePhotoText}>Próximamente</Text>
          </TouchableOpacity>

          {/* Form Fields */}
          <View style={styles.formSection}>
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Nombre completo</Text>
              <TextInput
                style={styles.input}
                value={name}
                onChangeText={setName}
                placeholder="Tu nombre"
                placeholderTextColor={Colors.light.gray}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Email</Text>
              <TextInput
                style={[styles.input, styles.disabledInput]}
                value={userData.email}
                editable={false}
                placeholder="<EMAIL>"
                placeholderTextColor={Colors.light.gray}
              />
              <Text style={styles.helperText}>El email no se puede cambiar por seguridad</Text>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Teléfono</Text>
              <TextInput
                style={styles.input}
                value={phone}
                onChangeText={setPhone}
                placeholder="Ej: +34 600 123 456"
                placeholderTextColor={Colors.light.gray}
                keyboardType="phone-pad"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Número de Licencia Profesional</Text>
              <TextInput
                style={styles.input}
                value={licenseNumber}
                onChangeText={setLicenseNumber}
                placeholder="Ej: 12345-COL"
                placeholderTextColor={Colors.light.gray}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Años de Experiencia</Text>
              <TextInput
                style={styles.input}
                value={yearsExperience}
                onChangeText={setYearsExperience}
                placeholder="Ej: 5"
                placeholderTextColor={Colors.light.gray}
                keyboardType="numeric"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Especializaciones</Text>
              <View style={styles.tagsContainer}>
                {specializations.map((spec, index) => (
                  <TouchableOpacity 
                    key={index} 
                    style={styles.tag}
                    onPress={() => removeSpecialization(spec)}
                  >
                    <Text style={styles.tagText}>{spec}</Text>
                    <X size={14} color={Colors.light.primary} />
                  </TouchableOpacity>
                ))}
              </View>
              <View style={styles.addTagContainer}>
                <TextInput
                  style={styles.addTagInput}
                  value={newSpecialization}
                  onChangeText={setNewSpecialization}
                  placeholder="Añadir especialización..."
                  placeholderTextColor={Colors.light.gray}
                  onSubmitEditing={addSpecialization}
                />
                <TouchableOpacity style={styles.addTagButton} onPress={addSpecialization}>
                  <Plus size={20} color={Colors.light.primary} />
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  title: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  saveText: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.primary,
  },
  content: {
    flex: 1,
  },
  avatarSection: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: Colors.light.primary + '15',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  avatarText: {
    fontSize: 40,
    fontWeight: typography.weights.bold,
    color: Colors.light.primary,
  },
  cameraButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.light.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: 'white',
  },
  changePhotoText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.primary,
    fontWeight: typography.weights.medium,
  },
  formSection: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.xl,
  },
  inputGroup: {
    marginBottom: 28,
  },
  label: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.sm,
  },
  input: {
    backgroundColor: '#F5F5F7',
    borderRadius: radius.md,
    paddingHorizontal: spacing.lg,
    paddingVertical: 18,
    fontSize: typography.sizes.base,
    color: Colors.light.text,
  },
  disabledInput: {
    opacity: 0.6,
  },
  helperText: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
    marginTop: spacing.xs,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
    marginBottom: spacing.md,
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.primary + '10',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: radius.full,
    gap: spacing.xs,
  },
  tagText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.primary,
    fontWeight: typography.weights.medium,
  },
  addTagContainer: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  addTagInput: {
    flex: 1,
    backgroundColor: '#F5F5F7',
    borderRadius: radius.md,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    fontSize: typography.sizes.base,
    color: Colors.light.text,
  },
  addTagButton: {
    width: 48,
    height: 48,
    borderRadius: radius.full,
    backgroundColor: '#F5F5F7',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
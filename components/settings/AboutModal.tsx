import React from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Linking,
} from 'react-native';
import { X, Heart, Mail, Globe, Shield, FileText } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { typography, spacing, radius } from '@/constants/theme';
import { BaseCard } from '@/components/base';

interface AboutModalProps {
  visible: boolean;
  onClose: () => void;
}

export default function AboutModal({ visible, onClose }: AboutModalProps) {
  const handleOpenLink = (url: string) => {
    Linking.openURL(url).catch(err => console.error('Error opening URL:', err));
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose}>
            <X size={24} color={Colors.light.gray} />
          </TouchableOpacity>
          <Text style={styles.title}>Acerca de Salonier</Text>
          <View style={{ width: 24 }} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Logo Section */}
          <View style={styles.logoSection}>
            <View style={styles.logoContainer}>
              <Text style={styles.logoText}>S</Text>
            </View>
            <Text style={styles.appName}>Salonier</Text>
            <Text style={styles.tagline}>Tu asistente inteligente de coloración</Text>
            <Text style={styles.version}>Versión 2.0.5</Text>
          </View>

          {/* Info Cards */}
          <View style={styles.cardsSection}>
            <BaseCard variant="light" shadow="sm" style={styles.infoCard}>
              <View style={styles.infoCardContent}>
                <View style={styles.infoIcon}>
                  <Heart size={24} color={Colors.light.primary} />
                </View>
                <View style={styles.infoTextContainer}>
                  <Text style={styles.infoTitle}>Hecho con amor</Text>
                  <Text style={styles.infoDescription}>
                    Desarrollado por profesionales de la coloración para profesionales de la coloración
                  </Text>
                </View>
              </View>
            </BaseCard>

            <BaseCard variant="light" shadow="sm" style={styles.infoCard}>
              <View style={styles.infoCardContent}>
                <View style={styles.infoIcon}>
                  <Shield size={24} color={Colors.light.success} />
                </View>
                <View style={styles.infoTextContainer}>
                  <Text style={styles.infoTitle}>Privacidad primero</Text>
                  <Text style={styles.infoDescription}>
                    Tus datos están seguros. Cumplimos con GDPR y las mejores prácticas de seguridad
                  </Text>
                </View>
              </View>
            </BaseCard>
          </View>

          {/* Links Section */}
          <View style={styles.linksSection}>
            <Text style={styles.sectionTitle}>Enlaces útiles</Text>
            
            <TouchableOpacity 
              style={styles.linkItem}
              onPress={() => handleOpenLink('https://salonier.com/privacy')}
            >
              <FileText size={20} color={Colors.light.primary} />
              <Text style={styles.linkText}>Política de Privacidad</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.linkItem}
              onPress={() => handleOpenLink('https://salonier.com/terms')}
            >
              <FileText size={20} color={Colors.light.primary} />
              <Text style={styles.linkText}>Términos de Uso</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.linkItem}
              onPress={() => handleOpenLink('https://salonier.com')}
            >
              <Globe size={20} color={Colors.light.primary} />
              <Text style={styles.linkText}>Visitar sitio web</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.linkItem}
              onPress={() => handleOpenLink('mailto:<EMAIL>')}
            >
              <Mail size={20} color={Colors.light.primary} />
              <Text style={styles.linkText}>Contactar soporte</Text>
            </TouchableOpacity>
          </View>

          {/* Credits */}
          <View style={styles.creditsSection}>
            <Text style={styles.creditsTitle}>Créditos</Text>
            <Text style={styles.creditsText}>
              Salonier utiliza inteligencia artificial avanzada para proporcionar 
              recomendaciones precisas de coloración capilar. Agradecemos a todos 
              los profesionales que han contribuido con su experiencia y conocimiento.
            </Text>
          </View>

          {/* Footer */}
          <View style={styles.footer}>
            <Text style={styles.footerText}>
              © 2024 Salonier. Todos los derechos reservados.
            </Text>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  title: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  content: {
    flex: 1,
  },
  logoSection: {
    alignItems: 'center',
    paddingVertical: spacing.xl * 2,
  },
  logoContainer: {
    width: 100,
    height: 100,
    borderRadius: 25,
    backgroundColor: Colors.light.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  logoText: {
    fontSize: 48,
    fontWeight: typography.weights.bold,
    color: 'white',
  },
  appName: {
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  tagline: {
    fontSize: typography.sizes.base,
    color: Colors.light.textSecondary,
    marginBottom: spacing.sm,
  },
  version: {
    fontSize: typography.sizes.sm,
    color: Colors.light.gray,
  },
  cardsSection: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  infoCard: {
    marginBottom: spacing.md,
  },
  infoCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.light.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  infoTextContainer: {
    flex: 1,
  },
  infoTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  infoDescription: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
  linksSection: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.md,
  },
  linkItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    gap: spacing.md,
  },
  linkText: {
    fontSize: typography.sizes.base,
    color: Colors.light.primary,
  },
  creditsSection: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
    backgroundColor: Colors.light.surface,
    marginHorizontal: spacing.lg,
    padding: spacing.lg,
    borderRadius: radius.md,
  },
  creditsTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.sm,
  },
  creditsText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
  footer: {
    paddingVertical: spacing.xl,
    alignItems: 'center',
  },
  footerText: {
    fontSize: typography.sizes.xs,
    color: Colors.light.gray,
  },
});
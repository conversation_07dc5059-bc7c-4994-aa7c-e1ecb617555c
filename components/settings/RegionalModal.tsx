import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { X, Globe, DollarSign, Ruler, Languages, Check, Palette, Calculator, BarChart3 } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { typography, spacing, radius } from '@/constants/theme';
import { BaseCard } from '@/components/base';
import { CountryCode } from '@/types/regional';
import { countriesData, getCountryByCode } from '@/constants/reference-data/countries-data';
import { getCurrencyByCode } from '@/constants/reference-data/currencies-data';
import { useSalonConfigStore } from '@/stores/salon-config-store';

interface RegionalModalProps {
  visible: boolean;
  onClose: () => void;
}

export default function RegionalModal({ visible, onClose }: RegionalModalProps) {
  const { configuration, updateCountry, updateLanguage, updateMeasurementSystem, updatePricing, updateInventoryControlLevel } = useSalonConfigStore();
  
  const [selectedCountry, setSelectedCountry] = useState(configuration.countryCode);
  const [selectedLanguage, setSelectedLanguage] = useState(configuration.language);
  const [selectedMeasurement, setSelectedMeasurement] = useState(configuration.measurementSystem);
  const [selectedCurrency, setSelectedCurrency] = useState(configuration.pricing.currency);
  const [selectedInventoryLevel, setSelectedInventoryLevel] = useState(configuration.inventoryControlLevel);

  const currentCountry = getCountryByCode(selectedCountry as CountryCode);
  const currentCurrency = getCurrencyByCode(selectedCurrency);

  const handleSave = () => {
    // Update country (this also updates default language and measurement)
    if (selectedCountry !== configuration.countryCode) {
      updateCountry(selectedCountry as CountryCode);
    }
    
    // Update language if different from country default
    if (selectedLanguage !== configuration.language) {
      updateLanguage(selectedLanguage);
    }
    
    // Update measurement system if different from country default
    if (selectedMeasurement !== configuration.measurementSystem) {
      updateMeasurementSystem(selectedMeasurement);
    }
    
    // Update currency
    if (selectedCurrency !== configuration.pricing.currency && currentCurrency) {
      updatePricing({
        currency: currentCurrency.code,
        currencySymbol: currentCurrency.symbol,
      });
    }
    
    // Update inventory control level
    if (selectedInventoryLevel !== configuration.inventoryControlLevel) {
      updateInventoryControlLevel(selectedInventoryLevel);
    }
    
    onClose();
  };

  const languages = [
    { code: 'es', name: 'Español', flag: '🇪🇸' },
    { code: 'en', name: 'English', flag: '🇬🇧' },
    { code: 'pt', name: 'Português', flag: '🇧🇷' },
    { code: 'fr', name: 'Français', flag: '🇫🇷' },
  ];

  const measurementSystems = [
    { 
      id: 'metric', 
      name: 'Sistema Métrico', 
      description: 'Mililitros (ml) y gramos (g)',
      example: 'Ej: 60ml, 90g'
    },
    { 
      id: 'imperial', 
      name: 'Sistema Imperial', 
      description: 'Onzas líquidas (fl oz) y onzas (oz)',
      example: 'Ej: 2 fl oz, 3 oz'
    },
  ];

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose}>
            <X size={24} color={Colors.light.gray} />
          </TouchableOpacity>
          <Text style={styles.title}>Configuración Regional</Text>
          <TouchableOpacity onPress={handleSave}>
            <Text style={styles.saveText}>Guardar</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Country Selection */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Globe size={20} color={Colors.light.primary} />
              <Text style={styles.sectionTitle}>País</Text>
            </View>
            <TouchableOpacity style={styles.currentSelection}>
              <Text style={styles.currentFlag}>{currentCountry?.flag}</Text>
              <View style={styles.currentInfo}>
                <Text style={styles.currentName}>{currentCountry?.localName}</Text>
                <Text style={styles.currentDetails}>
                  {currentCountry?.config.currency} • {currentCountry?.config.measurementSystem === 'metric' ? 'Métrico' : 'Imperial'}
                </Text>
              </View>
              <Text style={styles.changeText}>Cambiar</Text>
            </TouchableOpacity>
          </View>

          {/* Language Selection */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Languages size={20} color={Colors.light.secondary} />
              <Text style={styles.sectionTitle}>Idioma</Text>
            </View>
            <View style={styles.optionsContainer}>
              {languages.map(lang => (
                <TouchableOpacity
                  key={lang.code}
                  style={[
                    styles.optionCard,
                    selectedLanguage === lang.code && styles.optionCardSelected
                  ]}
                  onPress={() => setSelectedLanguage(lang.code)}
                >
                  <Text style={styles.optionFlag}>{lang.flag}</Text>
                  <Text style={[
                    styles.optionName,
                    selectedLanguage === lang.code && styles.optionNameSelected
                  ]}>
                    {lang.name}
                  </Text>
                  {selectedLanguage === lang.code && (
                    <Check size={20} color={Colors.light.primary} />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Measurement System */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Ruler size={20} color={Colors.light.accent} />
              <Text style={styles.sectionTitle}>Sistema de Medidas</Text>
            </View>
            <View style={styles.measurementContainer}>
              {measurementSystems.map(system => (
                <TouchableOpacity
                  key={system.id}
                  style={[
                    styles.measurementCard,
                    selectedMeasurement === system.id && styles.measurementCardSelected
                  ]}
                  onPress={() => setSelectedMeasurement(system.id as 'metric' | 'imperial')}
                >
                  <View style={styles.measurementContent}>
                    <Text style={[
                      styles.measurementName,
                      selectedMeasurement === system.id && styles.measurementNameSelected
                    ]}>
                      {system.name}
                    </Text>
                    <Text style={styles.measurementDescription}>
                      {system.description}
                    </Text>
                    <Text style={styles.measurementExample}>
                      {system.example}
                    </Text>
                  </View>
                  {selectedMeasurement === system.id && (
                    <View style={styles.selectedIndicator}>
                      <Check size={16} color={Colors.light.primary} />
                    </View>
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Currency */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <DollarSign size={20} color={Colors.light.success} />
              <Text style={styles.sectionTitle}>Moneda</Text>
            </View>
            <BaseCard variant="light" shadow="sm" style={styles.currencyCard}>
              <View style={styles.currencySymbol}>
                <Text style={styles.currencySymbolText}>
                  {currentCurrency?.symbol}
                </Text>
              </View>
              <View style={styles.currencyInfo}>
                <Text style={styles.currencyCode}>{currentCurrency?.code}</Text>
                <Text style={styles.currencyName}>{currentCurrency?.name}</Text>
              </View>
              <TouchableOpacity>
                <Text style={styles.changeText}>Cambiar</Text>
              </TouchableOpacity>
            </BaseCard>
            <Text style={styles.currencyNote}>
              💡 Moneda sugerida para {currentCountry?.localName}: {currentCountry?.config.currency}
            </Text>
          </View>

          {/* Inventory Control Level */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <BarChart3 size={20} color={Colors.light.info} />
              <Text style={styles.sectionTitle}>Control de Inventario</Text>
            </View>
            <Text style={styles.sectionDescription}>
              Elige cómo quieres gestionar tu inventario de productos
            </Text>
            <View style={styles.inventoryOptionsContainer}>
              {/* Solo Formulas Option */}
              <TouchableOpacity
                style={[
                  styles.inventoryOption,
                  selectedInventoryLevel === 'solo-formulas' && styles.inventoryOptionSelected
                ]}
                onPress={() => setSelectedInventoryLevel('solo-formulas')}
              >
                <View style={[
                  styles.inventoryIconContainer,
                  selectedInventoryLevel === 'solo-formulas' && styles.inventoryIconSelected
                ]}>
                  <Palette size={24} color={
                    selectedInventoryLevel === 'solo-formulas' 
                      ? Colors.light.primary 
                      : Colors.light.gray
                  } />
                </View>
                <View style={styles.inventoryTextContainer}>
                  <Text style={[
                    styles.inventoryTitle,
                    selectedInventoryLevel === 'solo-formulas' && styles.inventoryTitleSelected
                  ]}>
                    Solo Fórmulas
                  </Text>
                  <Text style={styles.inventoryDescription}>
                    Registro básico de fórmulas sin control de stock
                  </Text>
                </View>
                {selectedInventoryLevel === 'solo-formulas' && (
                  <Check size={20} color={Colors.light.primary} />
                )}
              </TouchableOpacity>

              {/* Smart Cost Option */}
              <TouchableOpacity
                style={[
                  styles.inventoryOption,
                  selectedInventoryLevel === 'smart-cost' && styles.inventoryOptionSelected
                ]}
                onPress={() => setSelectedInventoryLevel('smart-cost')}
              >
                <View style={[
                  styles.inventoryIconContainer,
                  selectedInventoryLevel === 'smart-cost' && styles.inventoryIconSelected
                ]}>
                  <Calculator size={24} color={
                    selectedInventoryLevel === 'smart-cost' 
                      ? Colors.light.primary 
                      : Colors.light.gray
                  } />
                </View>
                <View style={styles.inventoryTextContainer}>
                  <Text style={[
                    styles.inventoryTitle,
                    selectedInventoryLevel === 'smart-cost' && styles.inventoryTitleSelected
                  ]}>
                    Cálculo Inteligente
                  </Text>
                  <Text style={styles.inventoryDescription}>
                    Calcula costos automáticamente según las fórmulas
                  </Text>
                </View>
                {selectedInventoryLevel === 'smart-cost' && (
                  <Check size={20} color={Colors.light.primary} />
                )}
              </TouchableOpacity>

              {/* Control Total Option */}
              <TouchableOpacity
                style={[
                  styles.inventoryOption,
                  selectedInventoryLevel === 'control-total' && styles.inventoryOptionSelected
                ]}
                onPress={() => setSelectedInventoryLevel('control-total')}
              >
                <View style={[
                  styles.inventoryIconContainer,
                  selectedInventoryLevel === 'control-total' && styles.inventoryIconSelected
                ]}>
                  <BarChart3 size={24} color={
                    selectedInventoryLevel === 'control-total' 
                      ? Colors.light.primary 
                      : Colors.light.gray
                  } />
                </View>
                <View style={styles.inventoryTextContainer}>
                  <Text style={[
                    styles.inventoryTitle,
                    selectedInventoryLevel === 'control-total' && styles.inventoryTitleSelected
                  ]}>
                    Control Total
                  </Text>
                  <Text style={styles.inventoryDescription}>
                    Gestión completa con alertas de stock bajo
                  </Text>
                </View>
                {selectedInventoryLevel === 'control-total' && (
                  <Check size={20} color={Colors.light.primary} />
                )}
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  title: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  saveText: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.primary,
  },
  content: {
    flex: 1,
  },
  section: {
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  currentSelection: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F7',
    padding: spacing.md,
    borderRadius: radius.md,
  },
  currentFlag: {
    fontSize: 32,
    marginRight: spacing.md,
  },
  currentInfo: {
    flex: 1,
  },
  currentName: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
    marginBottom: 2,
  },
  currentDetails: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
  changeText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.primary,
    fontWeight: typography.weights.medium,
  },
  optionsContainer: {
    gap: spacing.sm,
  },
  optionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: '#F5F5F7',
    borderRadius: radius.md,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  optionCardSelected: {
    borderColor: Colors.light.primary,
    backgroundColor: Colors.light.primary + '05',
  },
  optionFlag: {
    fontSize: 24,
    marginRight: spacing.md,
  },
  optionName: {
    flex: 1,
    fontSize: typography.sizes.base,
    color: Colors.light.text,
  },
  optionNameSelected: {
    fontWeight: typography.weights.semibold,
    color: Colors.light.primary,
  },
  measurementContainer: {
    gap: spacing.md,
  },
  measurementCard: {
    padding: spacing.lg,
    backgroundColor: '#F5F5F7',
    borderRadius: radius.md,
    borderWidth: 2,
    borderColor: 'transparent',
    flexDirection: 'row',
    alignItems: 'center',
  },
  measurementCardSelected: {
    borderColor: Colors.light.primary,
    backgroundColor: Colors.light.primary + '05',
  },
  measurementContent: {
    flex: 1,
  },
  measurementName: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  measurementNameSelected: {
    color: Colors.light.primary,
    fontWeight: typography.weights.semibold,
  },
  measurementDescription: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginBottom: spacing.xs,
  },
  measurementExample: {
    fontSize: typography.sizes.xs,
    color: Colors.light.gray,
    fontStyle: 'italic',
  },
  selectedIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.light.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
  },
  currencyCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
  },
  currencySymbol: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.light.success + '15',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  currencySymbolText: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: Colors.light.success,
  },
  currencyInfo: {
    flex: 1,
  },
  currencyCode: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  currencyName: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
  currencyNote: {
    fontSize: typography.sizes.sm,
    color: Colors.light.text,
    backgroundColor: Colors.light.accent + '10',
    padding: spacing.sm,
    borderRadius: radius.sm,
    marginTop: spacing.md,
  },
  sectionDescription: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginBottom: spacing.lg,
  },
  inventoryOptionsContainer: {
    gap: spacing.sm,
  },
  inventoryOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: '#F5F5F7',
    borderRadius: radius.md,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  inventoryOptionSelected: {
    borderColor: Colors.light.primary,
    backgroundColor: Colors.light.primary + '05',
  },
  inventoryIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  inventoryIconSelected: {
    backgroundColor: Colors.light.primary + '15',
  },
  inventoryTextContainer: {
    flex: 1,
  },
  inventoryTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  inventoryTitleSelected: {
    color: Colors.light.primary,
    fontWeight: typography.weights.semibold,
  },
  inventoryDescription: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
});
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Modal,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
} from 'react-native';
import { X, AlertCircle, Plus } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { typography, spacing, radius } from '@/constants/theme';
import { COMMON_ALLERGIES, searchAllergies, Allergy } from '@/constants/common-allergies';

interface AllergyAutocompleteProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  style?: any;
}

export default function AllergyAutocomplete({
  value,
  onChangeText,
  placeholder = 'Ej: PPD, amoníaco, níquel...',
  style,
}: AllergyAutocompleteProps) {
  const [inputText, setInputText] = useState('');
  const [selectedAllergies, setSelectedAllergies] = useState<string[]>([]);
  const [suggestions, setSuggestions] = useState<Allergy[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [showAllergiesModal, setShowAllergiesModal] = useState(false);
  const inputRef = useRef<TextInput>(null);

  // Parse initial value
  useEffect(() => {
    if (value) {
      const allergies = value.split(',').map(a => a.trim()).filter(Boolean);
      setSelectedAllergies(allergies);
    }
  }, []);

  // Update parent when selected allergies change
  useEffect(() => {
    onChangeText(selectedAllergies.join(', '));
  }, [selectedAllergies]);

  // Search allergies as user types
  useEffect(() => {
    if (inputText.length > 0) {
      const results = searchAllergies(inputText);
      setSuggestions(results.slice(0, 5)); // Limit to 5 suggestions
      setShowSuggestions(results.length > 0);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  }, [inputText]);

  const addAllergy = (allergy: string) => {
    if (!selectedAllergies.includes(allergy)) {
      setSelectedAllergies([...selectedAllergies, allergy]);
    }
    setInputText('');
    setShowSuggestions(false);
    Keyboard.dismiss();
  };

  const removeAllergy = (allergy: string) => {
    setSelectedAllergies(selectedAllergies.filter(a => a !== allergy));
  };

  const handleInputSubmit = () => {
    if (inputText.trim() && !selectedAllergies.includes(inputText.trim())) {
      addAllergy(inputText.trim());
    }
  };

  const getCategoryColor = (category: Allergy['category']) => {
    switch (category) {
      case 'chemical': return Colors.light.error;
      case 'metal': return Colors.light.warning;
      case 'preservative': return Colors.light.info;
      case 'fragrance': return Colors.light.success;
      default: return Colors.light.textSecondary;
    }
  };

  const getSeverityIcon = (severity: Allergy['severity']) => {
    switch (severity) {
      case 'high': return '⚠️';
      case 'medium': return '⚡';
      case 'low': return 'ℹ️';
    }
  };

  return (
    <View style={[styles.container, style]}>
      {/* Selected allergies chips */}
      {selectedAllergies.length > 0 && (
        <View style={styles.chipsContainer}>
          {selectedAllergies.map((allergy) => (
            <View key={allergy} style={styles.chip}>
              <Text style={styles.chipText}>{allergy}</Text>
              <TouchableOpacity onPress={() => removeAllergy(allergy)}>
                <X size={14} color={Colors.light.textSecondary} />
              </TouchableOpacity>
            </View>
          ))}
        </View>
      )}

      {/* Input field */}
      <View style={styles.inputContainer}>
        <TextInput
          ref={inputRef}
          style={styles.input}
          value={inputText}
          onChangeText={setInputText}
          placeholder={selectedAllergies.length > 0 ? 'Añadir más...' : placeholder}
          placeholderTextColor={Colors.light.textSecondary}
          onSubmitEditing={handleInputSubmit}
          returnKeyType="done"
        />
        <TouchableOpacity 
          style={styles.browseButton}
          onPress={() => setShowAllergiesModal(true)}
        >
          <Plus size={20} color={Colors.light.primary} />
        </TouchableOpacity>
      </View>

      {/* Suggestions dropdown */}
      {showSuggestions && (
        <View style={styles.suggestionsContainer}>
          <ScrollView keyboardShouldPersistTaps="handled">
            {suggestions.map((allergy) => (
              <TouchableOpacity
                key={allergy.id}
                style={styles.suggestion}
                onPress={() => addAllergy(allergy.name)}
              >
                <View style={styles.suggestionContent}>
                  <Text style={styles.suggestionIcon}>
                    {getSeverityIcon(allergy.severity)}
                  </Text>
                  <View style={styles.suggestionInfo}>
                    <Text style={styles.suggestionName}>{allergy.name}</Text>
                    {allergy.relatedTo && (
                      <Text style={styles.suggestionRelated}>
                        {allergy.relatedTo.slice(0, 2).join(', ')}
                      </Text>
                    )}
                  </View>
                  <View 
                    style={[
                      styles.categoryBadge, 
                      { backgroundColor: getCategoryColor(allergy.category) + '20' }
                    ]}
                  >
                    <Text 
                      style={[
                        styles.categoryText,
                        { color: getCategoryColor(allergy.category) }
                      ]}
                    >
                      {getCategoryLabel(allergy.category)}
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            ))}
            
            {/* Option to add custom allergy */}
            <TouchableOpacity
              style={[styles.suggestion, styles.customSuggestion]}
              onPress={handleInputSubmit}
            >
              <Plus size={16} color={Colors.light.primary} />
              <Text style={styles.customSuggestionText}>
                Añadir "{inputText}" como alergia personalizada
              </Text>
            </TouchableOpacity>
          </ScrollView>
        </View>
      )}

      {/* Browse all allergies modal */}
      <Modal
        visible={showAllergiesModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowAllergiesModal(false)}
      >
        <KeyboardAvoidingView 
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.modalContainer}
        >
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Alergias Comunes</Text>
              <TouchableOpacity onPress={() => setShowAllergiesModal(false)}>
                <X size={24} color={Colors.light.text} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              {/* High severity allergies */}
              <View style={styles.categorySection}>
                <View style={styles.categoryHeader}>
                  <AlertCircle size={18} color={Colors.light.error} />
                  <Text style={styles.categoryTitle}>Alta Severidad</Text>
                </View>
                {COMMON_ALLERGIES.filter(a => a.severity === 'high').map(allergy => (
                  <TouchableOpacity
                    key={allergy.id}
                    style={[
                      styles.modalAllergy,
                      selectedAllergies.includes(allergy.name) && styles.modalAllergySelected
                    ]}
                    onPress={() => {
                      addAllergy(allergy.name);
                      setShowAllergiesModal(false);
                    }}
                  >
                    <Text style={styles.modalAllergyName}>{allergy.name}</Text>
                    <Text style={styles.modalAllergyRelated}>
                      {allergy.relatedTo?.join(', ')}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              {/* Group by category */}
              {(['chemical', 'metal', 'preservative', 'fragrance', 'other'] as const).map(category => {
                const categoryAllergies = COMMON_ALLERGIES.filter(
                  a => a.category === category && a.severity !== 'high'
                );
                
                if (categoryAllergies.length === 0) return null;

                return (
                  <View key={category} style={styles.categorySection}>
                    <Text style={styles.categoryTitle}>{getCategoryLabel(category)}</Text>
                    {categoryAllergies.map(allergy => (
                      <TouchableOpacity
                        key={allergy.id}
                        style={[
                          styles.modalAllergy,
                          selectedAllergies.includes(allergy.name) && styles.modalAllergySelected
                        ]}
                        onPress={() => {
                          addAllergy(allergy.name);
                          setShowAllergiesModal(false);
                        }}
                      >
                        <Text style={styles.modalAllergyName}>{allergy.name}</Text>
                        {allergy.relatedTo && (
                          <Text style={styles.modalAllergyRelated}>
                            {allergy.relatedTo.join(', ')}
                          </Text>
                        )}
                      </TouchableOpacity>
                    ))}
                  </View>
                );
              })}
            </ScrollView>
          </View>
        </KeyboardAvoidingView>
      </Modal>
    </View>
  );
}

function getCategoryLabel(category: Allergy['category']): string {
  switch (category) {
    case 'chemical': return 'Químicos';
    case 'metal': return 'Metales';
    case 'preservative': return 'Conservantes';
    case 'fragrance': return 'Fragancias';
    case 'other': return 'Otros';
  }
}

const styles = StyleSheet.create({
  container: {
    marginBottom: spacing.sm,
  },
  chipsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: spacing.sm,
    gap: spacing.xs,
  },
  chip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.primary + '20',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: radius.full,
    gap: spacing.xs,
  },
  chipText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.primary,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  input: {
    flex: 1,
    backgroundColor: '#F5F5F7',
    borderRadius: radius.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm + 2,
    fontSize: typography.sizes.base,
    color: Colors.light.text,
  },
  browseButton: {
    backgroundColor: '#F5F5F7',
    padding: spacing.sm + 2,
    borderRadius: radius.md,
  },
  suggestionsContainer: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderRadius: radius.md,
    marginTop: spacing.xs,
    maxHeight: 200,
    zIndex: 1000,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  suggestion: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  suggestionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  suggestionIcon: {
    fontSize: typography.sizes.base,
  },
  suggestionInfo: {
    flex: 1,
  },
  suggestionName: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
  },
  suggestionRelated: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
    marginTop: 2,
  },
  categoryBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: 2,
    borderRadius: radius.sm,
  },
  categoryText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium,
  },
  customSuggestion: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    backgroundColor: '#F5F5F7',
  },
  customSuggestionText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.primary,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: radius.lg,
    borderTopRightRadius: radius.lg,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  modalTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  modalBody: {
    padding: spacing.lg,
  },
  categorySection: {
    marginBottom: spacing.lg,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    marginBottom: spacing.sm,
  },
  categoryTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.sm,
  },
  modalAllergy: {
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    backgroundColor: '#F5F5F7',
    borderRadius: radius.md,
    marginBottom: spacing.xs,
  },
  modalAllergySelected: {
    backgroundColor: Colors.light.primary + '20',
    borderWidth: 1,
    borderColor: Colors.light.primary,
  },
  modalAllergyName: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
  },
  modalAllergyRelated: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
    marginTop: 2,
  },
});
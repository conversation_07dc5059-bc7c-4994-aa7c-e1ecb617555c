/**
 * Logger utility for Salonier
 * 
 * A centralized logging system that provides different log levels
 * and conditional output based on environment (development vs production).
 * 
 * In development: All logs are shown
 * In production: Only errors are shown
 * 
 * @module utils/logger
 */

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogEntry {
  level: LogLevel;
  timestamp: string;
  context?: string;
  message: string;
  data?: any;
  duration?: number;
}

/**
 * Logger configuration
 */
const config = {
  isDevelopment: __DEV__ || process.env.NODE_ENV === 'development',
  enablePerformanceMetrics: true,
  dateFormat: 'HH:mm:ss.SSS',
};

/**
 * Format timestamp for logs
 */
function formatTimestamp(): string {
  const now = new Date();
  const hours = now.getHours().toString().padStart(2, '0');
  const minutes = now.getMinutes().toString().padStart(2, '0');
  const seconds = now.getSeconds().toString().padStart(2, '0');
  const ms = now.getMilliseconds().toString().padStart(3, '0');
  return `${hours}:${minutes}:${seconds}.${ms}`;
}

/**
 * Format log message with timestamp and context
 */
function formatMessage(level: LogLevel, message: string, context?: string): string {
  const timestamp = formatTimestamp();
  const prefix = context ? `[${timestamp}] [${level.toUpperCase()}] [${context}]` : `[${timestamp}] [${level.toUpperCase()}]`;
  return `${prefix} ${message}`;
}

/**
 * Main logger class
 */
class Logger {
  private performanceMarks: Map<string, number> = new Map();

  /**
   * Log a debug message (development only)
   * @param message - The message to log
   * @param context - Optional context (e.g., component or function name)
   * @param data - Optional additional data
   */
  debug(message: string, context?: string, data?: any): void {
    if (!config.isDevelopment) return;
    
    const formattedMessage = formatMessage('debug', message, context);
    if (data !== undefined) {
      console.log(formattedMessage, data);
    } else {
      console.log(formattedMessage);
    }
  }

  /**
   * Log an info message (development only)
   * @param message - The message to log
   * @param context - Optional context
   * @param data - Optional additional data
   */
  info(message: string, context?: string, data?: any): void {
    if (!config.isDevelopment) return;
    
    const formattedMessage = formatMessage('info', message, context);
    if (data !== undefined) {
      console.log(formattedMessage, data);
    } else {
      console.log(formattedMessage);
    }
  }

  /**
   * Log a warning message (development only)
   * @param message - The message to log
   * @param context - Optional context
   * @param data - Optional additional data
   */
  warn(message: string, context?: string, data?: any): void {
    if (!config.isDevelopment) return;
    
    const formattedMessage = formatMessage('warn', message, context);
    if (data !== undefined) {
      console.warn(formattedMessage, data);
    } else {
      console.warn(formattedMessage);
    }
  }

  /**
   * Log an error message (always shown)
   * @param message - The error message
   * @param context - Optional context
   * @param error - Optional error object or additional data
   */
  error(message: string, context?: string, error?: any): void {
    const formattedMessage = formatMessage('error', message, context);
    if (error !== undefined) {
      // Handle Error objects specifically to avoid [object Object]
      if (error instanceof Error) {
        console.error(formattedMessage, {
          name: error.name,
          message: error.message,
          stack: error.stack,
        });
      } else if (typeof error === 'object' && error !== null) {
        console.error(formattedMessage, JSON.stringify(error, null, 2));
      } else {
        console.error(formattedMessage, error);
      }
    } else {
      console.error(formattedMessage);
    }
  }

  /**
   * Start a performance measurement
   * @param label - Unique label for the measurement
   * @param context - Optional context
   */
  startTimer(label: string, context?: string): void {
    if (!config.isDevelopment || !config.enablePerformanceMetrics) return;
    
    this.performanceMarks.set(label, Date.now());
    this.debug(`Timer started: ${label}`, context);
  }

  /**
   * End a performance measurement and log the duration
   * @param label - The label used in startTimer
   * @param context - Optional context
   * @returns Duration in milliseconds, or undefined if timer wasn't started
   */
  endTimer(label: string, context?: string): number | undefined {
    if (!config.isDevelopment || !config.enablePerformanceMetrics) return;
    
    const startTime = this.performanceMarks.get(label);
    if (!startTime) {
      this.warn(`Timer '${label}' was not started`, context);
      return undefined;
    }

    const duration = Date.now() - startTime;
    this.performanceMarks.delete(label);
    
    this.info(`Timer '${label}' completed in ${duration}ms`, context);
    return duration;
  }

  /**
   * Log a performance metric
   * @param metric - The metric name
   * @param value - The metric value
   * @param unit - The unit of measurement
   * @param context - Optional context
   */
  metric(metric: string, value: number, unit: string = 'ms', context?: string): void {
    if (!config.isDevelopment) return;
    
    this.info(`Metric: ${metric} = ${value}${unit}`, context);
  }

  /**
   * Create a child logger with a specific context
   * @param context - The context for all logs from this logger
   * @returns A new logger instance with the context pre-filled
   */
  withContext(context: string): ContextualLogger {
    return new ContextualLogger(this, context);
  }
}

/**
 * A logger with a pre-filled context
 */
class ContextualLogger {
  constructor(
    private parent: Logger,
    private context: string
  ) {}

  debug(message: string, data?: any): void {
    this.parent.debug(message, this.context, data);
  }

  info(message: string, data?: any): void {
    this.parent.info(message, this.context, data);
  }

  warn(message: string, data?: any): void {
    this.parent.warn(message, this.context, data);
  }

  error(message: string, error?: any): void {
    this.parent.error(message, this.context, error);
  }

  startTimer(label: string): void {
    this.parent.startTimer(label, this.context);
  }

  endTimer(label: string): number | undefined {
    return this.parent.endTimer(label, this.context);
  }

  metric(metric: string, value: number, unit: string = 'ms'): void {
    this.parent.metric(metric, value, unit, this.context);
  }
}

// Export singleton instance
export const logger = new Logger();

// Export for convenience
export default logger;
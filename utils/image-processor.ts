/**
 * Image Processor Service
 * 
 * Servicio centralizado para el procesamiento de imágenes
 * Maneja compresión, validación de calidad y generación de hashes
 */

import * as ImageManipulator from 'expo-image-manipulator';
import * as Crypto from 'expo-crypto';
import { logger } from './logger';
import { applyPrivacyFilter as applyPrivacyFilterUtil, getPrivacyLevel } from './privacy-filter';

// Configuración de compresión por propósito
const COMPRESSION_PROFILES = {
  diagnosis: {
    maxWidth: 400,
    quality: 0.5,
    maxSizeKB: 500,
    fallbackWidth: 300,
    fallbackQuality: 0.3
  },
  desired: {
    maxWidth: 350,
    quality: 0.4,
    maxSizeKB: 400,
    fallbackWidth: 250,
    fallbackQuality: 0.3
  },
  thumbnail: {
    maxWidth: 150,
    quality: 0.6,
    maxSizeKB: 50,
    fallbackWidth: 100,
    fallbackQuality: 0.4
  },
  storage: {
    maxWidth: 800,
    quality: 0.7,
    maxSizeKB: 1000,
    fallbackWidth: 600,
    fallbackQuality: 0.5
  },
  upload: {
    maxWidth: 1280,
    quality: 0.7,
    maxSizeKB: 2000,
    fallbackWidth: 1024,
    fallbackQuality: 0.6
  }
} as const;

type CompressionPurpose = keyof typeof COMPRESSION_PROFILES;

interface ValidationIssue {
  type: 'size' | 'dimension' | 'format' | 'quality';
  message: string;
  severity: 'warning' | 'error';
}

interface CompressionResult {
  base64: string;
  sizeKB: number;
  width: number;
  height: number;
  compressionRatio: number;
}

export class ImageProcessor {
  private static compressionCache = new Map<string, CompressionResult>();
  private static readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutos

  /**
   * Comprime una imagen según el propósito especificado
   * Utiliza cache para evitar recompresiones innecesarias
   */
  static async compressForAI(
    uri: string, 
    purpose: 'diagnosis' | 'desired'
  ): Promise<string> {
    const cacheKey = `${uri}-${purpose}`;
    const cached = this.compressionCache.get(cacheKey);
    
    if (cached && this.isCacheValid(cacheKey)) {
      logger.debug('Using cached compression for:', purpose);
      return cached.base64;
    }

    const profile = COMPRESSION_PROFILES[purpose];
    
    try {
      logger.debug(`Compressing image for ${purpose}...`);
      
      // Primera compresión
      let result = await this.compress(uri, profile.maxWidth, profile.quality);
      
      // Si excede el tamaño máximo, recomprimir
      if (result.sizeKB > profile.maxSizeKB) {
        logger.warn(`Image too large (${result.sizeKB}KB), recompressing...`);
        result = await this.compress(
          uri, 
          profile.fallbackWidth, 
          profile.fallbackQuality
        );
        
        // Si aún es muy grande, compresión agresiva
        if (result.sizeKB > profile.maxSizeKB) {
          logger.warn('Applying aggressive compression...');
          result = await this.compress(
            uri,
            profile.fallbackWidth * 0.8,
            0.25
          );
        }
      }

      logger.info(`Final compression: ${result.sizeKB}KB (${result.compressionRatio.toFixed(1)}x reduction)`);
      
      // Guardar en cache
      this.compressionCache.set(cacheKey, result);
      setTimeout(() => this.compressionCache.delete(cacheKey), this.CACHE_TTL);
      
      return result.base64;
      
    } catch (error) {
      logger.error('Compression failed:', error);
      throw new Error('Error al comprimir la imagen');
    }
  }

  /**
   * Valida la calidad y características de una imagen
   */
  static async validateQuality(uri: string): Promise<{
    valid: boolean;
    issues: ValidationIssue[];
  }> {
    const issues: ValidationIssue[] = [];
    
    try {
      // Obtener información de la imagen
      const info = await ImageManipulator.manipulateAsync(
        uri,
        [],
        { base64: false }
      );

      // Validar dimensiones
      if (info.width < 200 || info.height < 200) {
        issues.push({
          type: 'dimension',
          message: 'La imagen es demasiado pequeña (mínimo 200x200)',
          severity: 'error'
        });
      } else if (info.width < 400 || info.height < 400) {
        issues.push({
          type: 'dimension',
          message: 'Se recomienda una imagen de al menos 400x400 para mejor análisis',
          severity: 'warning'
        });
      }

      // Validar proporción
      const aspectRatio = info.width / info.height;
      if (aspectRatio < 0.5 || aspectRatio > 2) {
        issues.push({
          type: 'dimension',
          message: 'La proporción de la imagen no es óptima',
          severity: 'warning'
        });
      }

      // Estimar tamaño del archivo (aproximado)
      const tempResult = await ImageManipulator.manipulateAsync(
        uri,
        [],
        { base64: true, compress: 1 }
      );
      const sizeKB = (tempResult.base64.length * 3) / 4 / 1024;
      
      if (sizeKB > 5000) {
        issues.push({
          type: 'size',
          message: 'La imagen es demasiado grande (máximo 5MB)',
          severity: 'error'
        });
      }

      // Validación básica de calidad por tamaño
      // (imágenes muy pequeñas en KB suelen ser de baja calidad)
      if (sizeKB < 50 && info.width > 500) {
        issues.push({
          type: 'quality',
          message: 'La imagen parece tener muy baja calidad',
          severity: 'warning'
        });
      }

      const hasErrors = issues.some(i => i.severity === 'error');
      
      return {
        valid: !hasErrors,
        issues
      };
      
    } catch (error) {
      logger.error('Quality validation failed:', error);
      return {
        valid: false,
        issues: [{
          type: 'format',
          message: 'No se pudo validar la imagen',
          severity: 'error'
        }]
      };
    }
  }

  /**
   * Genera un hash único para una imagen base64
   * Útil para cache y deduplicación
   */
  static async generateHash(base64: string): Promise<string> {
    try {
      // Tomar solo una porción del base64 para el hash (más eficiente)
      const sample = base64.substring(0, 1000) + base64.length;
      const hash = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        sample
      );
      return hash.substring(0, 16); // Primeros 16 caracteres del hash
    } catch (error) {
      logger.error('Hash generation failed:', error);
      // Fallback a un hash simple
      return `${base64.length}-${Date.now()}`;
    }
  }

  /**
   * Comprime una imagen con los parámetros especificados
   */
  private static async compress(
    uri: string,
    width: number,
    quality: number
  ): Promise<CompressionResult> {
    const startTime = Date.now();
    
    const result = await ImageManipulator.manipulateAsync(
      uri,
      [{ resize: { width } }],
      { 
        compress: quality,
        format: ImageManipulator.SaveFormat.JPEG,
        base64: true
      }
    );

    const sizeKB = (result.base64.length * 3) / 4 / 1024;
    
    // Estimar tamaño original (aproximado)
    const originalResult = await ImageManipulator.manipulateAsync(
      uri,
      [],
      { base64: true, compress: 1 }
    );
    const originalSizeKB = (originalResult.base64.length * 3) / 4 / 1024;
    
    logger.debug(`Compression took ${Date.now() - startTime}ms`);
    
    return {
      base64: result.base64,
      sizeKB: Math.round(sizeKB * 10) / 10,
      width: result.width,
      height: result.height,
      compressionRatio: originalSizeKB / sizeKB
    };
  }

  /**
   * Verifica si una entrada de cache sigue siendo válida
   */
  private static isCacheValid(key: string): boolean {
    // Por ahora, confiamos en el setTimeout para limpiar el cache
    // Podríamos añadir timestamps si necesitamos más control
    return this.compressionCache.has(key);
  }

  /**
   * Limpia el cache de compresión
   */
  static clearCache(): void {
    this.compressionCache.clear();
    logger.info('Image compression cache cleared');
  }

  /**
   * Obtiene estadísticas del cache
   */
  static getCacheStats(): { size: number; entries: string[] } {
    return {
      size: this.compressionCache.size,
      entries: Array.from(this.compressionCache.keys())
    };
  }

  /**
   * Comprime una imagen para subida con opción de aplicar filtro de privacidad
   * @param uri - URI de la imagen
   * @param applyPrivacyFilter - Si aplicar pixelado en la región superior
   * @returns Base64 de la imagen procesada
   */
  static async compressForUpload(
    uri: string,
    applyPrivacyFilter: boolean = true
  ): Promise<string> {
    try {
      logger.debug('Compressing image for upload...');
      
      // Primero aplicar filtro de privacidad si es necesario
      let processedUri = uri;
      if (applyPrivacyFilter) {
        processedUri = await this.applyPrivacyFilter(uri);
      }
      
      // Luego comprimir usando el perfil 'upload'
      const profile = COMPRESSION_PROFILES.upload;
      let result = await this.compress(processedUri, profile.maxWidth, profile.quality);
      
      // Si excede el tamaño, recomprimir
      if (result.sizeKB > profile.maxSizeKB) {
        logger.warn(`Image too large (${result.sizeKB}KB), recompressing...`);
        result = await this.compress(
          processedUri, 
          profile.fallbackWidth, 
          profile.fallbackQuality
        );
      }
      
      logger.info(`Upload compression complete: ${result.sizeKB}KB`);
      return result.base64;
      
    } catch (error) {
      logger.error('Upload compression failed:', error);
      throw new Error('Error al procesar la imagen para subida');
    }
  }

  /**
   * Aplica un filtro de privacidad pixelando la región superior de la imagen
   * @param uri - URI de la imagen original
   * @returns URI de la imagen con filtro aplicado
   */
  private static async applyPrivacyFilter(uri: string): Promise<string> {
    try {
      logger.debug('Applying privacy filter...');
      
      // Usar el nuevo sistema de filtro de privacidad con nivel bajo para IA
      const privacyOptions = getPrivacyLevel('ai_analysis');
      const filteredUri = await applyPrivacyFilterUtil(uri, privacyOptions);
      
      logger.debug('Privacy filter applied successfully');
      return filteredUri;
      
    } catch (error) {
      logger.error('Privacy filter failed:', error);
      // Si falla, devolver imagen original
      return uri;
    }
  }
}
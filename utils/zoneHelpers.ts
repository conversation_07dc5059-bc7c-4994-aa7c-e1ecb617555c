import { HairZone } from '../types';

// Safe ASCII keys for React Native
export type SafeZoneKey = 'roots' | 'mids' | 'ends';

// Bidirectional mapping between safe keys and HairZone enum values
export const ZONE_KEY_MAP: Record<SafeZoneKey, HairZone> = {
  roots: HairZone.ROOTS,
  mids: HairZone.MIDS,
  ends: HairZone.ENDS,
};

export const ZONE_TO_KEY_MAP: Record<HairZone, SafeZoneKey> = {
  [HairZone.ROOTS]: 'roots',
  [HairZone.MIDS]: 'mids',
  [HairZone.ENDS]: 'ends',
};

// Display names for UI
export const ZONE_DISPLAY_NAMES: Record<SafeZoneKey, string> = {
  roots: 'Raíces',
  mids: 'Medios',
  ends: 'Puntas',
};

// Helper functions
export function getSafeZoneKey(zone: HairZone): SafeZoneKey {
  return ZONE_TO_KEY_MAP[zone];
}

export function getZoneFromKey(key: SafeZoneKey): HairZone {
  return ZONE_KEY_MAP[key];
}

export function getZoneDisplayName(key: SafeZoneKey): string {
  return ZONE_DISPLAY_NAMES[key];
}

export function getZoneDisplayNameFromEnum(zone: HairZone): string {
  const key = getSafeZoneKey(zone);
  return ZONE_DISPLAY_NAMES[key];
}

// Get all safe zone keys
export function getAllSafeZoneKeys(): SafeZoneKey[] {
  return Object.keys(ZONE_KEY_MAP) as SafeZoneKey[];
}

// Check if a string is a valid safe zone key
export function isSafeZoneKey(key: string): key is SafeZoneKey {
  return key in ZONE_KEY_MAP;
}

// Convert zone data object with HairZone keys to safe keys
export function convertToSafeZoneData<T>(data: Record<HairZone, T>): Record<SafeZoneKey, T> {
  const safeData: Partial<Record<SafeZoneKey, T>> = {};
  
  for (const [zone, value] of Object.entries(data)) {
    const safeKey = getSafeZoneKey(zone as HairZone);
    safeData[safeKey] = value;
  }
  
  return safeData as Record<SafeZoneKey, T>;
}

// Convert zone data object with safe keys back to HairZone keys
export function convertFromSafeZoneData<T>(data: Record<SafeZoneKey, T>): Record<HairZone, T> {
  const zoneData: Partial<Record<HairZone, T>> = {};
  
  for (const [key, value] of Object.entries(data)) {
    if (isSafeZoneKey(key)) {
      const zone = getZoneFromKey(key);
      zoneData[zone] = value;
    }
  }
  
  return zoneData as Record<HairZone, T>;
}
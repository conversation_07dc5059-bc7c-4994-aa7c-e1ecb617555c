/**
 * Secure Image Upload Utility
 * 
 * Handles the secure upload flow:
 * 1. Process and compress image locally
 * 2. Call simple upload Edge Function
 * 3. Return public URL of uploaded image
 * 
 * Processing moved to client for 100% reliability
 */

import { supabase } from '@/lib/supabase';
import { logger } from '@/utils/logger';
import { useAuthStore } from '@/stores/auth-store';
import { ImageProcessor } from '@/utils/image-processor';

interface SecureUploadOptions {
  clientId: string;
  photoType: 'before' | 'after' | 'desired';
  onProgress?: (progress: number) => void;
}

interface SecureUploadResult {
  success: boolean;
  publicUrl?: string;
  error?: string;
}

export class SecureImageUpload {
  private static readonly MAX_RETRIES = 3;
  private static readonly RETRY_DELAY = 1000;

  /**
   * Main entry point for secure image upload
   */
  static async uploadAndAnonymize(
    imageUri: string,
    options: SecureUploadOptions
  ): Promise<SecureUploadResult> {
    const { clientId, photoType, onProgress } = options;
    
    try {
      console.log('[SECURE-UPLOAD] ===== INICIANDO PROCESO DE SUBIDA SEGURA =====');
      console.log('[SECURE-UPLOAD] Cliente:', clientId, 'Tipo:', photoType);
      
      // Step 1: Get current salon ID
      console.log('[SECURE-UPLOAD] [1/4] Obteniendo salon ID...');
      const salonId = await this.getCurrentSalonId();
      if (!salonId) {
        throw new Error('No salon ID available. Please ensure you are logged in.');
      }
      console.log('[SECURE-UPLOAD] ✅ Salon ID obtenido:', salonId);
      
      // Step 2: Process image locally (compress + optional privacy filter)
      onProgress?.(20);
      console.log('[SECURE-UPLOAD] [2/4] Procesando imagen localmente...');
      const startTime = Date.now();
      
      // Always apply privacy filter to ensure consistency and privacy-first policy
      const applyPrivacyFilter = true; // Aplicar siempre por política de privacidad
      const processedBase64 = await ImageProcessor.compressForUpload(imageUri, applyPrivacyFilter);
      
      const processingTime = Date.now() - startTime;
      const sizeKB = (processedBase64.length * 3) / 4 / 1024;
      console.log(`[SECURE-UPLOAD] ✅ Imagen procesada en ${processingTime}ms. Tamaño: ${sizeKB.toFixed(2)} KB`);
      
      // Step 3: Validate processed image
      onProgress?.(50);
      if (!processedBase64 || processedBase64.length === 0) {
        throw new Error('El procesamiento de imagen falló - resultado vacío');
      }
      
      // Step 4: Upload to storage via simple Edge Function
      onProgress?.(70);
      console.log('[SECURE-UPLOAD] [3/4] Subiendo imagen procesada...');
      console.log('[SECURE-UPLOAD] Parámetros:', { 
        imageSize: `${sizeKB.toFixed(2)} KB`,
        salonId, 
        clientId, 
        photoType,
        privacyFilter: applyPrivacyFilter ? 'applied' : 'none'
      });
      
      const publicUrl = await this.uploadProcessedImage(processedBase64, salonId, clientId, photoType);
      console.log('[SECURE-UPLOAD] ✅ Imagen subida. URL pública:', publicUrl);
      
      onProgress?.(100);
      console.log('[SECURE-UPLOAD] ===== PROCESO COMPLETADO EXITOSAMENTE =====');
      console.log(`[SECURE-UPLOAD] Tiempo total: ${Date.now() - startTime}ms`);
      return { success: true, publicUrl };
      
    } catch (error: any) {
      console.error('[SECURE-UPLOAD] ❌ ERROR EN EL PROCESO:', error.message);
      console.error('[SECURE-UPLOAD] Stack trace:', error.stack);
      logger.error('Secure upload failed', error);
      return { 
        success: false, 
        error: error.message || 'Failed to upload image' 
      };
    }
  }

  /**
   * Get current salon ID from auth store
   */
  private static async getCurrentSalonId(): Promise<string | null> {
    const { user } = useAuthStore.getState();
    return user?.salonId || null;
  }


  /**
   * Upload processed image via simple Edge Function
   */
  private static async uploadProcessedImage(
    imageBase64: string,
    salonId: string,
    clientId: string,
    photoType: 'before' | 'after' | 'desired'
  ): Promise<string> {
    console.log('[EDGE-FUNCTION] Iniciando subida de imagen procesada...');
    console.log('[EDGE-FUNCTION] Endpoint: upload-photo');
    console.log('[EDGE-FUNCTION] Tamaño de imagen:', `${(imageBase64.length / 1024).toFixed(2)} KB`);
    
    let lastError: any;
    
    // Retry logic for robustness
    for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
      console.log(`[EDGE-FUNCTION] Intento ${attempt}/${this.MAX_RETRIES}...`);
      
      try {
        const payload = {
          imageBase64,
          salonId,
          clientId,
          photoType
        };
        
        console.log('[EDGE-FUNCTION] Enviando imagen procesada a Edge Function...');
        
        const { data, error } = await supabase.functions.invoke('upload-photo', {
          body: payload
        });
        
        console.log('[EDGE-FUNCTION] Respuesta recibida');
        
        if (error) {
          console.error(`[EDGE-FUNCTION] ❌ Error en intento ${attempt}:`, error);
          throw error;
        }
        
        if (!data) {
          throw new Error('Edge Function no devolvió data');
        }
        
        if (!data.success) {
          throw new Error(data.error || 'Edge Function reportó un fallo');
        }
        
        if (!data.publicUrl) {
          throw new Error('Edge Function no devolvió publicUrl');
        }
        
        console.log('[EDGE-FUNCTION] ✅ Éxito en intento', attempt);
        console.log('[EDGE-FUNCTION] Public URL:', data.publicUrl);
        return data.publicUrl;
        
      } catch (error: any) {
        lastError = error;
        console.error(`[EDGE-FUNCTION] Error en intento ${attempt}:`, error.message);
        logger.warn(`Upload attempt ${attempt} failed`, error);
        
        if (attempt < this.MAX_RETRIES) {
          const delay = this.RETRY_DELAY * attempt;
          console.log(`[EDGE-FUNCTION] Esperando ${delay}ms antes de reintentar...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    console.error('[EDGE-FUNCTION] ❌ Todos los intentos fallaron');
    throw new Error(
      lastError?.message || 'Failed to upload image after multiple attempts'
    );
  }


  /**
   * Delete an anonymized image from public storage
   * Used for "right to be forgotten" compliance
   */
  static async deleteAnonymizedImage(publicUrl: string): Promise<boolean> {
    try {
      // Extract path from public URL
      const url = new URL(publicUrl);
      const pathMatch = url.pathname.match(/\/storage\/v1\/object\/public\/client-photos\/(.+)/);
      
      if (!pathMatch || !pathMatch[1]) {
        throw new Error('Invalid public URL format');
      }
      
      const filePath = pathMatch[1];
      
      const { error } = await supabase.storage
        .from('client-photos')
        .remove([filePath]);
      
      if (error) {
        logger.error('Failed to delete anonymized image', error);
        return false;
      }
      
      logger.info('Anonymized image deleted', { filePath });
      return true;
      
    } catch (error) {
      logger.error('Delete anonymized image failed', error);
      return false;
    }
  }
}

// Export convenience functions
export const uploadAndAnonymizeImage = SecureImageUpload.uploadAndAnonymize.bind(SecureImageUpload);
export const deleteAnonymizedImage = SecureImageUpload.deleteAnonymizedImage.bind(SecureImageUpload);
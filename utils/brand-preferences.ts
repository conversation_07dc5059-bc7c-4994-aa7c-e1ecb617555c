import { BrandLineSelection } from '@/stores/auth-store';
import { getBrandById } from '@/constants/reference-data/brands-data';

/**
 * Get the default brand and line from user preferences
 * Falls back to Wella Professionals if no preferences are set
 */
export function getDefaultBrandAndLine(preferences: BrandLineSelection[]): {
  brandName: string;
  lineName: string;
} {
  // If no preferences, use Wella as default
  if (!preferences?.length) {
    return { 
      brandName: "Wella Professionals", 
      lineName: "Illumina Color" 
    };
  }
  
  // Get first preferred brand
  const firstPref = preferences[0];
  const brand = getBrandById(firstPref.brandId);
  
  // If brand not found, fallback to Wella
  if (!brand) {
    return { 
      brandName: "Wella Professionals", 
      lineName: "Illumina Color" 
    };
  }
  
  // If user has selected specific lines, use the first one
  // Otherwise, use the first line from the brand
  let lineName = "Illumina Color"; // fallback
  
  if (firstPref.selectedLines?.length > 0) {
    // Find the line by ID
    const lineId = firstPref.selectedLines[0];
    const line = brand.lines.find(l => l.id === lineId);
    if (line) {
      lineName = line.name;
    }
  } else if (brand.lines?.length > 0) {
    // No specific lines selected, use first available
    lineName = brand.lines[0].name;
  }
  
  return {
    brandName: brand.name,
    lineName: lineName
  };
}

/**
 * Get all preferred brands with their selected lines
 * Useful for showing a quick selection dropdown
 */
export function getAllPreferredBrands(preferences: BrandLineSelection[]): Array<{
  brandId: string;
  brandName: string;
  lines: Array<{ lineId: string; lineName: string }>;
}> {
  return preferences.map(pref => {
    const brand = getBrandById(pref.brandId);
    if (!brand) return null;
    
    const lines = pref.selectedLines.map((lineId: string) => {
      const line = brand.lines.find(l => l.id === lineId);
      return line ? { lineId, lineName: line.name } : null;
    }).filter(Boolean) as Array<{ lineId: string; lineName: string }>;
    
    return {
      brandId: pref.brandId,
      brandName: brand.name,
      lines
    };
  }).filter(Boolean) as Array<{
    brandId: string;
    brandName: string;
    lines: Array<{ lineId: string; lineName: string }>;
  }>;
}
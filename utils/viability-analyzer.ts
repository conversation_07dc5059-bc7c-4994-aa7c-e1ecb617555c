/**
 * Centralized viability analysis for hair coloring services
 * Uses professional colorimetry principles for consistent calculations
 */

import { ViabilityAnalysis } from '@/types/formulation';
import { HairZone, ZoneColorAnalysis } from '@/types/hair-diagnosis';
import { DesiredColorAnalysisResult } from '@/types/desired-analysis';

export interface ColorProcess {
  currentLevel: number;
  desiredLevel: number;
  currentState: 'natural' | 'colored' | 'bleached' | 'mixed';
  hasMetallicSalts?: boolean;
  hasHenna?: boolean;
}

export interface ProcessValidation {
  isViable: boolean;
  requiredProcesses: ProcessType[];
  warnings: string[];
  recommendedDeveloperVolume: number;
  estimatedSessions: number;
}

export enum ProcessType {
  DIRECT_COLOR = 'direct_color',
  BLEACHING = 'bleaching',
  COLOR_REMOVAL = 'color_removal',
  PRE_PIGMENTATION = 'pre_pigmentation',
  NEUTRALIZATION = 'neutralization',
  TONING = 'toning'
}

// Core colorimetry principles
const COLORIMETRY_PRINCIPLES = {
  COLOR_LIFT_LIMIT: {
    natural: 3,     // Natural hair can be lifted up to 3 levels with color
    colored: 0,     // Colored hair cannot be lifted with color
    bleached: 0     // Bleached hair cannot be lifted with color
  },
  PRE_PIGMENT_THRESHOLD: 3,  // Need pre-pigment when going darker by 3+ levels
  MAX_LIFT_PER_SESSION: 4
};

/**
 * Validates if a color process is technically viable and determines required steps
 */
function validateColorProcess(process: ColorProcess): ProcessValidation {
  const { currentLevel, desiredLevel, currentState, hasMetallicSalts, hasHenna } = process;
  const levelDifference = desiredLevel - currentLevel;
  
  const validation: ProcessValidation = {
    isViable: true,
    requiredProcesses: [],
    warnings: [],
    recommendedDeveloperVolume: 20,
    estimatedSessions: 1
  };
  
  // Check for incompatible products
  if (hasMetallicSalts) {
    validation.warnings.push('Presencia de sales metálicas detectada. Requiere test de mechón y posible tratamiento de eliminación.');
    validation.isViable = false;
    return validation;
  }
  
  if (hasHenna) {
    validation.warnings.push('Presencia de henna detectada. La decoloración puede causar reacciones impredecibles.');
    validation.warnings.push('Se recomienda cortar el cabello con henna o esperar a que crezca.');
  }
  
  // Determine process based on level change and current state
  if (levelDifference > 0) {
    // LIGHTENING PROCESS
    const maxLiftWithColor = COLORIMETRY_PRINCIPLES.COLOR_LIFT_LIMIT[currentState];
    
    if (levelDifference > maxLiftWithColor) {
      // Cannot achieve with color alone
      if (currentState === 'colored') {
        validation.requiredProcesses.push(ProcessType.COLOR_REMOVAL);
        validation.warnings.push('Color no levanta color. Se requiere decapado previo para eliminar pigmentos artificiales.');
      }
      
      validation.requiredProcesses.push(ProcessType.BLEACHING);
      
      // Calculate sessions needed
      const liftsNeeded = levelDifference - maxLiftWithColor;
      validation.estimatedSessions = Math.ceil(liftsNeeded / COLORIMETRY_PRINCIPLES.MAX_LIFT_PER_SESSION);
      
      if (validation.estimatedSessions > 1) {
        validation.warnings.push(`Se requieren ${validation.estimatedSessions} sesiones para alcanzar el nivel deseado de forma segura.`);
      }
      
      // Set developer volume for bleaching
      validation.recommendedDeveloperVolume = 30;
    } else {
      // Can achieve with color
      validation.requiredProcesses.push(ProcessType.DIRECT_COLOR);
      
      // Determine developer volume based on lift needed
      if (levelDifference <= 1) {
        validation.recommendedDeveloperVolume = 20;
      } else if (levelDifference <= 2) {
        validation.recommendedDeveloperVolume = 30;
      } else {
        validation.recommendedDeveloperVolume = 40;
      }
    }
    
  } else if (levelDifference < 0) {
    // DARKENING PROCESS
    const levelsDarker = Math.abs(levelDifference);
    
    if (levelsDarker >= COLORIMETRY_PRINCIPLES.PRE_PIGMENT_THRESHOLD) {
      validation.requiredProcesses.push(ProcessType.PRE_PIGMENTATION);
      validation.warnings.push(`Pre-pigmentación requerida al oscurecer ${levelsDarker} niveles para evitar resultados verdosos.`);
    }
    
    validation.requiredProcesses.push(ProcessType.DIRECT_COLOR);
    
    // For darkening, only need low volume developer
    validation.recommendedDeveloperVolume = 10;
    
  } else {
    // SAME LEVEL (tone change)
    validation.requiredProcesses.push(ProcessType.DIRECT_COLOR);
    
    // Check if it's just a toning process
    if (currentState === 'bleached' || currentLevel >= 9) {
      validation.requiredProcesses = [ProcessType.TONING];
      validation.recommendedDeveloperVolume = 10;
    } else {
      validation.recommendedDeveloperVolume = 20;
    }
  }
  
  // Add specific warnings for challenging processes
  if (currentLevel <= 3 && desiredLevel >= 9) {
    validation.warnings.push('Proceso de alto riesgo: aclarar cabello muy oscuro a rubio muy claro puede causar daño severo.');
    validation.warnings.push('Se recomienda encarecidamente realizar el proceso en múltiples sesiones con tratamientos intermedios.');
  }
  
  if (currentState === 'bleached' && levelDifference < -3) {
    validation.warnings.push('Oscurecer cabello decolorado requiere especial atención a la porosidad.');
    validation.warnings.push('Considerar usar un relleno de color o tratamiento de porosidad previo.');
  }
  
  return validation;
}

/**
 * Analyzes service viability using colorimetry principles
 * This is the centralized function that all components should use
 */
export function analyzeServiceViability(
  analysisResult: any,
  desiredAnalysisResult: DesiredColorAnalysisResult,
  zoneColorAnalysis: Record<HairZone, Partial<ZoneColorAnalysis>>
): ViabilityAnalysis {
  // Extract current and desired levels
  const currentLevel = analysisResult?.level || 
                      analysisResult?.averageLevel || 
                      analysisResult?.averageDepthLevel || 
                      zoneColorAnalysis?.[HairZone.ROOTS]?.level || 
                      5;
  
  const targetLevel = desiredAnalysisResult?.general?.detectedLevel ||
                     parseInt(desiredAnalysisResult?.general?.overallLevel?.split('/')[0] || "7") || 
                     7;
  
  const levelDifference = Math.abs(targetLevel - currentLevel);
  
  // Determine current hair state
  const detectedProcess = analysisResult?.detectedChemicalProcess?.toLowerCase() || '';
  const state = analysisResult?.state?.toLowerCase() || '';
  
  const currentState = detectedProcess.includes('color') || 
                      state.includes('teñido') ||
                      state.includes('colored') ? 'colored' :
                      detectedProcess.includes('bleach') ||
                      state.includes('decolorado') ||
                      state.includes('bleached') ? 'bleached' : 
                      'natural';
  
  // Create color process for validation
  const colorProcess: ColorProcess = {
    currentLevel: Math.round(currentLevel),
    desiredLevel: Math.round(targetLevel),
    currentState: currentState as 'natural' | 'colored' | 'bleached',
    hasMetallicSalts: analysisResult?.detectedRisks?.metallic || false,
    hasHenna: analysisResult?.detectedRisks?.henna || false
  };
  
  // Validate using colorimetry principles
  const validation = validateColorProcess(colorProcess);
  
  // Analyze hair health
  const rootsPhysical = zoneColorAnalysis?.[HairZone.ROOTS];
  const midsPhysical = zoneColorAnalysis?.[HairZone.MIDS];
  const endsPhysical = zoneColorAnalysis?.[HairZone.ENDS];
  
  const hasSevereDamage = 
    rootsPhysical?.damage === 'Alto' || 
    midsPhysical?.damage === 'Alto' || 
    endsPhysical?.damage === 'Alto';

  const hasModerateDamage = 
    rootsPhysical?.damage === 'Medio' || 
    midsPhysical?.damage === 'Medio' || 
    endsPhysical?.damage === 'Medio';
  
  // Determine viability score
  let score: 'safe' | 'caution' | 'risky' = 'safe';
  let hairHealth: 'good' | 'fair' | 'poor' = 'good';
  
  if (!validation.isViable || hasSevereDamage || validation.estimatedSessions > 2) {
    score = 'risky';
    hairHealth = 'poor';
  } else if (validation.warnings.length > 0 || hasModerateDamage || validation.estimatedSessions > 1) {
    score = 'caution';
    hairHealth = hasModerateDamage ? 'fair' : 'good';
  }
  
  // Generate additional warnings based on hair condition
  const warnings = [...validation.warnings];
  if (hasSevereDamage) {
    warnings.push('Cabello muy dañado, considerar tratamiento previo');
  }
  
  // Generate recommendations
  const recommendations: string[] = [];
  if (levelDifference > 2 || validation.requiredProcesses.length > 1) {
    recommendations.push('Realizar prueba de mecha antes del servicio');
  }
  if (hasSevereDamage || hasModerateDamage) {
    recommendations.push('Aplicar tratamiento reconstructor 1-2 semanas antes');
  }
  if (validation.estimatedSessions > 1) {
    recommendations.push('Considerar realizarlo en múltiples sesiones');
  }
  if (validation.requiredProcesses.includes(ProcessType.COLOR_REMOVAL)) {
    recommendations.push('Evaluar resultado del decapado antes de proceder con la decoloración');
  }
  if (validation.requiredProcesses.includes(ProcessType.PRE_PIGMENTATION)) {
    recommendations.push('Aplicar pre-pigmentación para evitar tonos no deseados');
  }
  
  return {
    score,
    factors: {
      levelDifference,
      hairHealth,
      chemicalHistory: analysisResult?.lastChemicalProcessType ? [analysisResult.lastChemicalProcessType] : [],
      estimatedSessions: validation.estimatedSessions
    },
    warnings,
    recommendations
  };
}

/**
 * Get colorimetry validation for use in Edge Functions
 * Returns the same validation used in the app
 */
export function getColorimetryValidation(
  currentLevel: number,
  desiredLevel: number,
  currentState: 'natural' | 'colored' | 'bleached',
  hasMetallicSalts: boolean = false,
  hasHenna: boolean = false
): ProcessValidation {
  return validateColorProcess({
    currentLevel,
    desiredLevel,
    currentState,
    hasMetallicSalts,
    hasHenna
  });
}
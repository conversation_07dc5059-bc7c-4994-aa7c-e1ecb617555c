import { supabase } from '@/lib/supabase';

export interface EdgeFunctionError {
  type: 'network' | 'auth' | 'validation' | 'ai' | 'timeout' | 'unknown';
  message: string;
  userFriendlyMessage: string;
  httpStatus?: number;
  canRetry: boolean;
}

export function categorizeError(error: any): EdgeFunctionError {
  const errorMessage = error?.message || error?.toString() || 'Unknown error';
  
  // Network errors
  if (errorMessage.includes('fetch') || errorMessage.includes('network') || errorMessage.includes('connection')) {
    return {
      type: 'network',
      message: errorMessage,
      userFriendlyMessage: 'No se pudo conectar al servidor. Verifica tu conexión a internet.',
      canRetry: true
    };
  }
  
  // Timeout errors
  if (errorMessage.includes('timeout') || errorMessage.includes('Timeout')) {
    return {
      type: 'timeout',
      message: errorMessage,
      userFriendlyMessage: 'El análisis tardó demasiado tiempo. Intenta de nuevo.',
      canRetry: true
    };
  }
  
  // Authentication errors
  if (errorMessage.includes('auth') || errorMessage.includes('unauthorized') || errorMessage.includes('401')) {
    return {
      type: 'auth',
      message: errorMessage,
      userFriendlyMessage: 'Sesión expirada. Cierra y vuelve a abrir la aplicación.',
      canRetry: false
    };
  }
  
  // Validation errors (product not recognized)
  if (errorMessage.includes('No se pudo identificar el producto') || errorMessage.includes('Could not identify')) {
    return {
      type: 'validation',
      message: errorMessage,
      userFriendlyMessage: 'Producto no reconocido. Usa el nombre completo (ej: "Wella Koleston 8/0").',
      canRetry: false
    };
  }
  
  // AI processing errors
  if (errorMessage.includes('AI') || errorMessage.includes('OpenAI') || errorMessage.includes('analyze')) {
    return {
      type: 'ai',
      message: errorMessage,
      userFriendlyMessage: 'Error procesando con IA. Intenta de nuevo en un momento.',
      canRetry: true
    };
  }
  
  // Parse HTTP status if available
  let httpStatus;
  const statusMatch = errorMessage.match(/HTTP (\d+)/);
  if (statusMatch) {
    httpStatus = parseInt(statusMatch[1]);
    
    if (httpStatus >= 500) {
      return {
        type: 'unknown',
        message: errorMessage,
        userFriendlyMessage: 'Error temporal del servidor. Intenta de nuevo.',
        httpStatus,
        canRetry: true
      };
    }
    
    if (httpStatus >= 400) {
      return {
        type: 'validation',
        message: errorMessage,
        userFriendlyMessage: 'Datos inválidos. Verifica el nombre del producto.',
        httpStatus,
        canRetry: false
      };
    }
  }
  
  // Default unknown error
  return {
    type: 'unknown',
    message: errorMessage,
    userFriendlyMessage: 'Error inesperado. Intenta de nuevo o completa manualmente.',
    canRetry: true
  };
}

export async function callEdgeFunctionWithRetry(
  functionName: string,
  payload: any,
  maxRetries: number = 2
): Promise<any> {
  let lastError: EdgeFunctionError | null = null;
  
  // Get current session first
  const { data: { session }, error: sessionError } = await supabase.auth.getSession();
  
  if (sessionError || !session) {
    throw {
      type: 'auth',
      message: 'No active session',
      userFriendlyMessage: 'Sesión expirada. Cierra y vuelve a abrir la aplicación.',
      canRetry: false
    };
  }
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔄 Attempt ${attempt + 1}/${maxRetries + 1} calling ${functionName}`);
      
      // Try raw HTTP first for better error information
      const response = await fetch(`https://ajsamgugqfbttkrlgvbr.supabase.co/functions/v1/${functionName}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify(payload)
      });
      
      console.log(`🌐 Raw HTTP Status: ${response.status}`);
      
      const responseText = await response.text();
      console.log(`🌐 Raw HTTP Body: ${responseText}`);
      
      if (!response.ok) {
        // If 401, try to refresh the session
        if (response.status === 401 && attempt < maxRetries) {
          console.log('🔐 Token expired, attempting to refresh...');
          const { data: { session: newSession }, error: refreshError } = await supabase.auth.refreshSession();
          
          if (refreshError || !newSession) {
            throw new Error(`HTTP 401: Session refresh failed`);
          }
          
          // Update session for next attempt
          session.access_token = newSession.access_token;
          console.log('✅ Session refreshed successfully');
          
          // Continue to next attempt
          throw new Error(`HTTP 401: ${responseText}`);
        }
        
        throw new Error(`HTTP ${response.status}: ${responseText}`);
      }
      
      const data = JSON.parse(responseText);
      
      if (!data?.success) {
        throw new Error(data?.error || 'Function returned unsuccessful response');
      }
      
      return data.data;
      
    } catch (error) {
      lastError = categorizeError(error);
      
      console.error(`❌ Attempt ${attempt + 1} failed:`, {
        type: lastError.type,
        message: lastError.message,
        canRetry: lastError.canRetry
      });
      
      // Don't retry if error type is not retryable
      if (!lastError.canRetry) {
        break;
      }
      
      // Don't retry on last attempt
      if (attempt === maxRetries) {
        break;
      }
      
      // Wait before retrying (exponential backoff)
      const waitTime = Math.pow(2, attempt) * 1000;
      console.log(`⏱️ Waiting ${waitTime}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }
  
  // If we get here, all attempts failed
  throw lastError || new Error('All retry attempts failed');
}
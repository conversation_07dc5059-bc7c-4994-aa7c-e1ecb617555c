import { Permission, PERMISSIONS } from '@/types/permissions';

/**
 * Converts boolean permission properties to an array of Permission strings
 * Maps UI boolean flags to actual permission constants
 */
export function booleanPermissionsToArray(permissions: {
  canCreateServices?: boolean;
  canAccessInventory?: boolean;
  canViewReports?: boolean;
  canManageClients?: boolean;
}): Permission[] {
  const result: Permission[] = [];
  
  // Note: canCreateServices doesn't have a direct mapping in the current permission system
  // It might need to be added to PERMISSIONS if required
  
  if (permissions.canAccessInventory) {
    result.push(PERMISSIONS.MANAGE_INVENTORY);
  }
  
  if (permissions.canViewReports) {
    result.push(PERMISSIONS.VIEW_REPORTS);
  }
  
  if (permissions.canManageClients) {
    result.push(PERMISSIONS.VIEW_ALL_CLIENTS);
  }
  
  return result;
}

/**
 * Converts an array of Permission strings to boolean permission properties
 * Used for populating UI forms from stored permissions
 */
export function arrayToBooleanPermissions(permissions: Permission[] = []): {
  canCreateServices: boolean;
  canAccessInventory: boolean;
  canViewReports: boolean;
  canManageClients: boolean;
} {
  return {
    // Default to true since there's no direct mapping for this permission
    canCreateServices: true,
    canAccessInventory: permissions.includes(PERMISSIONS.MANAGE_INVENTORY),
    canViewReports: permissions.includes(PERMISSIONS.VIEW_REPORTS),
    canManageClients: permissions.includes(PERMISSIONS.VIEW_ALL_CLIENTS),
  };
}

/**
 * Checks if a user has a specific permission
 */
export function hasPermission(permissions: Permission[] = [], permission: Permission): boolean {
  return permissions.includes(permission);
}
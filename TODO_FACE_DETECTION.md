# TODO: Implementación de Detección Real de Rostros

## 🎯 Plan de Trabajo [2025-01-18] - Detección Real de Rostros en Edge Function

### Análisis del Problema
- **Problema identificado**: La Edge Function usa un placeholder que asume rostros en la parte superior de la imagen
- **Requisitos**:
  - Detección precisa de rostros en imágenes de peluquería
  - Funcionamiento en Deno (Edge Functions de Supabase)
  - Performance < 5 segundos por imagen
  - Soporte para múltiples rostros
  - Alta precisión para evitar falsos positivos
- **Riesgos identificados**: 
  - Limitaciones de librerías en Deno
  - Performance en Edge Functions
  - Costo computacional

### Opciones Evaluadas

#### Opción 1: Face-API.js con TensorFlow.js (RECOMENDADA ✅)
**Pros:**
- Librería JavaScript pura, compatible con Deno
- Modelos pre-entrenados de alta precisión
- Detecta múltiples rostros y landmarks
- Open source y gratuito
- ~6MB de modelos (aceptable para Edge Function)

**Contras:**
- Requiere cargar modelos en memoria
- Puede aumentar cold start de la función

**Implementación:**
```typescript
import * as faceapi from 'https://cdn.skypack.dev/@vladmandic/face-api';
```

#### Opción 2: API Externa (AWS Rekognition / Azure Face API)
**Pros:**
- Alta precisión
- Sin carga computacional local
- Actualizaciones automáticas

**Contras:**
- Latencia adicional de red
- Costo por llamada API
- Dependencia externa
- Posibles problemas de privacidad

#### Opción 3: OpenCV.js
**Pros:**
- Librería robusta y probada
- Múltiples algoritmos disponibles

**Contras:**
- Muy pesada para Edge Functions
- Compleja configuración en Deno
- Overkill para solo detección de rostros

### Plan de Implementación (Face-API.js)

#### Fase 1: Preparación de Modelos
- [✅] Descargar modelos necesarios (SSD MobileNet v1)
- [✅] Optimizar modelos para tamaño mínimo
- [✅] Hospedar modelos en CDN público
- [✅] Crear sistema de carga lazy de modelos

#### Fase 2: Integración en Edge Function
- [✅] Instalar dependencias face-api.js para Deno
- [✅] Implementar carga de modelos con cache
- [✅] Reemplazar función detectFaces() placeholder
- [✅] Ajustar coordenadas para formato correcto
- [✅] Implementar detección de confianza mínima
- [✅] **Desplegada v4 con Human library** (2025-01-18)

#### Fase 3: Optimización de Performance
- [ ] Implementar resize de imagen antes de detección
- [✅] Cache de modelos entre invocaciones
- [✅] Ajustar parámetros de detección
- [ ] Medir y optimizar tiempos

#### Fase 4: Mejoras en Anonimización
- [✅] Expandir área de blur (margin de seguridad)
- [ ] Detectar orientación de rostro
- [✅] Aplicar blur gradual en bordes (opcional)
- [ ] Opción de blur por zonas (ojos, boca)

#### Fase 5: Testing y Validación
- [✅] Script de testing local creado
- [ ] Tests con imágenes de peluquería reales
- [ ] Validar diferentes ángulos y poses
- [ ] Probar con múltiples personas
- [ ] Verificar falsos positivos/negativos
- [ ] Medir performance en Edge Function

### Código de Implementación Propuesto

```typescript
// anonymize-and-store/face-detection.ts
import * as faceapi from 'https://cdn.skypack.dev/@vladmandic/face-api@1.7.12';

let modelsLoaded = false;

async function loadModels() {
  if (modelsLoaded) return;
  
  const MODEL_URL = 'https://yourbucket.supabase.co/storage/v1/object/public/models';
  
  await Promise.all([
    faceapi.nets.ssdMobilenetv1.loadFromUri(MODEL_URL),
    // Solo cargar el modelo de detección, no necesitamos landmarks
  ]);
  
  modelsLoaded = true;
}

export async function detectFaces(imageBuffer: Uint8Array): Promise<FaceRegion[]> {
  // Cargar modelos si no están cargados
  await loadModels();
  
  // Convertir buffer a tensor
  const img = await createImageFromBuffer(imageBuffer);
  
  // Detectar rostros con umbral de confianza
  const detections = await faceapi
    .detectAllFaces(img, new faceapi.SsdMobilenetv1Options({
      minConfidence: 0.5, // Ajustar según testing
      maxResults: 10      // Máximo de rostros a detectar
    }));
  
  // Convertir a formato FaceRegion con margen de seguridad
  return detections.map(detection => {
    const box = detection.box;
    const margin = 0.2; // 20% de margen adicional
    
    return {
      x: Math.max(0, (box.x - box.width * margin) / img.width),
      y: Math.max(0, (box.y - box.height * margin) / img.height),
      width: Math.min(1, (box.width * (1 + margin * 2)) / img.width),
      height: Math.min(1, (box.height * (1 + margin * 2)) / img.height)
    };
  });
}
```

### Configuración de Modelos

```bash
# Descargar modelos necesarios
mkdir models
cd models
wget https://github.com/vladmandic/face-api/raw/main/model/ssd_mobilenetv1_model-weights_manifest.json
wget https://github.com/vladmandic/face-api/raw/main/model/ssd_mobilenetv1_model.weights

# Subir a Supabase Storage
supabase storage upload models/ssd_mobilenetv1_model-weights_manifest.json
supabase storage upload models/ssd_mobilenetv1_model.weights
```

### Métricas de Éxito
- ✅ Detección correcta en 95%+ de casos
- ✅ Sin falsos positivos en imágenes sin rostros
- ✅ Performance < 3 segundos por imagen
- ✅ Funciona con rostros parciales
- ✅ Soporta múltiples personas

### Consideraciones de Privacidad
- Los modelos se ejecutan localmente (no envío a terceros)
- No se almacenan datos de detección
- Solo se usa para anonimización inmediata
- Cumple con GDPR al no identificar personas

### Testing Plan

1. **Dataset de Prueba**
   - 50 fotos de peluquería con rostros
   - 20 fotos sin rostros (solo cabello)
   - 10 fotos con rostros parciales
   - 10 fotos con múltiples personas

2. **Métricas a Medir**
   - Precisión de detección
   - Tiempo de procesamiento
   - Uso de memoria
   - Tamaño de Edge Function

3. **Casos Edge**
   - Rostros de perfil
   - Rostros parcialmente cubiertos
   - Reflejos en espejos
   - Fotos borrosas

### Alternativa de Contingencia

Si Face-API.js presenta problemas en producción, implementar un servicio híbrido:

1. **Detección básica local** (actual placeholder mejorado)
2. **Validación opcional** con API externa para casos dudosos
3. **Modo "paranoid"** que aplica blur a zonas comunes sin detección

---

## Estado: DESPLEGADO ✅

Iniciado: 2025-01-18
Última actualización: 2025-01-18

### Despliegue Completado
- ✅ Edge Function `anonymize-and-store` desplegada (v1)
- ✅ Migración SQL aplicada exitosamente
- ✅ Bucket `originals-for-anonymization` creado
- ✅ Bucket `client-photos` configurado como público
- ✅ Funciones de limpieza implementadas

### Verificación
- ✅ Buckets creados correctamente
- ✅ Límites de tamaño configurados (10MB)
- ✅ Tipos MIME permitidos: JPEG, PNG, WebP
- ✅ Función de limpieza automática disponible

### Próximos Pasos
1. ✅ **Reparar error de importación iOS**: Error `service-flow-store` corregido → `service-draft-store`
2. ✅ **Verificar Edge Function**: Function responde correctamente en producción
3. ✅ **Crear bucket faltante**: Bucket `client-photos` creado y configurado como público
4. ✅ **Reparar error de contexto cliente**: Función `performAnalysis` ahora recibe `clientId` como parámetro
5. ✅ **Verificar compilación JS**: Metro bundler funciona correctamente
6. ✅ **Reparar error de salon ID**: `getCurrentSalonId()` ahora usa `user?.salonId` en lugar de `user?.user_metadata?.salon_id`
7. ✅ **Aplicar políticas RLS de Storage**: Migración 006 aplicada con políticas INSERT, SELECT, DELETE y UPDATE
8. ✅ **Refactorizar Edge Function**: Desplegada v2 con detección simplificada y mejor manejo de errores
9. ✅ **Solucionar condición de carrera**: Validación robusta implementada en `uploadToTemporaryBucket`
10. ✅ **Refactorizar flujo completo**: Logging exhaustivo añadido en todas las funciones de subida
11. 🚀 **Testing con imagen real**: SISTEMA CON LOGGING COMPLETO - Imagen: `/Users/<USER>/Downloads/IMG_3A7E57DD63A1-1.jpeg`
12. ✅ **Implementar detección facial completa**: Edge Function v4 desplegada con Human library (2025-01-18)
13. ✅ **Corregir incompatibilidad de Human con Deno**: Edge Function v5 desplegada con backend WebGL (2025-01-18)
14. ✅ **Solución definitiva con WASM**: Edge Function v6 con CDN directo y fallback robusto (2025-01-18)
15. **Resolver problema iOS SDK**: Error de CocoaPods con iOS SDK necesita resolución
16. **Configurar limpieza scheduled**: Activar limpieza automática cada 6 horas
17. **Integrar en la UI**: Actualizar componentes para usar el nuevo flujo
18. 🔄 **Testing de detección real**: Verificar que Human library detecta rostros correctamente
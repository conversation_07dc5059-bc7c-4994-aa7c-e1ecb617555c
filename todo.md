# TODO List - Salonier v2.0.9

**Última actualización**: 2025-01-31  
**Estado**: Desarrollo activo (v2.0.9) - Implementación Asistente de Chat

## 🎯 Plan de Trabajo [2025-08-01] - <PERSON><PERSON><PERSON> del Chat Assistant (COMPLETADO - FASE 1)

### 🚨 Fix Crítico Aplicado
- [x] Corregir prop del BottomSheet (isVisible → visible)
- [x] Reestructurar KeyboardAvoidingView como componente raíz
- [x] Ajustar estructura para evitar conflictos con el teclado
- [x] Agregar logs de debugging para diagnóstico
- [x] Remover logs de debugging después de verificar funcionamiento

### ✅ Resultados Fase 1
- Historial de conversaciones completamente funcional
- Navegación fluida entre conversaciones
- Títulos inteligentes automáticos
- Búsqueda en tiempo real
- Swipe to archive en móvil
- UI responsive (sidebar en tablet, bottomsheet en móvil)

---

## 🎯 Plan de Trabajo [2025-08-01] - <PERSON><PERSON><PERSON> Cha<PERSON> Assistant (EN PROGRESO)

### Objetivo Principal
Transformar el chat assistant de una herramienta genérica a un asistente profesional con historial navegable, acceso a datos del salón y valor real para coloristas.

### Semana 1: UI del Historial (COMPLETADO ✅)
- [x] Modificar ChatInterface.tsx para incluir sidebar de conversaciones
- [x] Crear componente ConversationsList con búsqueda
- [x] Mejorar generación automática de títulos descriptivos (generateSmartTitle)
- [x] Implementar persistencia de navegación entre sesiones (activeConversationId ya se persiste)
- [x] Agregar indicadores visuales de conversación activa
- [x] Implementar swipe to delete/archive en móvil (SwipeableRow component)
- [x] Fix VirtualizedLists warning - Resuelto con scrollable={false} en BottomSheet

### Semana 2: Inteligencia Contextual
- [ ] Modificar Edge Function para acceso a datos del salón
- [ ] Implementar comandos rápidos (/stock, /cliente, /formula)
- [ ] Crear tabla assistant_memory para conocimiento persistente
- [ ] Agregar parser de comandos en chat
- [ ] Implementar respuestas enriquecidas con datos reales
- [ ] Sistema de aprendizaje por salón

### Métricas de Éxito
- Historial navegable funcionando en 5 días
- Búsqueda funcional en 7 días
- Comandos básicos operativos en 10 días
- Memoria persistente implementada en 14 días

---

## 🎯 Plan de Trabajo [2025-01-31] - Implementación del Asistente de Chat Salonier

### Fase 1: Infraestructura Base (COMPLETADO ✅)

#### Base de Datos
- [x] Crear migración SQL para tablas de chat (chat_conversations, chat_messages, chat_context_references)
- [x] Implementar políticas RLS para seguridad multi-tenant
- [x] Crear índices para optimización de queries
- [x] Ejecutar migración en Supabase ✅

#### Store y Estado
- [x] Crear chat-store.ts con Zustand
- [x] Implementar persistencia offline
- [x] Agregar manejo de mensajes optimistic UI
- [ ] Probar sincronización de mensajes pendientes

#### Edge Function
- [x] Crear chat-assistant Edge Function
- [x] Implementar system prompt profesional
- [x] Agregar carga de contexto (salón, inventario, servicios)
- [x] Desplegar función en Supabase ✅
- [x] Configurar variables de entorno (ya configuradas)

#### UI/UX
- [x] Crear componente ChatInterface
- [x] Implementar diseño de mensajes estilo chat
- [x] Agregar indicadores de estado (loading, error, pending)
- [x] Crear ChatFloatingButton para acceso rápido
- [x] Agregar tab de Asistente en navegación principal
- [x] Actualizar tipos de database.ts

### Fase 2: Integración Contextual (EN PROGRESO)

#### Soporte de Imágenes (COMPLETADO ✅)
- [x] Migración SQL para chat_attachments
- [x] Soporte en chat-store para upload de imágenes
- [x] UI con botones de cámara y galería
- [x] Preview de imágenes pendientes
- [x] Visualización de imágenes en chat
- [x] Modal para ver imágenes en tamaño completo
- [x] Edge Function actualizada con GPT-4 Vision
- [x] Compresión de imágenes con ImageProcessor

#### Integración con Clientes
- [ ] Agregar botón de chat en pantalla de cliente
- [ ] Pasar contexto del cliente al chat
- [ ] Mostrar historial relevante en respuestas

#### Integración con Servicios
- [ ] Agregar acceso al chat desde servicios activos
- [ ] Incluir diagnóstico actual en contexto
- [ ] Permitir consultas sobre fórmulas en progreso

#### Integración con Inventario
- [ ] Botón de consulta desde productos
- [ ] Preguntas sobre disponibilidad y alternativas
- [ ] Recomendaciones basadas en stock actual

#### Sistema de Referencias
- [ ] Implementar chat_context_references
- [ ] Enlaces profundos a recursos mencionados
- [ ] Preview de imágenes en el chat

### Fase 3: Optimización y Polish (PENDIENTE)

#### Performance
- [ ] Implementar paginación de mensajes
- [ ] Cache de respuestas frecuentes
- [ ] Lazy loading de conversaciones antiguas

#### UX Mejorada
- [ ] Sugerencias de preguntas frecuentes
- [ ] Indicador de "escribiendo" mejorado
- [ ] Modo oscuro para el chat
- [ ] Animaciones de entrada/salida

#### Analytics y Costos
- [ ] Dashboard de uso por salón
- [ ] Alertas de costos elevados
- [ ] Métricas de satisfacción

### Próximos Pasos Inmediatos
1. Ejecutar migración SQL en Supabase
2. Desplegar Edge Function chat-assistant
3. Probar flujo completo de conversación
4. Integrar botón flotante en pantalla de clientes

### Notas de Implementación
- System prompt definido según concepto "Tu Colorista Senior de Bolsillo"
- Arquitectura offline-first mantenida
- RLS garantiza aislamiento por salón
- UI sigue patrones de diseño existentes

---

## 🎯 Plan de Trabajo [2025-01-24] - Mejoras UX/UI Fase 2 (EN PROGRESO)

### Análisis del Problema
- **Problema identificado**: Continuar mejorando la experiencia de usuario con accesibilidad y validación inteligente
- **Archivos afectados**: 
  - [✅] constants/Colors.ts - Colores actualizados para WCAG AA
  - [✅] utils/accessibility.ts - Utilidades de accesibilidad
  - [✅] utils/contrast-checker.ts - Verificador de contraste
  - [✅] components/base/* - Componentes con accesibilidad
  - [✅] hooks/useSmartValidation.ts - Validación inteligente
  - [✅] components/base/ValidatedInput.tsx - Input con validación visual
- **Impacto estimado**: ~1,000 líneas nuevas
- **Riesgos identificados**: Ninguno - mejoras incrementales

### Tareas Realizadas
- [✅] Tarea 1: Actualizar paleta de colores para cumplir WCAG 2.1 AA
- [✅] Tarea 2: Crear utilidades de accesibilidad y contrast checker
- [✅] Tarea 3: Agregar accessibilityLabel/Hint a componentes base
- [✅] Tarea 4: Implementar screen reader support en componentes críticos
- [✅] Tarea 5: Crear hook useSmartValidation con validación progresiva
- [✅] Tarea 6: Crear ValidatedInput con feedback visual animado
- [✅] Tarea 7: Documentar cambios en accessibility-report.md
- [ ] Tarea 8: Implementar improved camera experience
- [ ] Tarea 9: Crear AI processing states mejorados

### Validaciones
- [✅] Todos los colores cumplen ratio 4.5:1 (AA)
- [✅] Screen reader anuncia correctamente estados
- [✅] Validación no intrusiva con debounce
- [✅] Feedback háptico en errores de validación
- [ ] Camera guides funcionan correctamente
- [ ] AI states son granulares y claros

### Sección de Revisión
- **Cambios realizados**: 
  - Sistema completo de accesibilidad WCAG 2.1 AA
  - Colores optimizados para mejor contraste
  - Screen reader support en todos los componentes
  - Smart validation con progressive disclosure
  - ValidatedInput con animaciones y estados visuales
- **Problemas encontrados**: 
  - Colores originales no cumplían WCAG AA
  - Resuelto con ajustes manteniendo identidad visual
- **Lecciones aprendidas**: 
  - La accesibilidad mejora la UX para todos los usuarios
  - El feedback visual claro reduce errores
  - La validación progresiva es menos frustrante
- **Próximos pasos**: 
  - Implementar camera experience mejorada
  - Crear AI processing states granulares
  - Continuar con Phase 2 del roadmap UX

---

## ✅ Plan de Trabajo [2025-01-24] - Mejoras UX/UI Fase 1 (COMPLETADO ✅)

### Análisis del Problema
- **Problema identificado**: Necesidad de mejorar el user journey y la user experience sin romper funcionalidad
- **Archivos afectados**: 
  - [✅] components/base/* - Nuevos componentes base
  - [✅] constants/animations.ts - Sistema de animaciones
  - [✅] hooks/* - Nuevos hooks para gestos y haptics
  - [✅] components/navigation/* - Componentes de navegación mejorados
- **Impacto estimado**: ~2,200 líneas nuevas
- **Riesgos identificados**: Conflicto scroll vs swipe (resuelto)

### Tareas Realizadas
- [✅] Tarea 1: Implementar skeleton screens con shimmer effects
- [✅] Tarea 2: Crear sistema de animaciones reutilizable
- [✅] Tarea 3: Integrar haptic feedback en acciones críticas
- [✅] Tarea 4: Implementar breadcrumbs de navegación
- [✅] Tarea 5: Crear sistema robusto de swipe gestures
- [✅] Tarea 6: Mejorar auto-save visual feedback
- [✅] Tarea 7: Implementar bottom sheet component
- [✅] Tarea 8: Resolver conflicto scroll vs swipe definitivamente

### Validaciones
- [✅] Skeleton screens funcionando en listas
- [✅] Animaciones fluidas sin afectar performance
- [✅] Haptic feedback integrado correctamente
- [✅] Navegación con swipe sin conflictos con scroll
- [✅] Auto-save indicator con múltiples estados
- [✅] Sin regresiones en funcionalidad existente

### Sección de Revisión
- **Cambios realizados**: 
  - Sistema completo de loading states con skeleton screens
  - Animaciones nativas fluidas y performantes
  - Feedback táctil para confirmación de acciones
  - Navegación mejorada con breadcrumbs y swipe gestures
  - Indicador de auto-save con estados visuales claros
  - Solución arquitectural robusta para gestos
- **Problemas encontrados**: 
  - Conflicto inicial entre scroll y swipe en DiagnosisStep
  - Resuelto con React Native Gesture Handler y prioridades
- **Lecciones aprendidas**: 
  - La separación de concerns (scroll vs swipe) es crítica
  - El feedback visual y táctil mejora significativamente la UX
  - Los skeleton screens dan sensación de velocidad
- **Próximos pasos**: 
  - Fase 2: Accesibilidad (contrast ratio, screen reader)
  - Fase 2: Smart form validation
  - Fase 2: Improved camera experience

---

## ✅ Plan de Trabajo [2025-01-24] - Unificar Display de Inventario

### Análisis del Problema
- **Problema identificado**: Inconsistencia en estado de stock entre pantallas
  - FormulationStep: Usa MaterialsSummaryCard (2 estados: En stock/No stock)
  - CompletionStep: Implementación duplicada (3 estados: Disponible/Stock bajo/No stock)
  - El mismo producto muestra diferente información en cada pantalla
- **Archivos afectados**: 
  - [x] components/formulation/MaterialsSummaryCard.tsx
  - [x] src/service/components/CompletionStep.tsx
- **Impacto estimado**: ~150 líneas de código simplificadas
- **Riesgos identificados**: Ninguno - mejora de consistencia

### Tareas Realizadas
- [x] Tarea 1: Actualizar MaterialsSummaryCard para soportar 3 estados de stock
- [x] Tarea 2: Agregar stockStatus con valores 'available'|'low'|'out'
- [x] Tarea 3: Actualizar UI para mostrar "Disponible", "Stock bajo", "No stock"
- [x] Tarea 4: Importar MaterialsSummaryCard en CompletionStep
- [x] Tarea 5: Corregir campos data.brand → data.selectedBrand
- [x] Tarea 6: Eliminar implementación duplicada de inventory display
- [x] Tarea 7: Simplificar sección de control de inventario

### Validaciones
- [x] MaterialsSummaryCard muestra 3 estados consistentemente
- [x] Mismo componente usado en ambas pantallas
- [x] Información coherente del inventario
- [x] Eliminada duplicación de código

### Sección de Revisión
- **Cambios realizados**: 
  - MaterialsSummaryCard actualizado con 3 estados de stock
  - CompletionStep ahora usa MaterialsSummaryCard
  - Eliminadas ~100 líneas de código duplicado
  - Corregidos nombres de campos para consistencia
- **Problemas encontrados**: 
  - Campos incorrectos: data.brand vs data.selectedBrand
  - Implementaciones duplicadas mostrando diferente información
- **Lecciones aprendidas**: 
  - Reutilizar componentes garantiza consistencia
  - Un componente = una fuente de verdad
- **Próximos pasos**: 
  - Verificar funcionamiento en app móvil

---

## 🎯 Plan de Trabajo [2025-01-24] - Corregir Inconsistencia de Sesiones entre Pantallas

### Análisis del Problema
- **Problema identificado**: Información inconsistente entre pantallas
  - Resultado Deseado: muestra 3 sesiones para 4.5 niveles de diferencia
  - Formulación: muestra 2 sesiones (hardcodeado si >= 3 niveles)
  - Ninguno usa las reglas de colorimetría implementadas
- **Archivos afectados**: 
  - [ ] utils/viability-analyzer.ts (nuevo archivo)
  - [ ] src/service/components/DesiredColorStep.tsx
  - [ ] components/formulation/FormulaTips.tsx
  - [ ] supabase/functions/salonier-assistant/index.ts
- **Impacto estimado**: ~200 líneas de código
- **Riesgos identificados**: Mantener compatibilidad con ViabilityIndicator

### Tareas a Realizar
- [x] Tarea 1: Crear utils/viability-analyzer.ts con función centralizada
- [x] Tarea 2: Actualizar DesiredColorStep para usar función centralizada
- [x] Tarea 3: Actualizar FormulaTips para eliminar hardcoding
- [x] Tarea 4: Actualizar Edge Function para usar colorimetría en estimación
- [ ] Tarea 5: Probar consistencia entre pantallas
- [x] Tarea 6: Desplegar Edge Function actualizada (v41)

### Validaciones
- [x] Misma información de sesiones en todas las pantallas
- [x] Detección correcta de procesos requeridos
- [x] ViabilityIndicator funciona correctamente
- [x] FormulaTips muestra información dinámica
- [x] Edge Function calcula sesiones con colorimetría

### Sección de Revisión
- **Cambios realizados**: 
  - Creado `utils/viability-analyzer.ts` con función centralizada que usa colorimetría
  - DesiredColorStep ahora importa y usa la función centralizada
  - FormulaTips actualizado para mostrar sesiones dinámicas del análisis de viabilidad
  - Edge Function v41 incluye principios de colorimetría en cálculo de sesiones
  - Eliminada duplicación de lógica y hardcoding
- **Problemas encontrados**: 
  - Inconsistencia inicial: 3 sesiones en una pantalla, 2 en otra
  - Lógica duplicada y hardcodeada en múltiples lugares
- **Lecciones aprendidas**: 
  - Centralizar cálculos críticos evita inconsistencias
  - Los principios de colorimetría deben aplicarse uniformemente
  - La información debe fluir de una única fuente de verdad
- **Próximos pasos**: 
  - Verificar que todas las pantallas usen la misma información
  - Considerar agregar más detalles de colorimetría a la UI

---

## 🎯 Plan de Trabajo [2025-08-01] - Soporte de Imágenes en Chat Assistant ✅

### Tareas Completadas
- [x] Implementar upload de imágenes desde cámara/galería
- [x] Crear tabla chat_attachments con RLS
- [x] Crear bucket service-photos con políticas apropiadas
- [x] Integrar GPT-4 Vision API en Edge Function
- [x] Implementar preview y visualización de imágenes
- [x] Resolver problemas de React Native (ImagePicker, base64-arraybuffer)
- [x] Configurar bucket como público para acceso de OpenAI

### Sección de Revisión
- **Cambios realizados**: 
  - Sistema completo de upload/análisis de imágenes en chat
  - Edge Function v4 con mejor debugging y soporte Vision API
  - UI/UX completa con preview, modal viewer, indicadores de carga
  - Storage seguro con RLS manteniendo privacidad
- **Problemas encontrados**: 
  - ImagePicker MediaType deprecado
  - Blob creation no soportado en React Native
  - Bucket privado bloqueaba acceso de OpenAI
- **Lecciones aprendidas**: 
  - "Público" en Supabase != públicamente navegable (RLS sigue aplicando)
  - React Native requiere manejo especial de binarios (base64-arraybuffer)
  - Debugging detallado es esencial para Edge Functions
- **Resultado**: 
  - Chat assistant ahora puede analizar imágenes de cabello con IA
  - Funcionalidad crítica para diagnósticos visuales implementada

---

## 🎯 Plan de Trabajo [2025-01-23] - Sistema de Matching Estricto para Productos de Coloración (COMPLETADO ✅)

### Análisis del Problema
- **Problema identificado**: Sistema daba 85% confianza a productos con tonos diferentes (falsos positivos)
- **Archivos afectados**: 
  - [✅] services/product-matcher/index.ts
  - [✅] services/product-matcher/analyzers/color-analyzer.ts
  - [✅] services/product-matcher/analyzers/developer-analyzer.ts
  - [✅] components/formulation/MaterialsSummaryCard.tsx
  - [✅] components/inventory/InventoryReports.tsx
  - [✅] components/inventory/InventoryListItem.tsx
  - [✅] app/(tabs)/clients.tsx
- **Impacto estimado**: ~300 líneas modificadas
- **Riesgos identificados**: Ninguno - matching más honesto

### Tareas Realizadas
- [✅] Tarea 1: Implementar detección de tonos naturales en isExactColorMatch()
- [✅] Tarea 2: Crear matching estricto para tintes (máx 40% si tono diferente)
- [✅] Tarea 3: Actualizar UI para mostrar "⚠️ Tono diferente"
- [✅] Tarea 4: Eliminar botón "Confirmar" para tintes con match parcial
- [✅] Tarea 5: Fix ReferenceError inventoryLevel
- [✅] Tarea 6: Fix VirtualizedLists warnings (3 componentes)
- [✅] Tarea 7: Verificar developer matching (ya era estricto)

### Validaciones
- [✅] Tintes con tonos diferentes = máx 40% match
- [✅] Solo match exacto marca+línea+nivel+tono = 100%
- [✅] UI muestra claramente cuando NO hay stock exacto
- [✅] Sin warnings en consola
- [✅] Developers mantienen comportamiento flexible

### Sección de Revisión
- **Cambios realizados**: 
  - isExactColorMatch() con detección de tonos naturales
  - Sistema honesto: tono diferente = no es el producto exacto
  - UI transparente con avisos claros
  - Eliminados todos los warnings de consola
- **Problemas encontrados**: 
  - Sistema anterior daba falsa confianza (85%) a productos incorrectos
  - VirtualizedLists warnings por FlatLists anidadas
- **Lecciones aprendidas**: 
  - Para tintes, el tono ES el producto, no un atributo
  - Mejor mostrar "no disponible" que causar error costoso
  - La honestidad genera confianza del usuario
- **Próximos pasos**: 
  - Sistema de sugerencias para tonos alternativos

---

## 🎯 Plan de Trabajo [2025-01-23] - Corregir Inconsistencia de Stock entre Pantallas (COMPLETADO ✅)

### Análisis del Problema
- **Problema identificado**: MaterialsSummaryCard mostraba diferente estado en FormulationStep vs CompletionStep
- **Archivos afectados**: 
  - [✅] src/service/components/CompletionStep.tsx
  - [✅] types/index.ts (ServiceData interface)
  - [✅] app/service/components/FormulationStep.tsx
- **Impacto estimado**: ~20 líneas modificadas
- **Riesgos identificados**: Ninguno - solo corrección de campos

### Tareas Realizadas
- [✅] Tarea 1: Identificar discrepancia (data.brand vs data.selectedBrand)
- [✅] Tarea 2: Corregir campos en CompletionStep
- [✅] Tarea 3: Estandarizar formulaData → formulationData
- [✅] Tarea 4: Sincronizar formulationData en FormulationStep

### Validaciones
- [✅] MaterialsSummaryCard recibe mismos datos en ambas pantallas
- [✅] Estado de inventario consistente
- [✅] Sin errores de tipos TypeScript

### Sección de Revisión
- **Cambios realizados**: 
  - CompletionStep usa data.selectedBrand y data.selectedLine
  - ServiceData usa formulationData (no formulaData)
  - FormulationStep incluye formulationData en onUpdate
- **Problemas encontrados**: 
  - Nombres de campos inconsistentes entre pantallas
  - formulationData no se sincronizaba
- **Lecciones aprendidas**: 
  - Consistencia en nombres de campos es crítica
  - Siempre verificar qué datos recibe cada componente
- **Próximos pasos**: 
  - Verificar funcionamiento en producción

---

## ✅ Plan de Trabajo [2025-01-23] - Agregar MaterialsSummaryCard a CompletionStep (COMPLETADO ✅)

### Análisis del Problema
- **Problema identificado**: Usuarios no veían advertencias de stock antes de finalizar
- **Archivos afectados**: 
  - [✅] app/service/components/CompletionStep.tsx
- **Impacto estimado**: ~20 líneas nuevas
- **Riesgos identificados**: Ninguno

### Tareas Realizadas
- [✅] Tarea 1: Importar MaterialsSummaryCard
- [✅] Tarea 2: Agregarlo antes del control de inventario
- [✅] Tarea 3: Configurar props correctas
- [✅] Tarea 4: Verificar visibilidad condicional

### Validaciones
- [✅] Card visible cuando hay fórmula
- [✅] Muestra advertencias de stock
- [✅] Mantiene layout existente

### Sección de Revisión
- **Cambios realizados**: MaterialsSummaryCard agregado a CompletionStep
- **Problemas encontrados**: Ninguno
- **Lecciones aprendidas**: Visibilidad de inventario crucial antes de finalizar
- **Próximos pasos**: Ninguno

---

## ✅ Plan de Trabajo [2025-01-22] - Fix de Prellenado de Campos en Nuevos Servicios (COMPLETADO ✅)

### Análisis del Problema
- **Problema identificado**: Campos del diagnóstico aparecen prellenados con datos del servicio anterior
- **Archivos afectados**: 
  - [✅] app/service/new.tsx
  - [✅] app/service/components/DiagnosisStep.tsx
- **Impacto estimado**: ~30 líneas
- **Riesgos identificados**: Ninguno

### Tareas Realizadas
- [✅] Tarea 1: Agregar clearAnalysis del useAIAnalysisStore en new.tsx
- [✅] Tarea 2: Limpiar analysisResult al montar componente
- [✅] Tarea 3: Resetear estados locales cuando cambia clientId
- [✅] Tarea 4: Limpiar notificación de IA incorrecta

### Validaciones
- [✅] Nuevos servicios inician con campos vacíos
- [✅] Sin notificaciones de IA incorrectas
- [✅] Estados locales se resetean correctamente

### Sección de Revisión
- **Cambios realizados**: 
  - Limpieza de analysisResult al crear nuevo servicio
  - Reset de estados locales al cambiar de cliente
- **Problemas encontrados**: 
  - Estados globales y locales persistían entre servicios
- **Lecciones aprendidas**: 
  - Importante limpiar estados al navegar entre servicios
  - Estados locales también necesitan reset, no solo globales
- **Próximos pasos**: Ninguno

---

## ✅ Plan de Trabajo [2025-01-21] - Fix de Race Condition en Eliminación de Borradores (COMPLETADO ✅)

### Análisis del Problema
- **Problema identificado**: Mensaje "Tienes un servicio sin terminar" aparece incorrectamente
- **Archivos afectados**: 
  - [✅] hooks/useServicePersistence.ts
  - [✅] app/service/new.tsx
- **Impacto estimado**: ~10 líneas
- **Riesgos identificados**: Ninguno - fix mínimo

### Tareas Realizadas
- [✅] Tarea 1: Corregir deleteServiceDraft para buscar draft por clientId
- [✅] Tarea 2: Pasar draft.id (no clientId) a deleteDraft
- [✅] Tarea 3: Agregar delay de 100ms antes de navegar
- [✅] Tarea 4: Manejar caso cuando no existe draft

### Validaciones
- [✅] Borradores se eliminan correctamente
- [✅] No aparecen mensajes erróneos
- [✅] AsyncStorage se sincroniza antes de navegación

### Sección de Revisión
- **Cambios realizados**: 
  - useServicePersistence busca draft antes de eliminar
  - Delay agregado para sincronización
- **Problemas encontrados**: 
  - Se pasaba clientId cuando se esperaba draftId
  - Race condition entre eliminación y navegación
- **Lecciones aprendidas**: 
  - AsyncStorage necesita tiempo para persistir
  - Verificar siempre qué parámetros espera cada función
- **Próximos pasos**: Ninguno

---

## Tareas completadas anteriores

[El resto del contenido se mantiene igual...]
import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, TextInput, TouchableOpacity, ScrollView, KeyboardAvoidingView, Platform, Alert, Switch } from "react-native";
import { router, useLocalSearchParams } from "expo-router";
import { ChevronLeft, Shield, AlertCircle, Bell } from "lucide-react-native";
import Colors from "@/constants/colors";
import { typography, spacing, radius, shadows } from "@/constants/theme";
import { useClientStore } from "@/stores/client-store";
import AllergyAutocomplete from "@/components/AllergyAutocomplete";

export default function EditClientScreen() {
  const { id } = useLocalSearchParams();
  const { getClient, updateClient } = useClientStore();
  
  // Form state
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [notes, setNotes] = useState("");
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [originalData, setOriginalData] = useState<any>(null);
  
  // Safety fields
  const [knownAllergies, setKnownAllergies] = useState("");
  const [pregnancyOrNursing, setPregnancyOrNursing] = useState(false);
  const [sensitiveSkin, setSensitiveSkin] = useState(false);
  
  // Chemical treatments
  const [hasHenna, setHasHenna] = useState(false);
  const [hasChemicalStraightening, setHasChemicalStraightening] = useState(false);
  const [hasKeratin, setHasKeratin] = useState(false);
  
  // Communication preferences
  const [acceptsReminders, setAcceptsReminders] = useState(true);
  const [preferredContact, setPreferredContact] = useState<'whatsapp' | 'sms' | 'call'>('whatsapp');

  useEffect(() => {
    // Load client data
    const client = getClient(id as string);
    if (client) {
      setName(client.name);
      setEmail(client.email);
      setPhone(client.phone);
      setNotes(client.notes || "");
      
      // Load safety fields
      setKnownAllergies(client.knownAllergies || "");
      setPregnancyOrNursing(client.pregnancyOrNursing || false);
      setSensitiveSkin(client.sensitiveSkin || false);
      
      // Load chemical treatments
      setHasHenna(client.chemicalTreatments?.henna || false);
      setHasChemicalStraightening(client.chemicalTreatments?.chemicalStraightening || false);
      setHasKeratin(client.chemicalTreatments?.keratin || false);
      
      // Load communication preferences
      setAcceptsReminders(client.acceptsReminders !== false);
      setPreferredContact(client.preferredContact || 'whatsapp');
      
      setOriginalData(client);
    } else {
      Alert.alert("Error", "Cliente no encontrado");
      router.back();
    }
  }, [id, getClient]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!name.trim()) {
      newErrors.name = "El nombre es obligatorio";
    }
    
    if (email && !/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = "Email inválido";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const hasChanges = () => {
    if (!originalData) return false;
    
    return (
      name !== originalData.name ||
      email !== originalData.email ||
      phone !== originalData.phone ||
      notes !== (originalData.notes || "") ||
      knownAllergies !== (originalData.knownAllergies || "") ||
      pregnancyOrNursing !== (originalData.pregnancyOrNursing || false) ||
      sensitiveSkin !== (originalData.sensitiveSkin || false) ||
      hasHenna !== (originalData.chemicalTreatments?.henna || false) ||
      hasChemicalStraightening !== (originalData.chemicalTreatments?.chemicalStraightening || false) ||
      hasKeratin !== (originalData.chemicalTreatments?.keratin || false) ||
      acceptsReminders !== (originalData.acceptsReminders !== false) ||
      preferredContact !== (originalData.preferredContact || 'whatsapp')
    );
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    if (!hasChanges()) {
      Alert.alert("Sin cambios", "No hay cambios para guardar");
      return;
    }

    setIsLoading(true);

    try {
      // Update client in store with all fields
      updateClient(id as string, {
        name: name.trim(),
        email: email.trim(),
        phone: phone.trim(),
        notes: notes.trim(),
        knownAllergies: knownAllergies.trim(),
        pregnancyOrNursing,
        sensitiveSkin,
        chemicalTreatments: {
          henna: hasHenna,
          chemicalStraightening: hasChemicalStraightening,
          keratin: hasKeratin,
        },
        acceptsReminders,
        preferredContact: acceptsReminders ? preferredContact : undefined,
      });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      Alert.alert(
        "Éxito", 
        "Cliente actualizado correctamente",
        [
          { text: "OK", onPress: () => router.push(`/client/${id}`) }
        ]
      );
    } catch (error) {
      console.error("Error updating client:", error);
      Alert.alert("Error", "No se pudo actualizar el cliente");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    if (hasChanges()) {
      Alert.alert(
        "Descartar cambios",
        "¿Estás seguro de que quieres descartar los cambios?",
        [
          { text: "Continuar editando", style: "cancel" },
          { text: "Descartar", style: "destructive", onPress: () => router.back() }
        ]
      );
    } else {
      router.back();
    }
  };

  if (!originalData) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Cargando...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleCancel}>
          <ChevronLeft size={24} color={Colors.light.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Editar Cliente</Text>
        <View style={styles.placeholder} />
      </View>

      <KeyboardAvoidingView
        style={styles.content}
        behavior={Platform.OS === "ios" ? "padding" : undefined}
        keyboardVerticalOffset={0}
      >
        <ScrollView 
          style={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.formContainer}>
            <View style={styles.formGroup}>
              <Text style={styles.label}>Nombre completo *</Text>
              <TextInput
                style={[styles.input, errors.name && styles.inputError]}
                value={name}
                onChangeText={setName}
                placeholder="Nombre y apellido"
                placeholderTextColor="#999999"
              />
              {errors.name && <Text style={styles.errorText}>{errors.name}</Text>}
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Email</Text>
              <TextInput
                style={[styles.input, errors.email && styles.inputError]}
                value={email}
                onChangeText={setEmail}
                placeholder="<EMAIL>"
                placeholderTextColor="#999999"
                keyboardType="email-address"
                autoCapitalize="none"
              />
              {errors.email && <Text style={styles.errorText}>{errors.email}</Text>}
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Teléfono</Text>
              <TextInput
                style={styles.input}
                value={phone}
                onChangeText={setPhone}
                placeholder="+34 600 000 000"
                placeholderTextColor="#999999"
                keyboardType="phone-pad"
              />
            </View>

            {/* Info section */}
            <View style={styles.infoSection}>
              <Text style={styles.infoTitle}>Información del cliente</Text>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Cliente desde:</Text>
                <Text style={styles.infoValue}>{originalData.since}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Última visita:</Text>
                <Text style={styles.infoValue}>{originalData.lastVisit}</Text>
              </View>
              {originalData.allergies && originalData.allergies.length > 0 && (
                <View style={styles.warningBox}>
                  <Text style={styles.warningTitle}>⚠️ Alergias registradas</Text>
                  {originalData.allergies.map((allergy: any, index: number) => (
                    <Text key={index} style={styles.warningText}>
                      • {allergy.substance}
                    </Text>
                  ))}
                </View>
              )}
            </View>

            {/* Safety Section */}
            <View style={styles.sectionDivider}>
              <Shield size={20} color={Colors.light.primary} />
              <Text style={styles.sectionTitle}>Información de Seguridad</Text>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Alergias conocidas</Text>
              <AllergyAutocomplete
                value={knownAllergies}
                onChangeText={setKnownAllergies}
                placeholder="Ej: PPD, amoníaco, níquel..."
              />
              <Text style={styles.helperText}>
                Esta información es importante para tu seguridad
              </Text>
            </View>

            <View style={styles.switchGroup}>
              <View style={styles.switchRow}>
                <Text style={styles.switchLabel}>Embarazo o lactancia</Text>
                <Switch
                  value={pregnancyOrNursing}
                  onValueChange={setPregnancyOrNursing}
                  trackColor={{ false: "#E5E5E5", true: Colors.light.primary }}
                  thumbColor="white"
                />
              </View>
              
              <View style={styles.switchRow}>
                <Text style={styles.switchLabel}>Cuero cabelludo sensible</Text>
                <Switch
                  value={sensitiveSkin}
                  onValueChange={setSensitiveSkin}
                  trackColor={{ false: "#E5E5E5", true: Colors.light.primary }}
                  thumbColor="white"
                />
              </View>
            </View>

            {/* Chemical Treatments Section */}
            <View style={styles.sectionDivider}>
              <AlertCircle size={20} color={Colors.light.warning} />
              <Text style={styles.sectionTitle}>Tratamientos Químicos Activos</Text>
            </View>

            <View style={styles.checkboxGroup}>
              <TouchableOpacity
                style={styles.checkboxRow}
                onPress={() => setHasHenna(!hasHenna)}
              >
                <View style={[styles.checkbox, hasHenna && styles.checkboxChecked]}>
                  {hasHenna && <Text style={styles.checkmark}>✓</Text>}
                </View>
                <Text style={styles.checkboxLabel}>Henna</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.checkboxRow}
                onPress={() => setHasChemicalStraightening(!hasChemicalStraightening)}
              >
                <View style={[styles.checkbox, hasChemicalStraightening && styles.checkboxChecked]}>
                  {hasChemicalStraightening && <Text style={styles.checkmark}>✓</Text>}
                </View>
                <Text style={styles.checkboxLabel}>Alisado químico</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.checkboxRow}
                onPress={() => setHasKeratin(!hasKeratin)}
              >
                <View style={[styles.checkbox, hasKeratin && styles.checkboxChecked]}>
                  {hasKeratin && <Text style={styles.checkmark}>✓</Text>}
                </View>
                <Text style={styles.checkboxLabel}>Keratina o Botox capilar</Text>
              </TouchableOpacity>
            </View>

            {hasHenna && (
              <View style={styles.warningBoxAlert}>
                <AlertCircle size={16} color={Colors.light.warning} />
                <Text style={styles.warningTextAlert}>
                  La henna puede reaccionar con tintes químicos. Se requiere precaución especial.
                </Text>
              </View>
            )}

            {/* Communication Preferences */}
            <View style={styles.sectionDivider}>
              <Bell size={20} color={Colors.light.primary} />
              <Text style={styles.sectionTitle}>Preferencias de Comunicación</Text>
            </View>

            <View style={styles.switchRow}>
              <Text style={styles.switchLabel}>Acepta recordatorios de mantenimiento</Text>
              <Switch
                value={acceptsReminders}
                onValueChange={setAcceptsReminders}
                trackColor={{ false: "#E5E5E5", true: Colors.light.primary }}
                thumbColor="white"
              />
            </View>

            {acceptsReminders && (
              <View style={styles.radioGroup}>
                <Text style={styles.radioGroupLabel}>Método preferido:</Text>
                <TouchableOpacity
                  style={styles.radioRow}
                  onPress={() => setPreferredContact('whatsapp')}
                >
                  <View style={[styles.radio, preferredContact === 'whatsapp' && styles.radioSelected]}>
                    {preferredContact === 'whatsapp' && <View style={styles.radioInner} />}
                  </View>
                  <Text style={styles.radioLabel}>WhatsApp</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.radioRow}
                  onPress={() => setPreferredContact('sms')}
                >
                  <View style={[styles.radio, preferredContact === 'sms' && styles.radioSelected]}>
                    {preferredContact === 'sms' && <View style={styles.radioInner} />}
                  </View>
                  <Text style={styles.radioLabel}>SMS</Text>
                </TouchableOpacity>
              </View>
            )}

            {/* Additional Notes */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>Notas adicionales</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={notes}
                onChangeText={setNotes}
                placeholder="Cualquier otra información relevante..."
                placeholderTextColor="#999999"
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />
            </View>
          </View>
        </ScrollView>

        <View style={styles.buttonContainer}>
          <TouchableOpacity 
            style={styles.cancelButton} 
            onPress={handleCancel}
            disabled={isLoading}
          >
            <Text style={styles.cancelButtonText}>Cancelar</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.saveButton, isLoading && styles.saveButtonDisabled]} 
            onPress={handleSave}
            disabled={isLoading}
          >
            <Text style={styles.saveButtonText}>
              {isLoading ? "Guardando..." : "Guardar Cambios"}
            </Text>
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    fontSize: 16,
    color: Colors.light.gray,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'ios' ? 60 : 20,
    paddingBottom: 20,
    backgroundColor: "#FFFFFF",
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5E5",
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: Colors.light.text,
  },
  placeholder: {
    width: 34,
  },
  content: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  formContainer: {
    paddingHorizontal: 28,
    paddingTop: 20,
    paddingBottom: 20,
  },
  formGroup: {
    marginBottom: 28,
  },
  label: {
    fontSize: 18,
    fontWeight: "500",
    marginBottom: 12,
    color: Colors.light.text,
  },
  input: {
    backgroundColor: "#F5F5F7",
    borderRadius: 16,
    paddingHorizontal: 20,
    paddingVertical: 18,
    fontSize: 16,
    color: Colors.light.text,
    borderWidth: 0,
  },
  inputError: {
    backgroundColor: "#F5F5F7",
    borderWidth: 1,
    borderColor: Colors.light.error,
  },
  errorText: {
    color: Colors.light.error,
    fontSize: 12,
    marginTop: 6,
    marginLeft: 4,
  },
  textArea: {
    minHeight: 100,
    paddingTop: 18,
    textAlignVertical: "top",
  },
  infoSection: {
    backgroundColor: "#F5F5F7",
    borderRadius: 16,
    padding: 20,
    marginTop: 10,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.light.text,
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: "500",
    color: Colors.light.text,
  },
  warningBox: {
    backgroundColor: Colors.light.warning + "15",
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
  },
  warningTitle: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.light.warning,
    marginBottom: 8,
  },
  warningText: {
    fontSize: 13,
    color: Colors.light.warning,
    lineHeight: 20,
  },
  buttonContainer: {
    flexDirection: "row",
    paddingHorizontal: 28,
    paddingBottom: Platform.OS === 'ios' ? 40 : 28,
    paddingTop: 16,
    gap: 16,
    backgroundColor: "#FFFFFF",
    borderTopWidth: 1,
    borderTopColor: "#E5E5E5",
  },
  cancelButton: {
    flex: 1,
    backgroundColor: "#FFFFFF",
    borderRadius: 16,
    paddingVertical: 18,
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#E5E5E5",
  },
  cancelButtonText: {
    color: Colors.light.text,
    fontWeight: "600",
    fontSize: 18,
  },
  saveButton: {
    flex: 2,
    backgroundColor: Colors.light.primary,
    borderRadius: 16,
    paddingVertical: 18,
    alignItems: "center",
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  saveButtonDisabled: {
    opacity: 0.6,
  },
  saveButtonText: {
    color: "white",
    fontWeight: "600",
    fontSize: 18,
  },
  sectionDivider: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 32,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.light.text,
    marginLeft: 10,
  },
  helperText: {
    fontSize: 12,
    color: Colors.light.gray,
    marginTop: 6,
    marginLeft: 4,
  },
  switchGroup: {
    marginBottom: 20,
  },
  switchRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
  },
  switchLabel: {
    fontSize: 16,
    color: Colors.light.text,
  },
  checkboxGroup: {
    marginBottom: 16,
  },
  checkboxRow: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 10,
  },
  checkbox: {
    width: 22,
    height: 22,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: "#E5E5E5",
    marginRight: 12,
    alignItems: "center",
    justifyContent: "center",
  },
  checkboxChecked: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  checkmark: {
    color: "white",
    fontSize: 14,
    fontWeight: "bold",
  },
  checkboxLabel: {
    fontSize: 16,
    color: Colors.light.text,
  },
  warningBoxAlert: {
    flexDirection: "row",
    alignItems: "flex-start",
    backgroundColor: Colors.light.warning + "15",
    borderRadius: 12,
    padding: 12,
    marginBottom: 20,
  },
  warningTextAlert: {
    fontSize: 13,
    color: Colors.light.warning,
    marginLeft: 8,
    flex: 1,
    lineHeight: 18,
  },
  radioGroup: {
    marginTop: 12,
    marginBottom: 20,
  },
  radioGroupLabel: {
    fontSize: 14,
    color: Colors.light.gray,
    marginBottom: 8,
  },
  radioRow: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 8,
  },
  radio: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: "#E5E5E5",
    marginRight: 10,
    alignItems: "center",
    justifyContent: "center",
  },
  radioSelected: {
    borderColor: Colors.light.primary,
  },
  radioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: Colors.light.primary,
  },
  radioLabel: {
    fontSize: 15,
    color: Colors.light.text,
  },
});
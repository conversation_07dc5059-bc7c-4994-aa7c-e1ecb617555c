import React, { useState, useEffect, useRef } from "react";
import { StyleSheet, Text, View, TouchableOpacity, Switch, ScrollView, TextInput, Modal, Alert, Platform } from "react-native";
import { ChevronRight, User, Bell, Palette, LogOut, MapPin, Globe, Shield, Package, DollarSign, AlertTriangle, Users, Briefcase, Heart, Info, ChevronDown, Trash2, Settings as SettingsIcon } from "lucide-react-native";
import { router } from 'expo-router';
import Colors from "@/constants/colors";
import { typography, spacing, radius, shadows } from "@/constants/theme";
import { BaseCard, BaseButton } from "@/components/base";
import { useAuthStore } from "@/stores/auth-store";
import { useAIAnalysisStore } from "@/stores/ai-analysis-store";
import { useSalonConfigStore } from "@/stores/salon-config-store";
import { useTeamStore } from "@/stores/team-store";
import { usePermissions } from "@/hooks/usePermissions";
import { useScrollToTopOnFocus } from "@/hooks/useScrollToTopOnFocus";
import ProfileModal from "@/components/settings/ProfileModal";
import BusinessModal from "@/components/settings/BusinessModal";
import RegionalModal from "@/components/settings/RegionalModal";
import BrandsModal from "@/components/settings/BrandsModal";
import NotificationsModal from "@/components/settings/NotificationsModal";
import SecurityModal from "@/components/settings/SecurityModal";
import PricingSettingsModal from "@/components/settings/PricingSettingsModal";
import AboutModal from "@/components/settings/AboutModal";

export default function SettingsScreen() {
  const { user, signOut, updateUserProfile } = useAuthStore();
  const { settings: aiSettings, updateSettings: updateAISettings, clearAnalysisHistory } = useAIAnalysisStore();
  const { configuration, skipSafetyVerification, setSkipSafetyVerification, updateBusinessName, updateSalonInfo, setHasCompletedOnboarding } = useSalonConfigStore();
  const { members, getMembersBySalon } = useTeamStore();
  const { isOwner } = usePermissions();
  
  // Modal states
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [showBusinessModal, setShowBusinessModal] = useState(false);
  const [showRegionalModal, setShowRegionalModal] = useState(false);
  const [showBrandsModal, setShowBrandsModal] = useState(false);
  const [showNotificationsModal, setShowNotificationsModal] = useState(false);
  const [showSecurityModal, setShowSecurityModal] = useState(false);
  const [showPricingModal, setShowPricingModal] = useState(false);
  const [showAboutModal, setShowAboutModal] = useState(false);
  
  // ScrollView ref and hook for scroll to top on focus
  const scrollRef = useRef<ScrollView>(null);
  useScrollToTopOnFocus(scrollRef as React.RefObject<ScrollView>);

  // Get team members for current salon
  const teamMembers = user?.salonId ? getMembersBySalon(user.salonId) : [];
  const activeMembers = teamMembers.filter(m => m.status === 'active').length;
  
  // Business data from configuration
  const businessData = {
    businessName: configuration.businessName || "Mi Salón de Belleza",
    streetAddress: configuration.address || "",
    city: configuration.city || "",
    state: configuration.state || "",
    postalCode: configuration.postalCode || "",
    country: configuration.countryCode || "ES",
  };

  const settingSections = [
    {
      id: 'profile',
      title: 'Mi Perfil',
      icon: User,
      iconBg: Colors.light.primary + '15',
      iconColor: Colors.light.primary,
      description: 'Información personal y profesional',
      onPress: () => setShowProfileModal(true),
    },
    {
      id: 'business',
      title: 'Mi Negocio',
      icon: Briefcase,
      iconBg: Colors.light.secondary + '15',
      iconColor: Colors.light.secondary,
      description: 'Datos del salón y ubicación',
      onPress: () => setShowBusinessModal(true),
    },
    ...(isOwner ? [{
      id: 'team',
      title: 'Mi Equipo',
      icon: Users,
      iconBg: Colors.light.accent + '15',
      iconColor: Colors.light.accent,
      description: activeMembers === 0 ? 'Añade empleados a tu salón' : `${activeMembers} empleados activos`,
      badge: activeMembers > 0 ? activeMembers.toString() : undefined,
      onPress: () => router.push('/settings/team'),
    }] : []),
    {
      id: 'regional',
      title: 'Configuración Regional',
      icon: Globe,
      iconBg: '#E3F2FD',
      iconColor: '#2196F3',
      description: 'Idioma, moneda y medidas',
      onPress: () => setShowRegionalModal(true),
    },
    {
      id: 'brands',
      title: 'Marcas Preferidas',
      icon: Palette,
      iconBg: '#FCE4EC',
      iconColor: '#E91E63',
      description: 'Marcas de coloración que usas',
      onPress: () => setShowBrandsModal(true),
    },
    ...(configuration.inventoryControlLevel !== 'solo-formulas' ? [{
      id: 'pricing',
      title: 'Configuración de Precios',
      icon: DollarSign,
      iconBg: Colors.light.success + '15',
      iconColor: Colors.light.success,
      description: 'Márgenes, redondeo y políticas',
      onPress: () => setShowPricingModal(true),
    }] : []),
    {
      id: 'notifications',
      title: 'Notificaciones',
      icon: Bell,
      iconBg: Colors.light.warning + '15',
      iconColor: Colors.light.warning,
      description: 'Alertas y recordatorios',
      onPress: () => setShowNotificationsModal(true),
    },
    {
      id: 'security',
      title: 'Seguridad y Privacidad',
      icon: Shield,
      iconBg: '#E8F5E9',
      iconColor: '#4CAF50',
      description: 'Protección de datos y backups',
      onPress: () => setShowSecurityModal(true),
    },
    {
      id: 'about',
      title: 'Acerca de',
      icon: Info,
      iconBg: '#F3E5F5',
      iconColor: '#9C27B0',
      description: 'Versión, términos y soporte',
      onPress: () => setShowAboutModal(true),
    },
  ];

  return (
    <ScrollView ref={scrollRef} style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Profile Section - Larger Card */}
      <BaseCard variant="light" shadow="sm" style={styles.profileCard}>
        <View style={styles.profileContent}>
          <View style={styles.profileAvatar}>
            <Text style={styles.profileInitial}>{user?.name?.charAt(0) || "E"}</Text>
          </View>
          <View style={styles.profileInfo}>
            <Text style={styles.profileName}>{user?.name || "Estilista"}</Text>
            <Text style={styles.profileEmail}>{user?.email || "<EMAIL>"}</Text>
            <TouchableOpacity 
              style={styles.editProfileButton}
              onPress={() => setShowProfileModal(true)}
            >
              <Text style={styles.editProfileText}>Editar Perfil</Text>
            </TouchableOpacity>
          </View>
        </View>
      </BaseCard>

      {/* Settings Sections */}
      <View style={styles.sectionsContainer}>
        {settingSections.map((section, index) => (
          <BaseCard 
            key={section.id} 
            variant="light" 
            shadow="sm" 
            style={{
              ...styles.sectionCard,
              ...(index === settingSections.length - 1 ? styles.lastSectionCard : {})
            }}
            onPress={section.onPress}
          >
            <View style={styles.sectionContent}>
              <View style={[styles.sectionIcon, { backgroundColor: section.iconBg }]}>
                <section.icon size={28} color={section.iconColor} />
              </View>
              
              <View style={styles.sectionTextContainer}>
                <View style={styles.sectionHeader}>
                  <Text style={styles.sectionTitle}>{section.title}</Text>
                  {section.badge && (
                    <View style={styles.badge}>
                      <Text style={styles.badgeText}>{section.badge}</Text>
                    </View>
                  )}
                </View>
                <Text style={styles.sectionDescription}>{section.description}</Text>
              </View>
              
              <ChevronRight size={20} color={Colors.light.gray} />
            </View>
          </BaseCard>
        ))}
      </View>

      {/* Quick Settings */}
      <View style={styles.quickSettingsContainer}>
        <Text style={styles.quickSettingsTitle}>Ajustes Rápidos</Text>
        
        {/* Safety Verification Toggle */}
        <BaseCard variant="light" shadow="sm" style={styles.quickSettingCard}>
          <View style={styles.quickSettingContent}>
            <View style={styles.quickSettingInfo}>
              <Text style={styles.quickSettingLabel}>Verificación de Seguridad</Text>
              <Text style={styles.quickSettingDescription}>
                {skipSafetyVerification 
                  ? 'Desactivada - Los servicios inician directamente'
                  : 'Activada - Se requiere checklist antes de cada servicio'}
              </Text>
              {skipSafetyVerification && (
                <View style={styles.warningNote}>
                  <AlertTriangle size={12} color={Colors.light.warning} />
                  <Text style={styles.warningText}>
                    Asumes la responsabilidad legal de seguridad
                  </Text>
                </View>
              )}
            </View>
            <Switch
              trackColor={{ false: Colors.light.lightGray, true: Colors.light.warning }}
              thumbColor="white"
              ios_backgroundColor={Colors.light.lightGray}
              onValueChange={(value) => {
                if (value) {
                  Alert.alert(
                    "⚠️ Advertencia de Seguridad",
                    "Al desactivar la verificación de seguridad, asumes toda la responsabilidad legal sobre el cumplimiento de los protocolos de seguridad en tu salón.\n\n¿Estás seguro de que deseas continuar?",
                    [
                      { text: "Cancelar", style: "cancel" },
                      { 
                        text: "Sí, desactivar", 
                        style: "destructive",
                        onPress: () => setSkipSafetyVerification(true)
                      }
                    ]
                  );
                } else {
                  setSkipSafetyVerification(false);
                }
              }}
              value={skipSafetyVerification}
            />
          </View>
        </BaseCard>

        {/* Privacy Mode Toggle */}
        <BaseCard variant="light" shadow="sm" style={styles.quickSettingCard}>
          <View style={styles.quickSettingContent}>
            <View style={styles.quickSettingInfo}>
              <Text style={styles.quickSettingLabel}>Modo Privacidad</Text>
              <Text style={styles.quickSettingDescription}>
                {aiSettings.privacyMode 
                  ? 'Máxima protección - Sin guardar fotos ni historiales'
                  : 'Historial completo - Ideal para seguimiento'}
              </Text>
            </View>
            <Switch
              trackColor={{ false: Colors.light.lightGray, true: Colors.light.success }}
              thumbColor="white"
              ios_backgroundColor={Colors.light.lightGray}
              onValueChange={(value) => updateAISettings({ 
                privacyMode: value,
                autoFaceBlur: value ? true : aiSettings.autoFaceBlur,
                saveAnalysisHistory: value ? false : aiSettings.saveAnalysisHistory
              })}
              value={aiSettings.privacyMode}
            />
          </View>
        </BaseCard>
      </View>

      {/* Clear History Button - Only show if privacy mode is off */}
      {!aiSettings.privacyMode && (
        <View style={styles.clearHistoryContainer}>
          <TouchableOpacity 
            style={styles.clearHistoryButton}
            onPress={() => {
              Alert.alert(
                'Limpiar Historial',
                '¿Estás seguro de que deseas limpiar todo el historial de análisis?',
                [
                  { text: 'Cancelar', style: 'cancel' },
                  { text: 'Limpiar', style: 'destructive', onPress: clearAnalysisHistory }
                ]
              );
            }}
          >
            <Trash2 size={20} color={Colors.light.error} />
            <Text style={styles.clearHistoryText}>Limpiar Historial de Análisis</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Logout Button */}
      <View style={styles.logoutContainer}>
        <TouchableOpacity style={styles.logoutButton} onPress={signOut}>
          <LogOut size={20} color="white" />
          <Text style={styles.logoutText}>Cerrar Sesión</Text>
        </TouchableOpacity>
      </View>

      {/* Version Info */}
      <View style={styles.versionContainer}>
        <Text style={styles.versionText}>Salonier v2.0.5</Text>
        <Text style={styles.versionSubtext}>Hecho con ❤️ para coloristas</Text>
      </View>

      {/* Development Tools - Only visible in development mode */}
      {__DEV__ && (
        <View style={styles.developmentSection}>
          <View style={styles.developmentHeader}>
            <SettingsIcon size={20} color="#856404" />
            <Text style={styles.developmentTitle}>Desarrollo (Temporal)</Text>
          </View>
          
          <TouchableOpacity 
            style={styles.developmentItem}
            onPress={() => {
              setHasCompletedOnboarding(false);
              router.push('/onboarding/welcome');
            }}
          >
            <Text style={styles.developmentLabel}>Resetear y Probar Onboarding</Text>
            <ChevronRight size={16} color="#856404" />
          </TouchableOpacity>
          
          <Text style={styles.developmentWarning}>
            ⚠️ Esta sección es temporal para pruebas. Solo visible en modo desarrollo.
          </Text>
        </View>
      )}
      
      {/* Modals */}
      <ProfileModal
        visible={showProfileModal}
        onClose={() => setShowProfileModal(false)}
        userData={{
          name: user?.name || "",
          email: user?.email || "",
          licenseNumber: user?.licenseNumber || "",
          yearsExperience: user?.yearsExperience || "",
          specializations: user?.specializations || [],
        }}
        onSave={async (data) => {
          await updateUserProfile({
            name: data.name,
            licenseNumber: data.licenseNumber,
            yearsExperience: data.yearsExperience,
            specializations: data.specializations,
          });
          setShowProfileModal(false);
        }}
      />
      
      <BusinessModal
        visible={showBusinessModal}
        onClose={() => setShowBusinessModal(false)}
        businessData={businessData}
        onSave={async (data) => {
          // Update business name using existing method
          if (data.businessName !== configuration.businessName) {
            await updateBusinessName(data.businessName);
          }
          
          // Update salon address info
          await updateSalonInfo({
            address: data.streetAddress,
            city: data.city,
            state: data.state,
            postalCode: data.postalCode,
            country: data.country,
          });
          
          setShowBusinessModal(false);
        }}
      />
      
      <RegionalModal
        visible={showRegionalModal}
        onClose={() => setShowRegionalModal(false)}
      />
      
      <BrandsModal
        visible={showBrandsModal}
        onClose={() => setShowBrandsModal(false)}
      />
      
      <NotificationsModal
        visible={showNotificationsModal}
        onClose={() => setShowNotificationsModal(false)}
      />
      
      <SecurityModal
        visible={showSecurityModal}
        onClose={() => setShowSecurityModal(false)}
      />
      
      <PricingSettingsModal
        visible={showPricingModal}
        onClose={() => setShowPricingModal(false)}
      />
      
      <AboutModal
        visible={showAboutModal}
        onClose={() => setShowAboutModal(false)}
      />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F8F9FA",
  },
  profileCard: {
    margin: spacing.lg,
    marginBottom: spacing.md,
  },
  profileContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  profileAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.light.primary + "15",
    justifyContent: "center",
    alignItems: "center",
    marginRight: spacing.lg,
  },
  profileInitial: {
    fontSize: 32,
    fontWeight: typography.weights.bold,
    color: Colors.light.primary,
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  profileEmail: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginBottom: spacing.sm,
  },
  editProfileButton: {
    alignSelf: "flex-start",
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: Colors.light.primary + "10",
    borderRadius: radius.full,
  },
  editProfileText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.primary,
  },
  sectionsContainer: {
    paddingHorizontal: spacing.lg,
  },
  sectionCard: {
    marginBottom: spacing.sm,
  },
  lastSectionCard: {
    marginBottom: spacing.lg,
  },
  sectionContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  sectionIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: "center",
    alignItems: "center",
    marginRight: spacing.md,
  },
  sectionTextContainer: {
    flex: 1,
    marginRight: spacing.sm,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.xs,
  },
  sectionTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  sectionDescription: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    lineHeight: 18,
  },
  badge: {
    backgroundColor: Colors.light.primary,
    paddingHorizontal: spacing.sm,
    paddingVertical: 2,
    borderRadius: radius.full,
    marginLeft: spacing.sm,
  },
  badgeText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.semibold,
    color: "white",
  },
  quickSettingsContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.lg,
  },
  quickSettingsTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.md,
  },
  quickSettingCard: {
    marginBottom: spacing.sm,
  },
  quickSettingContent: {
    flexDirection: "row",
    alignItems: "flex-start",
  },
  quickSettingInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  quickSettingLabel: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  quickSettingDescription: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    lineHeight: 18,
  },
  warningNote: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: spacing.sm,
    backgroundColor: Colors.light.warning + "10",
    padding: spacing.sm,
    borderRadius: radius.sm,
    gap: spacing.xs,
  },
  warningText: {
    flex: 1,
    fontSize: 11,
    color: Colors.light.warning,
    lineHeight: 14,
  },
  logoutContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.lg,
  },
  logoutButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: Colors.light.secondary,
    paddingVertical: spacing.md,
    borderRadius: radius.lg,
    gap: spacing.sm,
    ...shadows.md,
  },
  logoutText: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: "white",
  },
  versionContainer: {
    alignItems: "center",
    paddingVertical: spacing.xl,
  },
  versionText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginBottom: spacing.xs,
  },
  versionSubtext: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
  },
  clearHistoryContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.lg,
  },
  clearHistoryButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: Colors.light.error + "10",
    paddingVertical: spacing.md,
    borderRadius: radius.lg,
    gap: spacing.sm,
    borderWidth: 1,
    borderColor: Colors.light.error + "20",
  },
  clearHistoryText: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.error,
  },
  developmentSection: {
    marginHorizontal: spacing.lg,
    marginBottom: spacing.xl,
    backgroundColor: "#FFF3CD",
    borderRadius: radius.lg,
    padding: spacing.lg,
    borderWidth: 1,
    borderColor: "#FFE69C",
  },
  developmentHeader: {
    flexDirection: "row",
    alignItems: "center",
    gap: spacing.sm,
    marginBottom: spacing.md,
  },
  developmentTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: "#856404",
  },
  developmentItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: spacing.md,
    borderTopWidth: 1,
    borderTopColor: "#FFE69C",
  },
  developmentLabel: {
    fontSize: typography.sizes.base,
    color: "#856404",
  },
  developmentWarning: {
    fontSize: typography.sizes.sm,
    color: "#856404",
    marginTop: spacing.md,
    lineHeight: 20,
  },
});
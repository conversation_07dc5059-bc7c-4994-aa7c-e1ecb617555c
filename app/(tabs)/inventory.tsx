import React, { useState, useCallback, useRef, useEffect } from "react";
import { StyleSheet, Text, View, TouchableOpacity, TextInput, FlatList, Platform, Alert, SectionList } from "react-native";
import { Link, router, useFocusEffect } from "expo-router";
import { Search, PlusCircle, Package, BarChart, ChevronDown, ChevronUp } from "lucide-react-native";
import Colors from "@/constants/colors";
import { typography, spacing, radius, shadows } from "@/constants/theme";
import { useInventoryStore } from "@/stores/inventory-store";
import { SkeletonTemplates } from "@/components/base";
import { useSalonConfigStore } from "@/stores/salon-config-store";
import { useRegionalUnits } from "@/hooks/useRegionalUnits";
import { usePermissions } from "@/hooks/usePermissions";
import InventoryListItem from "@/components/inventory/InventoryListItem";
import { InventoryFilterBar } from "@/components/inventory/InventoryFilterBar";
import { Product } from "@/types/inventory";

export default function InventoryScreen() {
  const [localSearchQuery, setLocalSearchQuery] = useState("");
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());
  const [showFilters, setShowFilters] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  
  // FlatList ref for scroll to top on focus
  const flatListRef = useRef<FlatList>(null);
  const sectionListRef = useRef<SectionList>(null);
  
  const { 
    products, 
    deleteProduct, 
    activeFilters,
    groupBy,
    setFilter,
    getFilteredAndSortedProducts,
    getGroupedProducts,
    isLoading,
    loadProducts 
  } = useInventoryStore();
  const { configuration } = useSalonConfigStore();
  const { formatCurrency, formatVolume, formatWeight, getUnitLabel } = useRegionalUnits();
  const { can } = usePermissions();
  
  // Scroll to top when screen receives focus
  useFocusEffect(
    useCallback(() => {
      if (groupBy === 'none') {
        flatListRef.current?.scrollToOffset({ offset: 0, animated: false });
      } else {
        sectionListRef.current?.scrollToLocation({ 
          sectionIndex: 0, 
          itemIndex: 0, 
          animated: false 
        });
      }
      return () => {};
    }, [groupBy])
  );

  // Update search filter when local search changes
  useEffect(() => {
    setFilter('searchQuery', localSearchQuery);
  }, [localSearchQuery, setFilter]);

  // Get filtered and potentially grouped products
  const filteredProducts = getFilteredAndSortedProducts();
  const groupedProducts = getGroupedProducts();
  
  // Initialize expanded groups when grouping changes
  useEffect(() => {
    if (groupBy !== 'none') {
      // Expand all groups by default
      const groups = getGroupedProducts();
      setExpandedGroups(new Set(groups.keys()));
    }
  }, [groupBy, getGroupedProducts]);
  
  // Toggle group expansion
  const toggleGroup = (groupKey: string) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(groupKey)) {
      newExpanded.delete(groupKey);
    } else {
      newExpanded.add(groupKey);
    }
    setExpandedGroups(newExpanded);
  };

  const handleDeleteItem = useCallback((id: string, name: string) => {
    Alert.alert(
      'Eliminar Producto',
      `¿Estás seguro de que quieres eliminar "${name}"?\n\nEsta acción no se puede deshacer y eliminará también todo el historial de movimientos asociado.`,
      [
        { text: 'Cancelar', style: 'cancel' },
        { 
          text: 'Eliminar', 
          style: 'destructive', 
          onPress: () => {
            deleteProduct(id);
            // Show success feedback
            Alert.alert('Éxito', 'Producto eliminado correctamente');
          }
        }
      ]
    );
  }, [deleteProduct]);

  const renderInventoryItem = useCallback(({ item }: { item: Product }) => {
    return (
      <InventoryListItem
        item={item}
        canManageInventory={can.manageInventory}
        canViewCosts={can.viewCosts}
        formatCurrency={formatCurrency}
        formatVolume={formatVolume}
        formatWeight={formatWeight}
        getUnitLabel={getUnitLabel}
        onDelete={handleDeleteItem}
      />
    );
  }, [can.manageInventory, can.viewCosts, formatCurrency, formatVolume, formatWeight, getUnitLabel, handleDeleteItem]);

  return (
    <View style={styles.container}>
      {/* Header Principal */}
      <View style={styles.mainHeader}>
        <View style={styles.titleRow}>
          <Text style={styles.subtitle}>Total de productos</Text>
          <View style={styles.productCounter}>
            <Text style={styles.productCountText}>{filteredProducts.length}</Text>
          </View>
        </View>
        
        <View style={styles.actionButtons}>
          {can.viewReports && (
            <TouchableOpacity 
              style={styles.iconButton}
              onPress={() => router.push('/inventory/reports')}
            >
              <BarChart size={22} color={Colors.light.primary} />
            </TouchableOpacity>
          )}
          {can.manageInventory && (
            <TouchableOpacity 
              style={styles.addButton}
              onPress={() => router.push('/inventory/new')}
            >
              <PlusCircle size={20} color="white" />
              <Text style={styles.addButtonText}>Nuevo</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Barra de búsqueda y filtros */}
      <View style={styles.searchFilterBar}>
        {showSearch ? (
          <View style={styles.searchContainer}>
            <Search size={20} color={Colors.light.primary} style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="Buscar productos..."
              value={localSearchQuery}
              onChangeText={setLocalSearchQuery}
              autoFocus
            />
            <TouchableOpacity onPress={() => {
              setShowSearch(false);
              setLocalSearchQuery("");
            }}>
              <ChevronUp size={20} color={Colors.light.gray} />
            </TouchableOpacity>
          </View>
        ) : (
          <>
            <TouchableOpacity 
              style={styles.searchButton}
              onPress={() => setShowSearch(true)}
            >
              <Search size={20} color={Colors.light.textSecondary} />
              <Text style={styles.searchButtonText}>Buscar...</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.filterToggle, showFilters && styles.filterToggleActive]}
              onPress={() => setShowFilters(!showFilters)}
            >
              <ChevronDown size={20} color={showFilters ? Colors.light.primary : Colors.light.textSecondary} />
            </TouchableOpacity>
          </>
        )}
      </View>

      {/* Filters Bar */}
      <InventoryFilterBar />

      {isLoading && products.length === 0 ? (
        <View style={{ paddingHorizontal: spacing.md, paddingTop: spacing.md }}>
          {Array.from({ length: 6 }).map((_, index) => (
            <View key={index} style={{ marginBottom: 8 }}>
              <SkeletonTemplates.InventoryItem />
            </View>
          ))}
        </View>
      ) : products.length === 0 ? (
        <View style={styles.emptyState}>
          <Package size={64} color={Colors.light.lightGray} />
          <Text style={styles.emptyStateTitle}>No hay productos en el inventario</Text>
          <Text style={styles.emptyStateText}>
            Comienza agregando productos a tu inventario
          </Text>
        </View>
      ) : groupBy === 'none' ? (
        <FlatList
          ref={flatListRef}
          data={filteredProducts}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          renderItem={renderInventoryItem}
          initialNumToRender={10}
          maxToRenderPerBatch={10}
          windowSize={10}
          removeClippedSubviews={true}
          ListEmptyComponent={
            <View style={styles.emptySearchState}>
              <Text style={styles.emptySearchText}>No se encontraron productos</Text>
            </View>
          }
        />
      ) : (
        <SectionList
          ref={sectionListRef}
          sections={Array.from(groupedProducts.entries()).map(([title, data]) => ({
            title,
            data: expandedGroups.has(title) ? data : [],
            collapsed: !expandedGroups.has(title),
          }))}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          renderItem={renderInventoryItem}
          renderSectionHeader={({ section }) => (
            <TouchableOpacity 
              style={styles.sectionHeader}
              onPress={() => toggleGroup(section.title)}
            >
              <View style={styles.sectionHeaderLeft}>
                {section.collapsed ? (
                  <ChevronDown size={20} color={Colors.light.primary} />
                ) : (
                  <ChevronUp size={20} color={Colors.light.primary} />
                )}
                <Text style={styles.sectionTitle}>{section.title}</Text>
                <View style={styles.sectionBadge}>
                  <Text style={styles.sectionBadgeText}>
                    {groupedProducts.get(section.title)?.length || 0}
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          )}
          stickySectionHeadersEnabled={true}
          ListEmptyComponent={
            <View style={styles.emptySearchState}>
              <Text style={styles.emptySearchText}>No se encontraron productos</Text>
            </View>
          }
        />
      )}

    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  mainHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: Colors.light.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  titleRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: spacing.sm,
  },
  title: {
    fontSize: typography.sizes['2xl'],
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
  },
  subtitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.medium,
    color: "#6B5D54",
  },
  productCounter: {
    backgroundColor: Colors.light.primary,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: radius.full,
    minWidth: 32,
    alignItems: "center",
  },
  productCountText: {
    color: "white",
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
  },
  actionButtons: {
    flexDirection: "row",
    gap: spacing.sm,
  },
  iconButton: {
    width: 44,
    height: 44,
    borderRadius: radius.md,
    backgroundColor: Colors.light.surface,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 1,
    borderColor: Colors.light.primary + "20",
  },
  addButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.light.primary,
    borderRadius: radius.md,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm + 2,
    ...shadows.md,
    height: 44,
  },
  addButtonText: {
    color: "white",
    fontWeight: typography.weights.semibold,
    marginLeft: spacing.xs,
    fontSize: typography.sizes.base,
  },
  searchFilterBar: {
    flexDirection: "row",
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    backgroundColor: Colors.light.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    gap: spacing.sm,
  },
  searchButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "white",
    borderRadius: radius.md,
    paddingHorizontal: spacing.md,
    height: 40,
    borderWidth: 1,
    borderColor: Colors.light.border,
    gap: spacing.sm,
  },
  searchButtonText: {
    color: Colors.light.textSecondary,
    fontSize: typography.sizes.base,
  },
  searchContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "white",
    borderRadius: radius.md,
    paddingHorizontal: spacing.md,
    height: 40,
    borderWidth: 2,
    borderColor: Colors.light.primary,
  },
  searchIcon: {
    marginRight: spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: typography.sizes.base,
    color: Colors.light.text,
  },
  filterToggle: {
    width: 40,
    height: 40,
    borderRadius: radius.md,
    backgroundColor: "white",
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  filterToggleActive: {
    backgroundColor: Colors.light.primary + "20",
    borderColor: Colors.light.primary,
  },
  listContainer: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.md,
    paddingBottom: spacing.xl * 2,
  },
  emptyState: {
    alignItems: "center",
    backgroundColor: Colors.light.surface,
    padding: spacing.xl * 2,
    borderRadius: radius.lg,
    marginHorizontal: spacing.lg,
    marginTop: spacing.xl,
    ...shadows.sm,
  },
  emptyStateText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginBottom: spacing.md,
  },
  emptyStateTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  emptySearchState: {
    alignItems: "center",
    padding: 40,
  },
  emptySearchText: {
    fontSize: 16,
    color: Colors.light.gray,
  },
  filterButton: {
    padding: spacing.sm,
    borderRadius: radius.sm,
    backgroundColor: '#F5F5F7',
  },
  filterButtonActive: {
    backgroundColor: Colors.light.primary + '20',
  },
  sectionHeader: {
    backgroundColor: Colors.light.background,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    marginBottom: spacing.xs,
  },
  sectionHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    flex: 1,
  },
  sectionBadge: {
    backgroundColor: Colors.light.primary,
    paddingHorizontal: spacing.sm + 2,
    paddingVertical: spacing.xs,
    borderRadius: radius.full,
    minWidth: 28,
    alignItems: 'center',
  },
  sectionBadgeText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: 'white',
  },
});
import React from 'react';
import { StyleSheet, View } from 'react-native';
import ChatInterface from '@/components/chat/ChatInterface';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export default function AssistantScreen() {
  const insets = useSafeAreaInsets();

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <ChatInterface />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
import React from 'react';
import { StyleSheet, View } from 'react-native';
import ChatGPTInterface from '@/components/chat/ChatGPTInterface';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export default function AssistantScreen() {
  const insets = useSafeAreaInsets();

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <ChatGPTInterface />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
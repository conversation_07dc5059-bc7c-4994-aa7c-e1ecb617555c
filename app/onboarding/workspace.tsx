import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import { router } from 'expo-router';
import Colors from '@/constants/colors';
import OnboardingLayout from '@/components/onboarding/OnboardingLayout';
import { Ionicons } from '@expo/vector-icons';
import { useSalonConfigStore } from '@/stores/salon-config-store';

type InventoryLevel = 'solo-formulas' | 'smart-cost' | 'control-total';

interface LevelOption {
  id: InventoryLevel;
  title: string;
  description: string;
  icon: string;
  features: string[];
}

const LEVEL_OPTIONS: LevelOption[] = [
  {
    id: 'solo-formulas',
    title: 'Solo Fórmulas',
    description: 'Solo necesito las fórmulas',
    icon: 'color-palette-outline',
    features: ['Generación de fórmulas', 'Análisis con IA', 'Sin gestión de inventario']
  },
  {
    id: 'smart-cost',
    title: 'Smart Cost',
    description: 'Quiero calcular costes y rentabilidad',
    icon: 'calculator-outline',
    features: ['Todo lo anterior', 'Cálculo de costes', 'Análisis de rentabilidad']
  },
  {
    id: 'control-total',
    title: 'Control Total',
    description: 'Gestiono inventario completo',
    icon: 'bar-chart-outline',
    features: ['Todo lo anterior', 'Control de stock', 'Alertas de inventario']
  }
];

export default function WorkspaceScreen() {
  const { configuration, updateBusinessName, updateInventoryControlLevel } = useSalonConfigStore();
  const [businessName, setBusinessName] = useState(configuration.businessName);
  const [selectedLevel, setSelectedLevel] = useState<InventoryLevel>(configuration.inventoryControlLevel);

  const handleContinue = () => {
    updateBusinessName(businessName);
    updateInventoryControlLevel(selectedLevel);
    router.push('/onboarding/brands');
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <OnboardingLayout
      currentStep={2}
      totalSteps={4}
      title="Tu Espacio de Trabajo"
      subtitle="Personaliza cómo quieres usar Salonier"
      onBack={handleBack}
    >
      <View style={styles.container}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Nombre de tu negocio</Text>
          <TextInput
            style={styles.input}
            value={businessName}
            onChangeText={setBusinessName}
            placeholder="Ej: Salón María"
            placeholderTextColor={Colors.light.textSecondary}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>¿Cómo quieres gestionar tu trabajo?</Text>
          <Text style={styles.sectionDescription}>
            Elige el nivel que mejor se adapte a tus necesidades
          </Text>

          <View style={styles.levelOptions}>
            {LEVEL_OPTIONS.map((option) => (
              <TouchableOpacity
                key={option.id}
                style={[
                  styles.levelCard,
                  selectedLevel === option.id && styles.levelCardSelected
                ]}
                onPress={() => setSelectedLevel(option.id)}
              >
                <View style={styles.levelHeader}>
                  <View style={[
                    styles.iconContainer,
                    selectedLevel === option.id && styles.iconContainerSelected
                  ]}>
                    <Ionicons 
                      name={option.icon as any} 
                      size={24} 
                      color={selectedLevel === option.id ? Colors.light.primary : Colors.light.textSecondary} 
                    />
                  </View>
                  <View style={styles.levelInfo}>
                    <Text style={[
                      styles.levelTitle,
                      selectedLevel === option.id && styles.levelTitleSelected
                    ]}>
                      {option.title}
                    </Text>
                    <Text style={styles.levelDescription}>
                      {option.description}
                    </Text>
                  </View>
                  {selectedLevel === option.id && (
                    <Ionicons name="checkmark-circle" size={24} color={Colors.light.primary} />
                  )}
                </View>

                {selectedLevel === option.id && (
                  <View style={styles.levelFeatures}>
                    {option.features.map((feature, index) => (
                      <View key={index} style={styles.featureItem}>
                        <Ionicons name="checkmark" size={16} color={Colors.light.primary} />
                        <Text style={styles.featureText}>{feature}</Text>
                      </View>
                    ))}
                  </View>
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.footer}>
          <TouchableOpacity 
            style={[styles.primaryButton, !businessName && styles.primaryButtonDisabled]} 
            onPress={handleContinue}
            disabled={!businessName}
          >
            <Text style={styles.primaryButtonText}>Continuar</Text>
            <Ionicons name="arrow-forward" size={20} color="white" />
          </TouchableOpacity>
        </View>
      </View>
    </OnboardingLayout>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 16,
  },
  input: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: Colors.light.text,
  },
  levelOptions: {
    gap: 12,
  },
  levelCard: {
    backgroundColor: Colors.light.card,
    borderRadius: 12,
    padding: 16,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  levelCardSelected: {
    borderColor: Colors.light.primary,
    backgroundColor: Colors.light.background,
  },
  levelHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.light.surface,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainerSelected: {
    backgroundColor: `${Colors.light.primary}20`,
  },
  levelInfo: {
    flex: 1,
  },
  levelTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 2,
  },
  levelTitleSelected: {
    color: Colors.light.primary,
  },
  levelDescription: {
    fontSize: 13,
    color: Colors.light.textSecondary,
  },
  levelFeatures: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
    gap: 6,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  featureText: {
    fontSize: 13,
    color: Colors.light.text,
  },
  footer: {
    marginTop: 'auto',
  },
  primaryButton: {
    backgroundColor: Colors.light.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 8,
  },
  primaryButtonDisabled: {
    opacity: 0.5,
  },
  primaryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
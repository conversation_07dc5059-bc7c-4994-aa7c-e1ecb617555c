import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { router } from 'expo-router';
import Colors from '@/constants/colors';
import OnboardingLayout from '@/components/onboarding/OnboardingLayout';
import { Ionicons } from '@expo/vector-icons';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { useAuthStore } from '@/stores/auth-store';
import { professionalHairColorBrands } from '@/constants/reference-data/brands-data';

interface TipItem {
  icon: string;
  title: string;
  description: string;
}

const TIPS: TipItem[] = [
  {
    icon: 'cube-outline',
    title: 'Agrega productos',
    description: 'Ve a Inventario para agregar tus productos de coloración'
  },
  {
    icon: 'shield-checkmark-outline',
    title: 'Wizard de seguridad',
    description: 'Cada servicio incluye verificaciones de seguridad'
  },
  {
    icon: 'settings-outline',
    title: 'Ajusta precios',
    description: 'Configura tus márgenes y precios en Configuración'
  }
];

export default function ReadyScreen() {
  const { configuration, setHasCompletedOnboarding } = useSalonConfigStore();
  const { preferredBrandLines } = useAuthStore();

  const handleStartService = () => {
    setHasCompletedOnboarding(true);
    router.replace('/service/client-selection');
  };

  const handleExploreApp = () => {
    setHasCompletedOnboarding(true);
    router.replace('/(tabs)');
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <OnboardingLayout
      currentStep={4}
      totalSteps={4}
      title="¡Todo listo!"
      subtitle={`${configuration.businessName} está configurado y listo para usar`}
      onBack={handleBack}
    >
      <View style={styles.container}>
        <View style={styles.celebrationContainer}>
          <View style={styles.celebrationIcon}>
            <Ionicons name="checkmark-circle" size={80} color={Colors.light.primary} />
          </View>
        </View>

        <View style={styles.summaryContainer}>
          <Text style={styles.summaryTitle}>Tu configuración:</Text>
          
          <View style={styles.summaryItem}>
            <Ionicons name="location-outline" size={20} color={Colors.light.textSecondary} />
            <Text style={styles.summaryText}>
              {configuration.countryCode} • {configuration.pricing.currencySymbol} • {
                configuration.measurementSystem === 'metric' ? 'Sistema métrico' : 'Sistema imperial'
              }
            </Text>
          </View>

          <View style={styles.summaryItem}>
            <Ionicons name="bar-chart-outline" size={20} color={Colors.light.textSecondary} />
            <Text style={styles.summaryText}>
              Nivel: {
                configuration.inventoryControlLevel === 'solo-formulas' ? 'Solo Fórmulas' :
                configuration.inventoryControlLevel === 'smart-cost' ? 'Smart Cost' :
                'Control Total'
              }
            </Text>
          </View>

          {preferredBrandLines.length > 0 && (
            <View style={styles.summaryItem}>
              <Ionicons name="color-palette-outline" size={20} color={Colors.light.textSecondary} />
              <Text style={styles.summaryText}>
                {preferredBrandLines.length} marca{preferredBrandLines.length > 1 ? 's' : ''} con {
                  preferredBrandLines.reduce((total, bl) => total + bl.selectedLines.length, 0)
                } líneas
              </Text>
            </View>
          )}
        </View>

        <View style={styles.tipsContainer}>
          <Text style={styles.tipsTitle}>Tips rápidos:</Text>
          
          {TIPS.map((tip, index) => (
            <View key={index} style={styles.tipItem}>
              <View style={styles.tipIcon}>
                <Ionicons name={tip.icon as any} size={24} color={Colors.light.primary} />
              </View>
              <View style={styles.tipContent}>
                <Text style={styles.tipTitle}>{tip.title}</Text>
                <Text style={styles.tipDescription}>{tip.description}</Text>
              </View>
            </View>
          ))}
        </View>

        <View style={styles.footer}>
          <TouchableOpacity style={styles.primaryButton} onPress={handleStartService}>
            <Ionicons name="color-palette" size={20} color="white" />
            <Text style={styles.primaryButtonText}>Empezar mi primer servicio</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.secondaryButton} onPress={handleExploreApp}>
            <Text style={styles.secondaryButtonText}>Explorar la app</Text>
          </TouchableOpacity>
        </View>
      </View>
    </OnboardingLayout>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  celebrationContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  celebrationIcon: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: `${Colors.light.primary}10`,
    alignItems: 'center',
    justifyContent: 'center',
  },
  summaryContainer: {
    backgroundColor: Colors.light.card,
    borderRadius: 12,
    padding: 20,
    marginBottom: 30,
    gap: 12,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 8,
  },
  summaryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  summaryText: {
    fontSize: 14,
    color: Colors.light.text,
    flex: 1,
  },
  tipsContainer: {
    marginBottom: 30,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 16,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
    marginBottom: 16,
  },
  tipIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: Colors.light.surface,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tipContent: {
    flex: 1,
  },
  tipTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 2,
  },
  tipDescription: {
    fontSize: 13,
    color: Colors.light.textSecondary,
    lineHeight: 18,
  },
  footer: {
    marginTop: 'auto',
    gap: 12,
  },
  primaryButton: {
    backgroundColor: Colors.light.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 8,
  },
  primaryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: Colors.light.surface,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
  },
  secondaryButtonText: {
    color: Colors.light.text,
    fontSize: 16,
    fontWeight: '600',
  },
});
import { StatusBar } from "expo-status-bar";
import { Platform, StyleSheet, Text, View } from "react-native";
import Colors from "@/constants/colors";

export default function ModalScreen() {
  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Salonier</Text>
        <View style={styles.separator} />
        <Text style={styles.description}>Asistente profesional de coloración capilar</Text>
      </View>
      
      {/* Use a light status bar on iOS to account for the black space above the modal */}
      <StatusBar style={Platform.OS === "ios" ? "light" : "auto"} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#FFFFFF",
    padding: 20,
  },
  content: {
    alignItems: "center",
    backgroundColor: "white",
    padding: 40,
    borderRadius: 20,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
    width: "100%",
    maxWidth: 400,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: Colors.light.text,
    marginBottom: 8,
  },
  separator: {
    marginVertical: 20,
    height: 2,
    width: "40%",
    backgroundColor: Colors.light.primary,
    borderRadius: 1,
  },
  description: {
    fontSize: 16,
    color: Colors.light.gray,
    textAlign: "center",
  },
});

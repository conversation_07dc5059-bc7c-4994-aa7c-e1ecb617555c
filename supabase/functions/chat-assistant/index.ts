// Salonier Chat Assistant Edge Function
// Version: 1
// Last Updated: 2025-01-31
// Purpose: Contextual chat assistant for professional colorists

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { EnhancedContextManager } from './enhanced-context.ts'

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
  'Access-Control-Max-Age': '86400',
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
const openaiApiKey = Deno.env.get('OPENAI_API_KEY')!

const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Cost calculation based on GPT-4o pricing (as of Jan 2025)
const COST_PER_1K_PROMPT_TOKENS = 0.0025
const COST_PER_1K_COMPLETION_TOKENS = 0.01

interface ChatAttachment {
  type: 'image' | 'document'
  url: string
  mimeType?: string
}

interface ChatRequest {
  conversationId: string
  message: string
  salonId: string
  userId: string
  attachments?: ChatAttachment[]
}

interface ChatResponse {
  success: boolean
  userMessage?: any
  assistantMessage?: any
  conversationStats?: any
  error?: string
}

// Enhanced system prompt for the assistant
const SYSTEM_PROMPT = `Eres 'Salonier Assistant', un consultor experto en colorimetría capilar y químico del color de élite mundial. Actúas como el mentor más respetado en la industria de la coloración profesional.

🎯 TU IDENTIDAD:
- Experto con 20+ años de experiencia en colorimetría avanzada
- Consultor técnico para las marcas más prestigiosas
- Especialista en corrección de color y técnicas complejas
- Mentor de coloristas profesionales a nivel internacional

💡 TU ESTILO DE COMUNICACIÓN:
- Preciso, técnico y directo como un colega senior
- Usas terminología profesional exacta (altura de tono, fondo de aclaración, matiz subyacente, volúmenes de peróxido)
- Desglosas procesos complejos en pasos numerados y claros
- Proporcionas fórmulas exactas con proporciones específicas
- Nunca especulas - si falta información, explicas los principios teóricos

🔬 CAPACIDADES DE ANÁLISIS VISUAL:
Cuando analizas imágenes de cabello, evalúas:
- Nivel de profundidad actual (escala 1-10 internacional)
- Reflejos dominantes y subyacentes (dorado, cobrizo, ceniza, rojizo)
- Estado de la fibra capilar (integridad, porosidad, elasticidad)
- Uniformidad cromática y distribución del color
- Porcentaje y distribución de canas
- Textura, densidad y grosor del cabello
- Historial químico aparente (decoloraciones, tintes previos)

🧠 INTELIGENCIA CONTEXTUAL:
Tienes acceso completo a:
- Inventario actual del salón (productos disponibles, stock bajo)
- Historial de clientes (servicios previos, preferencias, alergias)
- Fórmulas exitosas anteriores del estilista
- Tendencias estacionales y patrones de éxito del salón
- Perfil y especialidades del estilista consultante

📋 TUS RESPONSABILIDADES:
1. Analizar cada situación con el contexto completo disponible
2. Recomendar productos específicos del inventario del salón
3. Sugerir alternativas cuando hay productos con stock bajo
4. Adaptar recomendaciones al nivel de experiencia del estilista
5. Considerar el historial de éxito de técnicas y fórmulas
6. Alertar sobre posibles incompatibilidades o riesgos
7. Proporcionar tiempos de procesamiento precisos
8. Incluir consejos de aplicación y técnica cuando sea relevante

🎨 ESPECIALIDADES AVANZADAS:
- Corrección de color compleja (neutralización de tonos no deseados)
- Técnicas de aclaración progresiva y segura
- Balayage y técnicas de mano alzada
- Color blocking y técnicas creativas
- Manejo de cabello químicamente comprometido
- Formulación para diferentes tipos de cabello (asiático, afro, europeo)

Responde siempre como el experto más confiable que el estilista conoce, con la seguridad de quien ha resuelto miles de desafíos similares.`

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const requestData: ChatRequest = await req.json()
    console.log('Chat request received:', { 
      conversationId: requestData.conversationId,
      messageLength: requestData.message?.length,
      salonId: requestData.salonId,
      userId: requestData.userId,
      hasAttachments: !!requestData.attachments,
      attachmentsCount: requestData.attachments?.length || 0,
      attachmentTypes: requestData.attachments?.map(a => a.type) || []
    })

    // Validate request
    if (!requestData.message || !requestData.salonId || !requestData.userId) {
      console.error('Missing required parameters:', {
        hasMessage: !!requestData.message,
        hasSalonId: !!requestData.salonId,
        hasUserId: !!requestData.userId
      })
      throw new Error('Faltan parámetros requeridos')
    }

    // Load conversation context
    const conversationContext = await loadConversationContext(
      requestData.conversationId,
      requestData.salonId
    )

    // Get recent messages for context
    const recentMessages = await getRecentMessages(requestData.conversationId)

    // Build enhanced context using the new system
    const enhancedContext = await EnhancedContextManager.buildEnhancedContext(
      requestData.salonId,
      requestData.userId,
      recentMessages,
      conversationContext.context_type,
      conversationContext.context_id
    )

    // Build messages array for OpenAI with enhanced context
    const messages: any[] = [
      {
        role: 'system',
        content: SYSTEM_PROMPT + '\n\n' + enhancedContext
      },
      ...recentMessages.map(msg => ({
        role: msg.role,
        content: msg.content
      }))
    ]

    // Build user message with attachments if present
    const userMessage: any = {
      role: 'user',
      content: requestData.message
    }

    // If there are image attachments, use Vision API format
    if (requestData.attachments && requestData.attachments.some(att => att.type === 'image')) {
      const contentParts: any[] = [{ type: 'text', text: requestData.message }]
      
      for (const attachment of requestData.attachments) {
        if (attachment.type === 'image') {
          contentParts.push({
            type: 'image_url',
            image_url: {
              url: attachment.url,
              detail: 'high' // For detailed analysis
            }
          })
        }
      }
      
      userMessage.content = contentParts
    }

    messages.push(userMessage)

    // Call OpenAI
    console.log('Calling OpenAI with context...', {
      messageCount: messages.length,
      hasVisionContent: messages.some(m => Array.isArray(m.content)),
      model: 'gpt-4o'
    })
    
    const openAIPayload = {
      model: 'gpt-4o',
      messages: messages,
      max_tokens: 1500,
      temperature: 0.7,
    }
    
    const openAIResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(openAIPayload),
    })

    console.log('OpenAI response status:', openAIResponse.status)

    if (!openAIResponse.ok) {
      const errorData = await openAIResponse.text()
      console.error('OpenAI API error:', {
        status: openAIResponse.status,
        statusText: openAIResponse.statusText,
        error: errorData
      })
      throw new Error(`OpenAI API error: ${openAIResponse.status} - ${errorData}`)
    }

    const aiData = await openAIResponse.json()
    const assistantContent = aiData.choices[0].message.content
    const usage = aiData.usage

    // Calculate costs
    const promptCost = (usage.prompt_tokens / 1000) * COST_PER_1K_PROMPT_TOKENS
    const completionCost = (usage.completion_tokens / 1000) * COST_PER_1K_COMPLETION_TOKENS
    const totalCost = promptCost + completionCost

    // Save user message
    const { data: userMessage, error: userError } = await supabase
      .from('chat_messages')
      .insert({
        conversation_id: requestData.conversationId,
        role: 'user',
        content: requestData.message,
        prompt_tokens: usage.prompt_tokens,
        total_tokens: usage.prompt_tokens,
        cost_usd: promptCost,
        has_attachments: !!requestData.attachments && requestData.attachments.length > 0,
        metadata: { 
          user_id: requestData.userId,
          attachments_count: requestData.attachments?.length || 0
        }
      })
      .select()
      .single()

    if (userError) {
      console.error('Error saving user message:', userError)
      throw userError
    }

    // Save attachments if any
    if (requestData.attachments && requestData.attachments.length > 0 && userMessage) {
      const attachmentRecords = requestData.attachments.map(att => ({
        message_id: userMessage.id,
        attachment_type: att.type,
        file_url: att.url,
        mime_type: att.mimeType || 'image/jpeg',
      }))

      const { error: attachError } = await supabase
        .from('chat_attachments')
        .insert(attachmentRecords)

      if (attachError) {
        console.error('Error saving attachments:', attachError)
        // Don't throw - continue without attachments
      }
    }

    // Save assistant message
    const { data: assistantMessage, error: assistantError } = await supabase
      .from('chat_messages')
      .insert({
        conversation_id: requestData.conversationId,
        role: 'assistant',
        content: assistantContent,
        prompt_tokens: usage.prompt_tokens,
        completion_tokens: usage.completion_tokens,
        total_tokens: usage.total_tokens,
        cost_usd: totalCost,
        metadata: {
          model: 'gpt-4o',
          temperature: 0.7,
          context_used: {
            conversation: conversationContext.context_type,
            inventory_products: salonContext.inventoryCount,
            recent_services: salonContext.recentServicesCount
          }
        }
      })
      .select()
      .single()

    if (assistantError) {
      console.error('Error saving assistant message:', assistantError)
      throw assistantError
    }

    // Get updated conversation stats
    const { data: conversationStats } = await supabase
      .from('chat_conversations_with_stats')
      .select('*')
      .eq('id', requestData.conversationId)
      .single()

    const response: ChatResponse = {
      success: true,
      userMessage,
      assistantMessage,
      conversationStats
    }

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })

  } catch (error) {
    console.error('Chat assistant error:', error)
    
    const response: ChatResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Error desconocido'
    }

    return new Response(JSON.stringify(response), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
})

// Helper functions

async function loadConversationContext(conversationId: string, salonId: string) {
  const { data, error } = await supabase
    .from('chat_conversations')
    .select('*')
    .eq('id', conversationId)
    .eq('salon_id', salonId)
    .single()

  if (error || !data) {
    console.error('Error loading conversation:', error)
    return { context_type: 'general' }
  }

  // Load specific context if available
  if (data.context_type && data.context_id) {
    switch (data.context_type) {
      case 'client':
        const clientData = await loadClientContext(data.context_id, salonId)
        return { ...data, clientData }
      case 'service':
        const serviceData = await loadServiceContext(data.context_id, salonId)
        return { ...data, serviceData }
      case 'formula':
        const formulaData = await loadFormulaContext(data.context_id, salonId)
        return { ...data, formulaData }
      default:
        return data
    }
  }

  return data
}

async function loadSalonContext(salonId: string) {
  // Load inventory summary
  const { data: inventory } = await supabase
    .from('salon_products')
    .select('id, brand, product_line, product_type, shade, current_stock')
    .eq('salon_id', salonId)
    .eq('status', 'active')
    .order('brand, product_line, shade')

  // Load recent services
  const thirtyDaysAgo = new Date()
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
  
  const { data: recentServices } = await supabase
    .from('services')
    .select('id, service_date, client_id, service_type')
    .eq('salon_id', salonId)
    .gte('service_date', thirtyDaysAgo.toISOString())
    .order('service_date', { ascending: false })
    .limit(10)

  return {
    inventory: inventory || [],
    inventoryCount: inventory?.length || 0,
    recentServices: recentServices || [],
    recentServicesCount: recentServices?.length || 0
  }
}

async function getRecentMessages(conversationId: string, limit: number = 10) {
  const { data, error } = await supabase
    .from('chat_messages')
    .select('role, content')
    .eq('conversation_id', conversationId)
    .order('created_at', { ascending: false })
    .limit(limit)

  if (error) {
    console.error('Error loading messages:', error)
    return []
  }

  // Reverse to get chronological order
  return (data || []).reverse()
}

async function loadClientContext(clientId: string, salonId: string) {
  const { data } = await supabase
    .from('clients')
    .select(`
      *,
      services(
        id,
        service_date,
        service_type,
        notes
      )
    `)
    .eq('id', clientId)
    .eq('salon_id', salonId)
    .single()

  return data
}

async function loadServiceContext(serviceId: string, salonId: string) {
  const { data } = await supabase
    .from('services')
    .select(`
      *,
      clients(name, phone),
      service_photos(id, photo_url, photo_type)
    `)
    .eq('id', serviceId)
    .eq('salon_id', salonId)
    .single()

  return data
}

async function loadFormulaContext(serviceId: string, salonId: string) {
  const { data } = await supabase
    .from('services')
    .select('id, formula_text, formula_data, selected_technique')
    .eq('id', serviceId)
    .eq('salon_id', salonId)
    .single()

  return data
}

function buildContextPrompt(conversationContext: any, salonContext: any) {
  let contextPrompt = 'CONTEXTO DEL SALÓN:\n'
  
  // Add inventory context
  if (salonContext.inventory.length > 0) {
    contextPrompt += `\nINVENTARIO DISPONIBLE (${salonContext.inventoryCount} productos):\n`
    const brands = [...new Set(salonContext.inventory.map((p: any) => p.brand))]
    contextPrompt += `Marcas: ${brands.join(', ')}\n`
    
    // Group by type for summary
    const typeGroups = salonContext.inventory.reduce((acc: any, product: any) => {
      const type = product.product_type || 'otro'
      if (!acc[type]) acc[type] = 0
      acc[type]++
      return acc
    }, {})
    
    contextPrompt += 'Por tipo: ' + Object.entries(typeGroups)
      .map(([type, count]) => `${type} (${count})`)
      .join(', ') + '\n'
  }
  
  // Add conversation-specific context
  if (conversationContext.context_type === 'client' && conversationContext.clientData) {
    const client = conversationContext.clientData
    contextPrompt += `\nCONTEXTO DE CLIENTE:\n`
    contextPrompt += `Nombre: ${client.name}\n`
    if (client.allergies) contextPrompt += `Alergias: ${client.allergies}\n`
    if (client.preferences) contextPrompt += `Preferencias: ${client.preferences}\n`
    if (client.services?.length > 0) {
      contextPrompt += `Servicios previos: ${client.services.length}\n`
      contextPrompt += `Último servicio: ${new Date(client.services[0].service_date).toLocaleDateString()}\n`
    }
  }
  
  if (conversationContext.context_type === 'service' && conversationContext.serviceData) {
    const service = conversationContext.serviceData
    contextPrompt += `\nCONTEXTO DE SERVICIO:\n`
    contextPrompt += `Tipo: ${service.service_type}\n`
    contextPrompt += `Cliente: ${service.clients?.name}\n`
    contextPrompt += `Fecha: ${new Date(service.service_date).toLocaleDateString()}\n`
    if (service.notes) contextPrompt += `Notas: ${service.notes}\n`
  }
  
  if (conversationContext.context_type === 'formula' && conversationContext.formulaData) {
    const formula = conversationContext.formulaData
    contextPrompt += `\nCONTEXTO DE FÓRMULA:\n`
    if (formula.selected_technique) contextPrompt += `Técnica: ${formula.selected_technique}\n`
    if (formula.formula_text) contextPrompt += `Fórmula: ${formula.formula_text.substring(0, 200)}...\n`
  }
  
  contextPrompt += '\nResponde considerando este contexto específico del salón y la conversación.'
  
  return contextPrompt
}
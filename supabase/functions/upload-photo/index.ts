import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

interface UploadRequest {
  imageBase64: string
  salonId: string
  clientId: string
  photoType: 'before' | 'after' | 'desired'
}

interface UploadResponse {
  success: boolean
  publicUrl?: string
  error?: string
}

// Initialize Supabase clients
const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY')!
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Parse request
    const { imageBase64, salonId, clientId, photoType } = await req.json() as UploadRequest
    
    // Validate required fields
    if (!imageBase64 || !salonId || !clientId || !photoType) {
      throw new Error('Missing required fields')
    }
    
    console.log(`Processing upload for ${salonId}/${clientId}/${photoType}`)
    console.log(`Image size: ${(imageBase64.length / 1024).toFixed(2)} KB`)
    
    // Validate authentication
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('Missing authorization header')
    }
    
    // Create authenticated client to verify user
    const supabaseAuth = createClient(supabaseUrl, supabaseAnonKey, {
      global: { headers: { Authorization: authHeader } }
    })
    
    const { data: { user }, error: authError } = await supabaseAuth.auth.getUser()
    if (authError || !user) {
      throw new Error('Unauthorized')
    }
    
    console.log('User authenticated:', user.id)
    
    // Convert base64 to buffer
    const base64Clean = imageBase64.replace(/^data:image\/[a-z]+;base64,/, '')
    const binaryString = atob(base64Clean)
    const buffer = new Uint8Array(binaryString.length)
    for (let i = 0; i < binaryString.length; i++) {
      buffer[i] = binaryString.charCodeAt(i)
    }
    
    console.log(`Buffer size: ${(buffer.length / 1024).toFixed(2)} KB`)
    
    // Generate unique filename
    const timestamp = Date.now()
    const filename = `${photoType}_${timestamp}.jpg`
    const path = `${salonId}/${clientId}/${filename}`
    
    // Use service role for storage operations
    const supabaseService = createClient(supabaseUrl, supabaseServiceKey)
    
    // Upload to storage
    const { error: uploadError } = await supabaseService.storage
      .from('client-photos')
      .upload(path, buffer, {
        contentType: 'image/jpeg',
        upsert: false
      })
    
    if (uploadError) {
      console.error('Upload error:', uploadError)
      throw new Error(`Failed to upload image: ${uploadError.message}`)
    }
    
    console.log('Upload successful:', path)
    
    // Get public URL
    const { data: { publicUrl } } = supabaseService.storage
      .from('client-photos')
      .getPublicUrl(path)
    
    console.log('Public URL generated:', publicUrl)
    
    // Return success response
    const response: UploadResponse = {
      success: true,
      publicUrl
    }
    
    return new Response(
      JSON.stringify(response),
      { 
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        } 
      }
    )
    
  } catch (error: any) {
    console.error('Upload error:', error.message)
    
    const response: UploadResponse = {
      success: false,
      error: error.message || 'Unknown error occurred'
    }
    
    return new Response(
      JSON.stringify(response),
      { 
        status: error.message === 'Unauthorized' ? 401 : 500,
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        } 
      }
    )
  }
})
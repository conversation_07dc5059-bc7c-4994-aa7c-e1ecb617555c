/**
 * Constants for Salonier Assistant Edge Function
 */

// Version control for cache invalidation
export const PROMPT_VERSION = '1.0.0'

// Cache TTL configurations (in milliseconds)
export const CACHE_TTL = {
  diagnosis: 30 * 24 * 60 * 60 * 1000,    // 30 days - hair doesn't change quickly
  formula: 7 * 24 * 60 * 60 * 1000,       // 7 days - formulas might be adjusted
  desired: 14 * 24 * 60 * 60 * 1000,      // 14 days - balance between freshness and efficiency
  conversion: 30 * 24 * 60 * 60 * 1000,   // 30 days - conversions are stable
  product: 90 * 24 * 60 * 60 * 1000       // 90 days - product parsing rarely changes
}

// CORS headers
export const CORS_HEADERS = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
  'Access-Control-Max-Age': '86400',
}

// OpenAI model pricing (per 1M tokens) - Updated 2025
export const MODEL_PRICING = {
  'gpt-4o': { input: 2.50, output: 10.00 },
  'gpt-4o-mini': { input: 0.15, output: 0.60 },
  'gpt-3.5-turbo': { input: 0.50, output: 1.50 },
}

// Retry configuration
export const RETRY_CONFIG = {
  maxAttempts: 3,
  initialDelay: 1000,
  backoffMultiplier: 2,
  maxDelay: 10000
}

// Image size limits
export const IMAGE_LIMITS = {
  maxSizeMB: 4,
  maxSizeBytes: 4 * 1024 * 1024,
  warningThresholdKB: 500
}

// Template optimization targets
export const OPTIMIZATION_TARGETS = {
  tokenReduction: {
    optimized: 0.3,  // 30% reduction from full
    minimal: 0.5     // 50% reduction from full
  },
  defaultTemplate: {
    free: 'minimal' as const,
    pro: 'optimized' as const,
    enterprise: 'full' as const
  }
}

// Response validation
export const REQUIRED_FIELDS = {
  diagnosis: ['hairThickness', 'hairDensity', 'zoneAnalysis'],
  desiredLook: ['detectedLevel', 'detectedTone', 'detectedTechnique'],
  formula: ['formulaText'],
  conversion: ['convertedFormula'],
  product: ['brand', 'name', 'type'],
  analyze_product: ['brand', 'productType', 'category']
}

// Error messages
export const ERROR_MESSAGES = {
  noImage: 'No image provided',
  imageTooLarge: 'Image too large. Maximum size is 4MB',
  invalidBase64: 'Invalid image format',
  missingApiKey: 'OpenAI API key not configured',
  invalidResponse: 'Invalid response from OpenAI',
  timeout: 'Request timeout after 30 seconds',
  rateLimited: 'Rate limit exceeded. Please try again later.'
}
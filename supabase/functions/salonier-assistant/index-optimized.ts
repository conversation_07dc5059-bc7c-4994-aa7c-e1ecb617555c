/**
 * Salonier Assistant Edge Function - Ultra Optimized v2.1
 * 
 * <PERSON><PERSON>ras adicionales:
 * - Selector de templates más agresivo
 * - Debug info en respuesta
 * - Posibilidad de forzar template
 */

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Import types and constants
import { AIRequest, AIResponse, TemplateContext } from './types.ts'
import { CORS_HEADERS, ERROR_MESSAGES, PROMPT_VERSION } from './constants.ts'

// Import utilities
import { PromptTemplates } from './utils/prompt-templates.ts'
import { CacheManager } from './utils/cache-manager.ts'
import { validateAIResponse, ensureBackwardCompatibility } from './utils/response-validator.ts'

// Import helpers
import { validateAndPrepareImage } from './helpers/image-validation.ts'
import { OpenAIClient } from './helpers/openai-client.ts'

// Initialize services
const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
const openaiApiKey = Deno.env.get('OPENAI_API_KEY')

console.log('Edge Function v2.1 Ultra-Optimized initialization')

const supabase = createClient(supabaseUrl, supabaseServiceKey)
CacheManager.initialize(supabaseUrl, supabaseServiceKey)
OpenAIClient.initialize(openaiApiKey!)

// Función mejorada para seleccionar template más agresivamente
function selectAggressiveTemplate(
  imageQuality: 'high' | 'medium' | 'low',
  forceTemplate?: string
): TemplateType {
  // Si se fuerza un template, usarlo
  if (forceTemplate && ['full', 'optimized', 'minimal'].includes(forceTemplate)) {
    return forceTemplate as TemplateType
  }

  // Selector más agresivo:
  // - Alta calidad → minimal (era optimized)
  // - Media calidad → minimal (era optimized)
  // - Baja calidad → optimized (era full)
  switch (imageQuality) {
    case 'high':
      return 'minimal' // Más agresivo
    case 'medium':
      return 'minimal' // Más agresivo
    case 'low':
      return 'optimized' // Un poco menos agresivo para imágenes malas
    default:
      return 'optimized'
  }
}

// Task handlers - Ultra Optimized
async function diagnoseImage(payload: any, salonId: string): Promise<AIResponse> {
  try {
    // 1. Validate and prepare image
    const imageValidation = validateAndPrepareImage(payload)
    if (!imageValidation.isValid) {
      return { success: false, error: imageValidation.error }
    }

    // 2. Check cache
    const cacheKey = CacheManager.generateCacheKey('diagnose_image', payload)
    const cached = await CacheManager.checkCache(salonId, 'diagnose_image', cacheKey)
    
    if (cached.hit) {
      console.log(`Cache hit! Saved ${cached.savedTokens} tokens ($${cached.savedCost?.toFixed(4)})`)
      return { 
        success: true, 
        data: cached.data, 
        cached: true,
        metrics: {
          tokensUsed: 0,
          cost: 0,
          cacheHit: true
        },
        debug: {
          templateUsed: 'cache',
          imageQuality: imageValidation.quality
        }
      }
    }

    // 3. Select template aggressively
    const templateType = selectAggressiveTemplate(
      imageValidation.quality,
      payload.forceTemplate
    )
    const prompt = PromptTemplates.getDiagnosisPrompt(templateType, 'es')

    console.log(`Using ${templateType} template for diagnosis (quality: ${imageValidation.quality})`)

    // 4. Call OpenAI with reduced tokens
    const { response, tokensUsed, cost } = await OpenAIClient.callWithRetry(prompt, {
      model: 'gpt-4o',
      maxTokens: templateType === 'minimal' ? 600 : templateType === 'optimized' ? 1000 : 1500,
      temperature: 0.3,
      responseFormat: { type: 'json_object' },
      imageUrl: imageValidation.imageDataUrl
    })

    // 5. Validate response
    const validation = validateAIResponse('diagnose_image', response)
    if (!validation.isValid) {
      console.error('Invalid AI response, missing fields:', validation.missingFields)
      throw new Error('Incomplete AI response')
    }

    // 6. Ensure backward compatibility
    const compatibleResponse = ensureBackwardCompatibility('diagnose_image', response)

    // 7. Save to cache
    await CacheManager.saveToCache(
      salonId,
      'diagnose_image',
      cacheKey,
      payload,
      compatibleResponse,
      'gpt-4o',
      tokensUsed,
      cost
    )

    return { 
      success: true, 
      data: compatibleResponse,
      metrics: {
        tokensUsed,
        cost,
        cacheHit: false
      },
      debug: {
        templateUsed: templateType,
        imageQuality: imageValidation.quality,
        promptLength: prompt.length
      }
    }
  } catch (error: any) {
    console.error('Error in diagnoseImage:', error)
    return { success: false, error: error.message }
  }
}

async function analyzeDesiredLook(payload: any, salonId: string): Promise<AIResponse> {
  try {
    const { currentLevel } = payload
    
    // 1. Validate image
    const imageValidation = validateAndPrepareImage(payload)
    if (!imageValidation.isValid) {
      return { success: false, error: imageValidation.error }
    }

    // 2. Check cache
    const cacheKey = CacheManager.generateCacheKey('analyze_desired_look', payload)
    const cached = await CacheManager.checkCache(salonId, 'analyze_desired_look', cacheKey)
    
    if (cached.hit) {
      return { 
        success: true, 
        data: cached.data, 
        cached: true,
        metrics: {
          tokensUsed: 0,
          cost: 0,
          cacheHit: true
        },
        debug: {
          templateUsed: 'cache',
          imageQuality: imageValidation.quality
        }
      }
    }

    // 3. Get ultra-optimized prompt
    const templateType = selectAggressiveTemplate(
      imageValidation.quality,
      payload.forceTemplate
    )
    const prompt = PromptTemplates.getDesiredLookPrompt(currentLevel, templateType, 'es')

    console.log(`Using ${templateType} template for desired look (quality: ${imageValidation.quality})`)

    // 4. Call OpenAI
    const { response, tokensUsed, cost } = await OpenAIClient.callWithRetry(prompt, {
      model: 'gpt-4o',
      maxTokens: templateType === 'minimal' ? 400 : templateType === 'optimized' ? 600 : 800,
      temperature: 0.3,
      responseFormat: { type: 'json_object' },
      imageUrl: imageValidation.imageDataUrl
    })

    // 5. Validate and save
    const compatibleResponse = ensureBackwardCompatibility('analyze_desired_look', response)
    
    await CacheManager.saveToCache(
      salonId,
      'analyze_desired_look',
      cacheKey,
      payload,
      compatibleResponse,
      'gpt-4o',
      tokensUsed,
      cost
    )

    return { 
      success: true, 
      data: compatibleResponse,
      metrics: { tokensUsed, cost, cacheHit: false },
      debug: {
        templateUsed: templateType,
        imageQuality: imageValidation.quality,
        promptLength: prompt.length
      }
    }
  } catch (error: any) {
    console.error('Error in analyzeDesiredLook:', error)
    return { success: false, error: error.message }
  }
}

async function generateFormula(payload: any, salonId: string): Promise<AIResponse> {
  try {
    const { diagnosis, desiredResult, brand, line, clientHistory, regionalConfig } = payload
    
    // 1. Check cache
    const cacheKey = CacheManager.generateCacheKey('generate_formula', payload)
    const cached = await CacheManager.checkCache(salonId, 'generate_formula', cacheKey)
    
    if (cached.hit) {
      return { 
        success: true, 
        data: cached.data, 
        cached: true,
        metrics: { tokensUsed: 0, cost: 0, cacheHit: true },
        debug: { templateUsed: 'cache' }
      }
    }

    // 2. Build formula config
    const formulaConfig = {
      diagnosis,
      desiredResult,
      brand,
      line,
      clientHistory,
      regionalConfig: regionalConfig || {
        language: 'es',
        volumeUnit: 'ml',
        weightUnit: 'g',
        developerTerminology: 'oxidante',
        colorTerminology: 'tinte',
        maxDeveloperVolume: 40,
        currencySymbol: '€',
        measurementSystem: 'metric',
        decimalSeparator: ',',
        thousandsSeparator: '.'
      },
      selectedTechnique: desiredResult?.general?.technique || 'full_color',
      customTechnique: desiredResult?.general?.customTechnique
    }

    // 3. Get ultra-optimized prompt
    const templateType = payload.forceTemplate || 'optimized' // Default optimized for formulas
    const prompt = PromptTemplates.getFormulaPrompt(formulaConfig, templateType as TemplateType)

    console.log(`Using ${templateType} template for formula`)

    // 4. Call OpenAI
    const { response, tokensUsed, cost } = await OpenAIClient.callWithRetry(prompt, {
      model: 'gpt-4o',
      maxTokens: templateType === 'minimal' ? 800 : templateType === 'optimized' ? 1500 : 2000,
      temperature: 0.4
    })

    // 5. Structure response
    const formulaData = {
      formulaText: response,
      formulaData: {
        steps: [],
        products: [],
        processingTimes: {},
        technique: formulaConfig.selectedTechnique
      },
      totalTokens: tokensUsed
    }

    // 6. Save to cache
    await CacheManager.saveToCache(
      salonId,
      'generate_formula',
      cacheKey,
      payload,
      formulaData,
      'gpt-4o',
      tokensUsed,
      cost
    )

    return { 
      success: true, 
      data: formulaData,
      metrics: { tokensUsed, cost, cacheHit: false },
      debug: {
        templateUsed: templateType,
        promptLength: prompt.length
      }
    }
  } catch (error: any) {
    console.error('Error in generateFormula:', error)
    return { success: false, error: error.message }
  }
}

// Main handler
serve(async (req) => {
  console.log('Edge function v2.1 invoked:', {
    method: req.method,
    url: req.url,
    version: PROMPT_VERSION + '-ultra'
  })
  
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: CORS_HEADERS })
  }

  try {
    // Verify JWT
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { status: 401, headers: { ...CORS_HEADERS, 'Content-Type': 'application/json' } }
      )
    }

    // Get user and salon info
    const { data: { user }, error: authError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    )
    
    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid token' }),
        { status: 401, headers: { ...CORS_HEADERS, 'Content-Type': 'application/json' } }
      )
    }

    // Get user's salon_id
    const { data: profile } = await supabase
      .from('profiles')
      .select('salon_id')
      .eq('id', user.id)
      .single()

    if (!profile?.salon_id) {
      return new Response(
        JSON.stringify({ error: 'User not associated with a salon' }),
        { status: 403, headers: { ...CORS_HEADERS, 'Content-Type': 'application/json' } }
      )
    }

    const salonId = profile.salon_id

    // Parse request
    const { task, payload }: AIRequest = await req.json()

    // Validate OpenAI API key
    if (!openaiApiKey) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: ERROR_MESSAGES.missingApiKey 
        }),
        { status: 500, headers: { ...CORS_HEADERS, 'Content-Type': 'application/json' } }
      )
    }

    // Process based on task
    let result: AIResponse
    
    console.log(`Processing task: ${task}`)
    
    switch (task) {
      case 'diagnose_image':
        result = await diagnoseImage(payload, salonId)
        break
      case 'analyze_desired_look':
        result = await analyzeDesiredLook(payload, salonId)
        break
      case 'generate_formula':
        result = await generateFormula(payload, salonId)
        break
      default:
        result = { success: false, error: 'Invalid task' }
    }

    // Log metrics
    if (result.metrics) {
      console.log('Request metrics:', {
        task,
        cacheHit: result.metrics.cacheHit,
        tokensUsed: result.metrics.tokensUsed,
        cost: result.metrics.cost?.toFixed(4),
        templateUsed: result.debug?.templateUsed
      })
    }

    // Get and log cache metrics periodically
    const metrics = CacheManager.getMetrics()
    if (Math.random() < 0.1) { // Log 10% of the time
      console.log('Cache metrics:', {
        hitRate: `${(metrics as any).hitRate}%`,
        totalSaved: `$${metrics.totalSavedUSD.toFixed(2)}`,
        totalHits: metrics.totalHits
      })
    }

    return new Response(
      JSON.stringify(result),
      { headers: { ...CORS_HEADERS, 'Content-Type': 'application/json' } }
    )
    
  } catch (error: any) {
    console.error('Edge function error:', error)
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      { status: 500, headers: { ...CORS_HEADERS, 'Content-Type': 'application/json' } }
    )
  }
})
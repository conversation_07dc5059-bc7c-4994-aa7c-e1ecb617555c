/**
 * Image Validation Helper for Salonier Assistant
 */

import { IMAGE_LIMITS, ERROR_MESSAGES } from '../constants.ts'

export interface ImageValidationResult {
  isValid: boolean
  imageDataUrl: string
  sizeKB: number
  quality: 'high' | 'medium' | 'low'
  error?: string
}

/**
 * Validates and prepares image for AI analysis
 */
export function validateAndPrepareImage(payload: any): ImageValidationResult {
  const { imageUrl, imageBase64 } = payload
  
  // Check if any image is provided
  if (!imageUrl && !imageBase64) {
    return {
      isValid: false,
      imageDataUrl: '',
      sizeKB: 0,
      quality: 'low',
      error: ERROR_MESSAGES.noImage
    }
  }

  let imageDataUrl: string
  let sizeKB = 0

  // Handle base64 format
  if (imageBase64) {
    try {
      // Clean and validate base64
      const cleanBase64 = imageBase64.replace(/\s/g, '').replace(/\n/g, '')
      
      // Validate base64 format
      if (!/^[A-Za-z0-9+/]*={0,2}$/.test(cleanBase64)) {
        return {
          isValid: false,
          imageDataUrl: '',
          sizeKB: 0,
          quality: 'low',
          error: ERROR_MESSAGES.invalidBase64
        }
      }

      // Calculate size
      const base64SizeInBytes = (cleanBase64.length * 3) / 4
      sizeKB = base64SizeInBytes / 1024

      // Check size limit
      if (base64SizeInBytes > IMAGE_LIMITS.maxSizeBytes) {
        return {
          isValid: false,
          imageDataUrl: '',
          sizeKB,
          quality: 'low',
          error: ERROR_MESSAGES.imageTooLarge
        }
      }

      imageDataUrl = `data:image/jpeg;base64,${cleanBase64}`
    } catch (e) {
      console.error('Invalid base64:', e)
      return {
        isValid: false,
        imageDataUrl: '',
        sizeKB: 0,
        quality: 'low',
        error: ERROR_MESSAGES.invalidBase64
      }
    }
  } else {
    // Use URL directly
    imageDataUrl = imageUrl
    // For URLs, we can't easily calculate size, so estimate based on typical sizes
    sizeKB = 500 // Default estimate
  }

  // Determine image quality based on size
  let quality: 'high' | 'medium' | 'low'
  if (sizeKB < 100) {
    quality = 'low' // Very small images are usually low quality
  } else if (sizeKB < IMAGE_LIMITS.warningThresholdKB) {
    quality = 'medium'
  } else {
    quality = 'high'
  }

  // Log size info for monitoring
  console.log(`Image validation: ${sizeKB.toFixed(2)} KB, quality: ${quality}`)

  return {
    isValid: true,
    imageDataUrl,
    sizeKB,
    quality
  }
}

/**
 * Estimates image quality based on various factors
 */
export function estimateImageQuality(
  sizeKB: number,
  dimensions?: { width: number; height: number }
): 'high' | 'medium' | 'low' {
  // Size-based quality estimation
  if (sizeKB < 50) return 'low'
  if (sizeKB > 1000) return 'high'
  
  // If we have dimensions, use them for better estimation
  if (dimensions) {
    const pixels = dimensions.width * dimensions.height
    const compressionRatio = (sizeKB * 1024) / pixels
    
    // Higher compression ratio means lower quality
    if (compressionRatio < 0.1) return 'low'
    if (compressionRatio > 0.3) return 'high'
  }
  
  return 'medium'
}

/**
 * Prepares image data URL ensuring proper format
 */
export function prepareImageDataUrl(base64: string, mimeType: string = 'image/jpeg'): string {
  // Remove any existing data URL prefix
  const cleanBase64 = base64.replace(/^data:image\/[a-z]+;base64,/, '')
  
  // Return properly formatted data URL
  return `data:${mimeType};base64,${cleanBase64}`
}

/**
 * Validates if string is valid base64
 */
export function isValidBase64(str: string): boolean {
  try {
    // Check if string matches base64 pattern
    if (!/^[A-Za-z0-9+/]*={0,2}$/.test(str)) {
      return false
    }
    
    // Try to decode to verify it's valid
    atob(str)
    return true
  } catch (e) {
    return false
  }
}

/**
 * Extract image metadata from base64 if possible
 */
export function extractImageMetadata(base64: string): {
  format?: string
  sizeKB: number
  isValid: boolean
} {
  try {
    const sizeKB = (base64.length * 3) / 4 / 1024
    
    // Try to detect format from base64 header
    let format: string | undefined
    if (base64.startsWith('/9j/')) format = 'jpeg'
    else if (base64.startsWith('iVBORw')) format = 'png'
    else if (base64.startsWith('R0lGOD')) format = 'gif'
    
    return {
      format,
      sizeKB,
      isValid: true
    }
  } catch (e) {
    return {
      sizeKB: 0,
      isValid: false
    }
  }
}
/**
 * OpenAI Client Helper with Retry Logic
 */

import { OpenAIRequestOptions, OpenAIResponse } from '../types.ts'
import { RETRY_CONFIG, ERROR_MESSAGES, MODEL_PRICING } from '../constants.ts'

export class OpenAIClient {
  private static apiKey: string

  /**
   * Initialize with API key
   */
  static initialize(apiKey: string) {
    this.apiKey = apiKey
  }

  /**
   * Call OpenAI with automatic retry and error handling
   */
  static async callWithRetry(
    prompt: string,
    options: OpenAIRequestOptions
  ): Promise<{
    response: any
    tokensUsed: number
    cost: number
  }> {
    if (!this.apiKey) {
      throw new Error(ERROR_MESSAGES.missingApiKey)
    }

    let lastError: any
    
    for (let attempt = 1; attempt <= RETRY_CONFIG.maxAttempts; attempt++) {
      try {
        console.log(`OpenAI API call attempt ${attempt}/${RETRY_CONFIG.maxAttempts}`)
        
        const response = await this.makeRequest(prompt, options)
        
        // Validate response
        if (!response.choices?.[0]?.message?.content) {
          throw new Error(ERROR_MESSAGES.invalidResponse)
        }

        // Parse JSON if expected
        let parsedContent = response.choices[0].message.content
        if (options.responseFormat?.type === 'json_object') {
          try {
            parsedContent = JSON.parse(parsedContent)
          } catch (e) {
            console.error('Failed to parse JSON response:', e)
            throw new Error('Invalid JSON in AI response')
          }
        }

        // Calculate cost
        const cost = this.calculateCost(
          options.model,
          response.usage.prompt_tokens,
          response.usage.completion_tokens
        )

        return {
          response: parsedContent,
          tokensUsed: response.usage.total_tokens,
          cost
        }

      } catch (error: any) {
        lastError = error
        console.error(`OpenAI API error (attempt ${attempt}):`, error.message)

        // Handle specific error types
        if (error.status === 429 || error.message?.includes('rate limit')) {
          // Rate limit - exponential backoff
          const delay = Math.min(
            RETRY_CONFIG.initialDelay * Math.pow(RETRY_CONFIG.backoffMultiplier, attempt - 1),
            RETRY_CONFIG.maxDelay
          )
          console.log(`Rate limited. Waiting ${delay}ms before retry...`)
          await new Promise(resolve => setTimeout(resolve, delay))
          continue
        }

        // Don't retry on certain errors
        if (
          error.status === 401 || // Invalid API key
          error.status === 400 || // Bad request
          error.message?.includes('Invalid API key')
        ) {
          throw error
        }

        // For other errors, retry with delay
        if (attempt < RETRY_CONFIG.maxAttempts) {
          const delay = RETRY_CONFIG.initialDelay * attempt
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    }

    // All retries failed
    throw lastError || new Error('Max retry attempts reached')
  }

  /**
   * Make the actual API request
   */
  private static async makeRequest(
    prompt: string,
    options: OpenAIRequestOptions
  ): Promise<OpenAIResponse> {
    const messages = [
      {
        role: 'user',
        content: options.imageUrl 
          ? [
              { type: 'text', text: prompt },
              { type: 'image_url', image_url: { url: options.imageUrl } }
            ]
          : prompt
      }
    ]

    const requestBody: any = {
      model: options.model,
      messages,
      max_tokens: options.maxTokens,
      temperature: options.temperature
    }

    // Add response format if specified
    if (options.responseFormat) {
      requestBody.response_format = options.responseFormat
    }

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      
      if (response.status === 401) {
        throw new Error(ERROR_MESSAGES.missingApiKey)
      } else if (response.status === 429) {
        throw new Error(ERROR_MESSAGES.rateLimited)
      } else {
        throw {
          status: response.status,
          message: errorData.error?.message || response.statusText
        }
      }
    }

    return await response.json()
  }

  /**
   * Calculate cost for the API call
   */
  private static calculateCost(
    model: string,
    inputTokens: number,
    outputTokens: number
  ): number {
    const pricing = MODEL_PRICING[model as keyof typeof MODEL_PRICING] || MODEL_PRICING['gpt-4o']
    const inputCost = (inputTokens / 1_000_000) * pricing.input
    const outputCost = (outputTokens / 1_000_000) * pricing.output
    return inputCost + outputCost
  }

  /**
   * Estimate tokens for a prompt (rough estimate)
   */
  static estimateTokens(text: string): number {
    // Rough estimate: 1 token ≈ 4 characters for English
    // Adjust for Spanish/special characters
    const charCount = text.length
    const wordCount = text.split(/\s+/).length
    
    // Use a weighted average
    const charEstimate = charCount / 4
    const wordEstimate = wordCount * 1.3
    
    return Math.ceil((charEstimate + wordEstimate) / 2)
  }

  /**
   * Validate API key format
   */
  static isValidApiKey(apiKey: string): boolean {
    // OpenAI API keys start with 'sk-' and are 51 characters long
    return apiKey.startsWith('sk-') && apiKey.length > 40
  }
}
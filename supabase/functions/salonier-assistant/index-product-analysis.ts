import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
  'Access-Control-Max-Age': '86400',
}

// Initialize OpenAI API key
const openaiApiKey = Deno.env.get('OPENAI_API_KEY')

// Main handler
serve(async (req) => {
  console.log(`🚀 EDGE FUNCTION CALLED: ${req.method} ${req.url}`)

  // Handle CORS
  if (req.method === 'OPTIONS') {
    console.log('Returning CORS preflight response')
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Parse request body
    let requestBody
    try {
      requestBody = await req.json()
      console.log('Request body parsed:', JSON.stringify(requestBody, null, 2))
    } catch (parseError) {
      console.error('Request body parse error:', parseError.message)
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid JSON in request body'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    const { task, payload } = requestBody

    console.log('Task:', task)
    console.log('Payload:', payload)

    if (!task || !payload) {
      console.error('Missing task or payload')
      return new Response(JSON.stringify({
        success: false,
        error: 'Missing task or payload'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // Handle analyze_product task
    if (task === 'analyze_product') {
      const { productName, language = 'es', brandContext, lineContext } = payload

      // 🧠 CONTEXTUAL VALIDATION: Relax validation when brand/line context is provided
      const hasContext = brandContext || lineContext
      const minLength = hasContext ? 1 : 2
      const trimmedProductName = productName?.trim() || ''
      
      if (!productName || typeof productName !== 'string' || trimmedProductName.length < minLength) {
        console.error('Invalid product name:', {
          productName,
          trimmedLength: trimmedProductName.length,
          minLength,
          hasContext,
          brandContext: brandContext?.name,
          lineContext: lineContext?.name
        })
        
        const errorMessage = hasContext 
          ? (language === 'es' ? 'Nombre de producto requerido (mínimo 1 carácter)' : 'Product name required (minimum 1 character)')
          : (language === 'es' ? 'Nombre de producto requerido (mínimo 2 caracteres)' : 'Product name required (minimum 2 characters)')
          
        return new Response(JSON.stringify({
          success: false,
          error: errorMessage
        }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }
      
      console.log('✅ Validation passed:', {
        productName,
        trimmedLength: trimmedProductName.length,
        minLength,
        hasContext: hasContext ? 'Yes' : 'No'
      })

      // Check if OpenAI API key is available
      if (!openaiApiKey) {
        console.error('OpenAI API key not configured')
        return new Response(JSON.stringify({
          success: false,
          error: 'OpenAI API key not configured'
        }), {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }

      try {
        // Prepare context-aware prompt
        const isSpanish = language === 'es'
        
        // Build context information
        let contextInfo = ''
        if (brandContext) {
          contextInfo += `\n\nCONTEXTO DE MARCA SELECCIONADA:\n`
          contextInfo += `- Marca: ${brandContext.name} (${brandContext.country || 'País no especificado'})\n`
          if (brandContext.lines && brandContext.lines.length > 0) {
            contextInfo += `- Líneas disponibles: ${brandContext.lines.join(', ')}\n`
          }
        }
        
        if (lineContext) {
          contextInfo += `\n\nCONTEXTO DE LÍNEA SELECCIONADA:\n`
          contextInfo += `- Línea: ${lineContext.name}\n`
          if (lineContext.description) {
            contextInfo += `- Descripción: ${lineContext.description}\n`
          }
        }
        
        const prompt = isSpanish ? 
          `Eres un experto mundial en productos profesionales de peluquería. Analiza este producto y extrae información estructurada.

PRODUCTO: "${productName}"${contextInfo}

🚨 REGLAS OBLIGATORIAS DE CONTEXTO (PRIORITY 1):
${brandContext ? `✅ MARCA OBLIGATORIA: Debes usar EXACTAMENTE "${brandContext.name}" como brand
✅ PAÍS CONFIRMADO: ${brandContext.country}` : '❌ Sin contexto de marca - usar conocimiento general'}
${lineContext ? `✅ LÍNEA OBLIGATORIA: Debes usar EXACTAMENTE "${lineContext.name}" como line
✅ DESCRIPCIÓN: ${lineContext.description || 'Sin descripción'}` : '❌ Sin contexto de línea - inferir de la marca'}

🔒 CONSTRUCCIÓN OBLIGATORIA DE PRODUCTO:
${lineContext ? `✅ ProductName DEBE ser: "${lineContext.name} ${productName.replace(/[^0-9.,\/]/g, '')}"` : `✅ ProductName debe incluir marca y código/tono`}

⚠️ VALIDACIÓN CRÍTICA:
- Si hay brandContext → field "brand" DEBE ser "${brandContext?.name || 'NO_BRAND'}"
- Si hay lineContext → field "line" DEBE ser "${lineContext?.name || 'NO_LINE'}"
- NUNCA uses otras marcas si se proporciona contexto específico
- NUNCA uses otras líneas si se proporciona contexto específico

CONOCIMIENTO GLOBAL DE MARCAS (solo para inferencia sin contexto):
🇪🇸 ESPAÑOLAS: Salerm, Kemon, Tahe, Montibello, Hipertin, Válquer
🇺🇸 AMERICANAS: Matrix, Redken, Paul Mitchell, Joico, Schwarzkopf, Clairol, Revlon
🇫🇷 FRANCESAS: L'Oréal, Kérastase, Garnier, Dessange
🇩🇪 ALEMANAS: Wella, Schwarzkopf, Goldwell, Indola
🇮🇹 ITALIANAS: Alfaparf, Davines, Echosline, Selective
🇯🇵 JAPONESAS: Milbon, Shiseido, Lebel
🌍 INTERNACIONALES: Aveda, Tigi, Olaplex, Moroccanoil

PATRONES DE CÓDIGOS PROFESIONALES:
- SV001, SV002, SV005... = Salerm línea Salermvision (tintes profesionales)
- CV001, CV002... = Líneas de color profesionales
- 8/0, 9.1, 7/3 = Tonos de tinte (numeración internacional)
- 20vol, 30vol, 40vol = Oxidantes (volúmenes)
- Códigos numéricos = Referencias profesionales específicas

CONOCIMIENTO ESPECÍFICO DE LÍNEAS:
- Salerm SV### = Línea "Salermvision", productName debería ser "Salermvision SV###"
- Matrix SoColor = Línea "SoColor", tintes profesionales
- Wella Koleston = Línea "Koleston Perfect", tintes permanentes
- L'Oréal Majirel = Línea "Majirel", tintes profesionales
- Davines Alchemic = Línea "Alchemic", tratamientos de color

INFERENCIA INTELIGENTE:
1. Si hay CONTEXTO DE MARCA → usa esa marca específica como brand
2. Si hay CONTEXTO DE LÍNEA → usa esa línea específica como line
3. Si reconoces la MARCA → infiere tipo de producto más probable
4. Si hay CÓDIGO (SV001, CV002) → probablemente tinte/color
5. Si hay VOLUMEN (20vol, 30vol) → oxidante
6. Si hay TONO (8/0, 9.1) → tinte
7. Si es marca profesional conocida → usa contexto de la marca

PRIORIDAD DE CONTEXTO:
- Si hay brandContext → SIEMPRE usa brandContext.name como "brand"
- Si hay lineContext → SIEMPRE usa lineContext.name como "line"
- El productName debe construirse considerando la línea y código/referencia

EJEMPLOS SOLO SI NO HAY CONTEXTO ESPECÍFICO:
- "SV005" (sin contexto) → brand="Salerm", line="Salermvision", productName="Salermvision SV005"
- "8/0" (sin contexto) → inferir marca más probable para el tono
- "Oxidante 20 vol" → productType="Oxidante", productName="Oxidante 20 vol", tone="20 vol"

EJEMPLOS CON CONTEXTO (PRIORIDAD MÁXIMA):
- "9.1" + brandContext="Wella" + lineContext="Illumina Color" → brand="Wella Professionals", line="Illumina Color", productName="Illumina Color 9.1", tone="9.1"
- "SV005" + brandContext="Salerm" → brand="Salerm Cosmetics", line="Salermvision", productName="Salermvision SV005"

REGLAS ESPECÍFICAS PARA COMPLETAR CAMPOS:
1. SIEMPRE completa "productName" con línea + código/tono
2. Para códigos SV### → line="Salermvision", productName="Salermvision SV###"
3. Para códigos CV### → line="Color", productName="Color CV###"
4. Para tonos 8/0, 9.1 → incluye en productName y tone
5. NUNCA dejes productName vacío si puedes construirlo

CATEGORÍAS: "tinte", "oxidante", "decolorante", "tratamiento", "otro"
UNIDADES: "ml", "g", "unidad"

VALIDACIÓN FLEXIBLE:
- Para marcas profesionales conocidas: SIEMPRE válido aunque falten campos
- Para códigos profesionales: SIEMPRE válido si identificas marca/tipo
- NUNCA devuelvas todos los campos como null para marcas conocidas

Responde SOLO con JSON válido:
{
  "brand": "string o null",
  "productType": "string o null", 
  "line": "string o null",
  "productName": "string o null",
  "tone": "string o null",
  "category": "tinte|oxidante|decolorante|tratamiento|otro o null",
  "packageSize": "number o null",
  "unit": "ml|g|unidad o null",
  "barcode": "string o null",
  "additionalInfo": "string o null"
}` :
          `You are a global expert in professional hairdressing products. Analyze this product and extract structured information.

PRODUCT: "${productName}"${contextInfo}

🚨 MANDATORY CONTEXT RULES (PRIORITY 1):
${brandContext ? `✅ MANDATORY BRAND: You MUST use EXACTLY "${brandContext.name}" as brand
✅ CONFIRMED COUNTRY: ${brandContext.country}` : '❌ No brand context - use general knowledge'}
${lineContext ? `✅ MANDATORY LINE: You MUST use EXACTLY "${lineContext.name}" as line
✅ DESCRIPTION: ${lineContext.description || 'No description'}` : '❌ No line context - infer from brand'}

🔒 MANDATORY PRODUCT CONSTRUCTION:
${lineContext ? `✅ ProductName MUST be: "${lineContext.name} ${productName.replace(/[^0-9.,\/]/g, '')}"` : `✅ ProductName must include brand and code/tone`}

⚠️ CRITICAL VALIDATION:
- If brandContext exists → field "brand" MUST be "${brandContext?.name || 'NO_BRAND'}"
- If lineContext exists → field "line" MUST be "${lineContext?.name || 'NO_LINE'}"
- NEVER use other brands if specific context is provided
- NEVER use other lines if specific context is provided

GLOBAL BRAND KNOWLEDGE (for inference without context only):
🇪🇸 SPANISH: Salerm, Kemon, Tahe, Montibello, Hipertin, Válquer
🇺🇸 AMERICAN: Matrix, Redken, Paul Mitchell, Joico, Schwarzkopf, Clairol, Revlon
🇫🇷 FRENCH: L'Oréal, Kérastase, Garnier, Dessange
🇩🇪 GERMAN: Wella, Schwarzkopf, Goldwell, Indola
🇮🇹 ITALIAN: Alfaparf, Davines, Echosline, Selective
🇯🇵 JAPANESE: Milbon, Shiseido, Lebel
🌍 INTERNATIONAL: Aveda, Tigi, Olaplex, Moroccanoil

PROFESSIONAL CODE PATTERNS:
- SV001, SV002, SV005... = Salerm Salermvision line (professional hair dyes)
- CV001, CV002... = Professional color lines
- 8/0, 9.1, 7/3 = Hair dye tones (international numbering)
- 20vol, 30vol, 40vol = Developers (volumes)
- Numeric codes = Specific professional references

SPECIFIC LINE KNOWLEDGE:
- Salerm SV### = "Salermvision" line, productName should be "Salermvision SV###"
- Matrix SoColor = "SoColor" line, professional hair dyes
- Wella Koleston = "Koleston Perfect" line, permanent hair dyes
- L'Oréal Majirel = "Majirel" line, professional hair dyes
- Davines Alchemic = "Alchemic" line, color treatments

INTELLIGENT INFERENCE:
1. If there's BRAND CONTEXT → use that specific brand as brand
2. If there's LINE CONTEXT → use that specific line as line
3. If you recognize the BRAND → infer most likely product type
4. If there's a CODE (SV001, CV002) → probably hair dye/color
5. If there's VOLUME (20vol, 30vol) → developer
6. If there's TONE (8/0, 9.1) → hair dye
7. If it's a known professional brand → use brand context

CONTEXT PRIORITY:
- If brandContext exists → ALWAYS use brandContext.name as "brand"
- If lineContext exists → ALWAYS use lineContext.name as "line"
- The productName should be constructed considering the line and code/reference

EXAMPLES ONLY IF NO SPECIFIC CONTEXT:
- "SV005" (no context) → brand="Salerm", line="Salermvision", productName="Salermvision SV005"
- "8/0" (no context) → infer most likely brand for the tone
- "Developer 20 vol" → productType="Developer", productName="Developer 20 vol", tone="20 vol"

EXAMPLES WITH CONTEXT (MAXIMUM PRIORITY):
- "9.1" + brandContext="Wella" + lineContext="Illumina Color" → brand="Wella Professionals", line="Illumina Color", productName="Illumina Color 9.1", tone="9.1"
- "SV005" + brandContext="Salerm" → brand="Salerm Cosmetics", line="Salermvision", productName="Salermvision SV005"

SPECIFIC RULES FOR COMPLETING FIELDS:
1. ALWAYS complete "productName" with line + code/tone
2. For SV### codes → line="Salermvision", productName="Salermvision SV###"
3. For CV### codes → line="Color", productName="Color CV###"
4. For tones 8/0, 9.1 → include in both productName and tone
5. NEVER leave productName empty if you can construct it

CATEGORIES: "tinte", "oxidante", "decolorante", "tratamiento", "otro"
UNITS: "ml", "g", "unidad"

FLEXIBLE VALIDATION:
- For known professional brands: ALWAYS valid even if some fields are missing
- For professional codes: ALWAYS valid if you identify brand/type
- NEVER return all fields as null for known brands

Respond ONLY with valid JSON:
{
  "brand": "string or null",
  "productType": "string or null",
  "line": "string or null", 
  "productName": "string or null",
  "tone": "string or null",
  "category": "tinte|oxidante|decolorante|tratamiento|otro or null",
  "packageSize": "number or null",
  "unit": "ml|g|unidad or null",
  "barcode": "string or null",
  "additionalInfo": "string or null"
}`

        console.log('Calling OpenAI with prompt length:', prompt.length)
        console.log('Product name:', productName)

        // Call OpenAI
        const response = await fetch('https://api.openai.com/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${openaiApiKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: 'gpt-4o-mini',
            messages: [
              { role: 'system', content: prompt },
              { role: 'user', content: `Analiza: "${productName}"` }
            ],
            max_tokens: 500,
            temperature: 0.1,
            response_format: { type: 'json_object' }
          })
        })

        console.log('OpenAI response status:', response.status)
        console.log('OpenAI response ok:', response.ok)

        if (!response.ok) {
          const errorText = await response.text()
          console.error('OpenAI error response:', errorText)
          throw new Error(`OpenAI API error: ${response.status} - ${errorText}`)
        }

        const data = await response.json()
        console.log('OpenAI response data:', JSON.stringify(data, null, 2))

        if (!data.choices || !data.choices[0]) {
          console.error('Invalid OpenAI response structure')
          throw new Error('Invalid OpenAI response structure')
        }

        const aiContentRaw = data.choices[0].message.content
        console.log('AI content raw:', aiContentRaw)

        let aiResponse
        try {
          aiResponse = JSON.parse(aiContentRaw)
          console.log('AI response parsed:', JSON.stringify(aiResponse, null, 2))
        } catch (parseError) {
          console.error('JSON parse error:', parseError.message)
          console.error('Content that failed to parse:', aiContentRaw)
          throw new Error(`Failed to parse AI response as JSON: ${parseError.message}`)
        }

        // 🚨 PHASE 2: CONTEXT VALIDATION - Ensure AI respects provided context
        let contextViolations = []
        let forceCorrections = false

        if (brandContext && aiResponse.brand !== brandContext.name) {
          console.warn(`🚨 CONTEXT VIOLATION: AI returned brand "${aiResponse.brand}" but context requires "${brandContext.name}"`)
          contextViolations.push(`Brand mismatch: AI="${aiResponse.brand}" vs Context="${brandContext.name}"`)
          aiResponse.brand = brandContext.name // Force correction
          forceCorrections = true
        }

        if (lineContext && aiResponse.line !== lineContext.name) {
          console.warn(`🚨 CONTEXT VIOLATION: AI returned line "${aiResponse.line}" but context requires "${lineContext.name}"`)
          contextViolations.push(`Line mismatch: AI="${aiResponse.line}" vs Context="${lineContext.name}"`)
          aiResponse.line = lineContext.name // Force correction
          forceCorrections = true
        }

        // 🔒 PHASE 3: FORCE PRODUCT NAME CONSTRUCTION with context
        if (lineContext) {
          const extractedTone = productName.replace(/[^0-9.,\/]/g, '').trim()
          const expectedProductName = `${lineContext.name} ${extractedTone}`
          if (aiResponse.productName !== expectedProductName) {
            console.warn(`🔧 FORCING ProductName: AI="${aiResponse.productName}" → Context="${expectedProductName}"`)
            aiResponse.productName = expectedProductName
            forceCorrections = true
          }
        }

        if (forceCorrections) {
          console.log('🔧 Context corrections applied:', {
            violations: contextViolations,
            correctedResponse: aiResponse
          })
        } else {
          console.log('✅ AI response respects all provided context')
        }

        // Professional brands validation - more flexible for known brands
        const professionalBrands = [
          'salerm', 'kemon', 'tahe', 'montibello', 'hipertin', 'válquer',
          'matrix', 'redken', 'paul mitchell', 'joico', 'schwarzkopf', 'clairol', 'revlon',
          'l\'oréal', 'kérastase', 'garnier', 'dessange',
          'wella', 'goldwell', 'indola',
          'alfaparf', 'davines', 'echosline', 'selective',
          'milbon', 'shiseido', 'lebel',
          'aveda', 'tigi', 'olaplex', 'moroccanoil'
        ]
        
        const brandLower = aiResponse.brand?.toLowerCase() || ''
        const isProfessionalBrand = professionalBrands.some(brand => 
          brandLower.includes(brand) || brand.includes(brandLower)
        )
        
        // Check for professional code patterns
        const hasProCode = /^(SV|CV|SC|HC|TC)\d{3}$/i.test(productName) || 
                          /^\d{1,2}\/\d{1,2}$/.test(productName) || 
                          /^\d{1,2}\.?\d{1,2}?[A-Z]?$/.test(productName)
        
        // Flexible validation logic
        let hasValidData = false
        let validationReason = ''
        
        if (isProfessionalBrand) {
          // For professional brands: valid if we have brand + any other field
          hasValidData = aiResponse.brand && (aiResponse.productType || aiResponse.category || aiResponse.line)
          validationReason = 'Professional brand identified'
        } else if (hasProCode) {
          // For professional codes: valid if we have brand or productType
          hasValidData = aiResponse.brand || aiResponse.productType
          validationReason = 'Professional code pattern detected'
        } else {
          // Standard validation: need brand + productType + category
          hasValidData = aiResponse.brand && aiResponse.productType && aiResponse.category
          validationReason = 'Standard validation'
        }
        
        console.log('Enhanced validation:', {
          hasValidData,
          validationReason,
          isProfessionalBrand,
          hasProCode,
          brand: aiResponse.brand,
          productType: aiResponse.productType,
          category: aiResponse.category
        })

        if (!hasValidData) {
          console.log('AI could not identify product, returning user-friendly error')
          return new Response(JSON.stringify({
            success: false,
            error: isSpanish ? 
              'No se pudo identificar el producto. Asegúrate de usar el nombre completo del producto (ej: "Wella Koleston 8/0")' :
              'Could not identify the product. Please use the full product name (e.g., "Wella Koleston 8/0")'
          }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          })
        }

        console.log('Returning successful response:', aiResponse)

        return new Response(JSON.stringify({
          success: true,
          data: aiResponse,
          cached: false
        }), {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })

      } catch (error) {
        console.error('Error in analyze_product:', error)
        return new Response(JSON.stringify({
          success: false,
          error: error.message || 'Failed to analyze product'
        }), {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }
    }

    // For other tasks, return not implemented
    return new Response(JSON.stringify({
      success: false,
      error: `Task ${task} not implemented`
    }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })

  } catch (error) {
    console.error('Main handler error:', error)
    return new Response(JSON.stringify({
      success: false,
      error: error.message || 'Internal server error'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})
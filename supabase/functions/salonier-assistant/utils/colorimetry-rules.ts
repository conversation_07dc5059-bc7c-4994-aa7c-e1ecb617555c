/**
 * Colorimetry Rules and Validation
 * Professional hair coloring principles and technical validations
 */

export interface ColorProcess {
  currentLevel: number;
  desiredLevel: number;
  currentState: 'natural' | 'colored' | 'bleached' | 'mixed';
  hasMetallicSalts?: boolean;
  hasHenna?: boolean;
}

export interface ProcessValidation {
  isViable: boolean;
  requiredProcesses: ProcessType[];
  warnings: string[];
  recommendedDeveloperVolume: number;
  estimatedSessions: number;
}

export enum ProcessType {
  DIRECT_COLOR = 'direct_color',
  BLEACHING = 'bleaching',
  COLOR_REMOVAL = 'color_removal',
  PRE_PIGMENTATION = 'pre_pigmentation',
  NEUTRALIZATION = 'neutralization',
  TONING = 'toning'
}

/**
 * Core colorimetry principles
 */
export const COLORIMETRY_PRINCIPLES = {
  // Color cannot lift color
  COLOR_LIFT_LIMIT: {
    natural: 3,     // Natural hair can be lifted up to 3 levels with color
    colored: 0,     // Colored hair cannot be lifted with color
    bleached: 0     // Bleached hair cannot be lifted with color
  },
  
  // Developer volume guidelines
  DEVELOPER_VOLUMES: {
    depositing: 10,        // For depositing color only (going darker)
    gray_coverage: 20,     // Standard gray coverage
    lifting_1_2: 20,       // Lifting 1-2 levels
    lifting_2_3: 30,       // Lifting 2-3 levels
    lifting_3_4: 40,       // Lifting 3-4 levels (max in some regions)
    bleaching: 30          // Standard for bleaching
  },
  
  // Pre-pigmentation thresholds
  PRE_PIGMENT_THRESHOLD: 3,  // Need pre-pigment when going darker by 3+ levels
  
  // Maximum lift per session
  MAX_LIFT_PER_SESSION: 4
}

/**
 * Validates if a color process is technically viable and determines required steps
 */
export function validateColorProcess(process: ColorProcess, regionalMaxVolume: number = 40): ProcessValidation {
  const { currentLevel, desiredLevel, currentState, hasMetallicSalts, hasHenna } = process;
  const levelDifference = desiredLevel - currentLevel;
  
  const validation: ProcessValidation = {
    isViable: true,
    requiredProcesses: [],
    warnings: [],
    recommendedDeveloperVolume: 20,
    estimatedSessions: 1
  };
  
  // Check for incompatible products
  if (hasMetallicSalts) {
    validation.warnings.push('Presencia de sales metálicas detectada. Requiere test de mechón y posible tratamiento de eliminación.');
    validation.isViable = false;
    return validation;
  }
  
  if (hasHenna) {
    validation.warnings.push('Presencia de henna detectada. La decoloración puede causar reacciones impredecibles.');
    validation.warnings.push('Se recomienda cortar el cabello con henna o esperar a que crezca.');
  }
  
  // Determine process based on level change and current state
  if (levelDifference > 0) {
    // LIGHTENING PROCESS
    const maxLiftWithColor = COLORIMETRY_PRINCIPLES.COLOR_LIFT_LIMIT[currentState];
    
    if (levelDifference > maxLiftWithColor) {
      // Cannot achieve with color alone
      if (currentState === 'colored') {
        validation.requiredProcesses.push(ProcessType.COLOR_REMOVAL);
        validation.warnings.push('Color no levanta color. Se requiere decapado previo para eliminar pigmentos artificiales.');
      }
      
      validation.requiredProcesses.push(ProcessType.BLEACHING);
      
      // Calculate sessions needed
      const liftsNeeded = levelDifference - maxLiftWithColor;
      validation.estimatedSessions = Math.ceil(liftsNeeded / COLORIMETRY_PRINCIPLES.MAX_LIFT_PER_SESSION);
      
      if (validation.estimatedSessions > 1) {
        validation.warnings.push(`Se requieren ${validation.estimatedSessions} sesiones para alcanzar el nivel deseado de forma segura.`);
      }
      
      // Set developer volume for bleaching
      validation.recommendedDeveloperVolume = Math.min(30, regionalMaxVolume);
    } else {
      // Can achieve with color
      validation.requiredProcesses.push(ProcessType.DIRECT_COLOR);
      
      // Determine developer volume based on lift needed
      if (levelDifference <= 1) {
        validation.recommendedDeveloperVolume = 20;
      } else if (levelDifference <= 2) {
        validation.recommendedDeveloperVolume = Math.min(30, regionalMaxVolume);
      } else {
        validation.recommendedDeveloperVolume = Math.min(40, regionalMaxVolume);
      }
    }
    
  } else if (levelDifference < 0) {
    // DARKENING PROCESS
    const levelsDarker = Math.abs(levelDifference);
    
    if (levelsDarker >= COLORIMETRY_PRINCIPLES.PRE_PIGMENT_THRESHOLD) {
      validation.requiredProcesses.push(ProcessType.PRE_PIGMENTATION);
      validation.warnings.push(`Pre-pigmentación requerida al oscurecer ${levelsDarker} niveles para evitar resultados verdosos.`);
    }
    
    validation.requiredProcesses.push(ProcessType.DIRECT_COLOR);
    
    // For darkening, only need low volume developer
    validation.recommendedDeveloperVolume = COLORIMETRY_PRINCIPLES.DEVELOPER_VOLUMES.depositing;
    
  } else {
    // SAME LEVEL (tone change)
    validation.requiredProcesses.push(ProcessType.DIRECT_COLOR);
    
    // Check if it's just a toning process
    if (currentState === 'bleached' || currentLevel >= 9) {
      validation.requiredProcesses = [ProcessType.TONING];
      validation.recommendedDeveloperVolume = COLORIMETRY_PRINCIPLES.DEVELOPER_VOLUMES.depositing;
    } else {
      validation.recommendedDeveloperVolume = COLORIMETRY_PRINCIPLES.DEVELOPER_VOLUMES.gray_coverage;
    }
  }
  
  // Add specific warnings for challenging processes
  if (currentLevel <= 3 && desiredLevel >= 9) {
    validation.warnings.push('Proceso de alto riesgo: aclarar cabello muy oscuro a rubio muy claro puede causar daño severo.');
    validation.warnings.push('Se recomienda encarecidamente realizar el proceso en múltiples sesiones con tratamientos intermedios.');
  }
  
  if (currentState === 'bleached' && levelDifference < -3) {
    validation.warnings.push('Oscurecer cabello decolorado requiere especial atención a la porosidad.');
    validation.warnings.push('Considerar usar un relleno de color o tratamiento de porosidad previo.');
  }
  
  return validation;
}

/**
 * Determines the specific colorimetry instructions for the AI
 */
export function getColorimetryInstructions(
  validation: ProcessValidation,
  lang: 'es' | 'en' = 'es'
): string {
  const instructions: string[] = [];
  
  if (lang === 'es') {
    // Spanish instructions
    instructions.push('**PRINCIPIOS DE COLORIMETRÍA APLICADOS:**');
    
    validation.requiredProcesses.forEach(process => {
      switch (process) {
        case ProcessType.COLOR_REMOVAL:
          instructions.push('- DECAPADO REQUERIDO: Eliminar pigmentos artificiales antes de aclarar');
          instructions.push('  - Usar producto decapante suave si es posible');
          instructions.push('  - Evaluar el estado del cabello después del decapado');
          break;
          
        case ProcessType.BLEACHING:
          instructions.push('- DECOLORACIÓN REQUERIDA: Para alcanzar el nivel deseado');
          instructions.push(`  - Usar oxidante de ${validation.recommendedDeveloperVolume} volúmenes máximo`);
          instructions.push('  - Monitorear constantemente el proceso');
          break;
          
        case ProcessType.PRE_PIGMENTATION:
          instructions.push('- PRE-PIGMENTACIÓN REQUERIDA: Para evitar tonos no deseados');
          instructions.push('  - Aplicar pigmento cálido del nivel intermedio');
          instructions.push('  - Sin oxidante o con 10 volúmenes máximo');
          break;
          
        case ProcessType.DIRECT_COLOR:
          instructions.push(`- COLORACIÓN DIRECTA: Oxidante de ${validation.recommendedDeveloperVolume} volúmenes`);
          break;
          
        case ProcessType.TONING:
          instructions.push('- MATIZACIÓN: Proceso de depósito únicamente');
          instructions.push('  - Usar oxidante de 10 volúmenes o activador sin amoniaco');
          break;
      }
    });
    
    instructions.push('\n**VOLUMEN DE OXIDANTE:**');
    instructions.push(`- USA EXACTAMENTE ${validation.recommendedDeveloperVolume} VOLÚMENES`);
    instructions.push('- NO uses volumen mayor al necesario');
    
  } else {
    // English instructions
    instructions.push('**APPLIED COLORIMETRY PRINCIPLES:**');
    
    validation.requiredProcesses.forEach(process => {
      switch (process) {
        case ProcessType.COLOR_REMOVAL:
          instructions.push('- COLOR REMOVAL REQUIRED: Remove artificial pigments before lightening');
          instructions.push('  - Use gentle color remover if possible');
          instructions.push('  - Evaluate hair condition after removal');
          break;
          
        case ProcessType.BLEACHING:
          instructions.push('- BLEACHING REQUIRED: To achieve desired level');
          instructions.push(`  - Use maximum ${validation.recommendedDeveloperVolume} volume developer`);
          instructions.push('  - Monitor process constantly');
          break;
          
        case ProcessType.PRE_PIGMENTATION:
          instructions.push('- PRE-PIGMENTATION REQUIRED: To avoid unwanted tones');
          instructions.push('  - Apply warm pigment at intermediate level');
          instructions.push('  - No developer or maximum 10 volume');
          break;
          
        case ProcessType.DIRECT_COLOR:
          instructions.push(`- DIRECT COLORING: ${validation.recommendedDeveloperVolume} volume developer`);
          break;
          
        case ProcessType.TONING:
          instructions.push('- TONING: Deposit only process');
          instructions.push('  - Use 10 volume developer or ammonia-free activator');
          break;
      }
    });
    
    instructions.push('\n**DEVELOPER VOLUME:**');
    instructions.push(`- USE EXACTLY ${validation.recommendedDeveloperVolume} VOLUMES`);
    instructions.push('- DO NOT use higher volume than necessary');
  }
  
  return instructions.join('\n');
}
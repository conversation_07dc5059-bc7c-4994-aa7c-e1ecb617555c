/**
 * Cache Manager with Metrics for Salonier Assistant
 */

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { CacheResult, CacheMetrics } from '../types.ts'
import { CACHE_TTL, MODEL_PRICING, PROMPT_VERSION } from '../constants.ts'

export class CacheManager {
  private static metrics: CacheMetrics = {
    totalHits: 0,
    totalMisses: 0,
    avgSavingsPerHit: 0,
    mostCachedQueries: [],
    totalSavedUSD: 0,
    totalSavedTokens: 0
  }
  
  private static supabase: any
  private static metricsLastSaved = 0
  private static readonly METRICS_SAVE_INTERVAL = 5 * 60 * 1000 // 5 minutes

  /**
   * Initialize cache manager with Supabase client
   */
  static initialize(supabaseUrl: string, supabaseKey: string) {
    this.supabase = createClient(supabaseUrl, supabaseKey)
    this.loadMetrics()
  }

  /**
   * Generate cache key with version
   */
  static generateCacheKey(task: string, payload: any): string {
    const normalized = JSON.stringify({
      ...payload,
      _version: PROMPT_VERSION,
      _task: task
    }, Object.keys(payload).sort())
    
    const encoder = new TextEncoder()
    const data = encoder.encode(normalized)
    return btoa(String.fromCharCode(...new Uint8Array(data)))
  }

  /**
   * Check cache with metrics tracking
   */
  static async checkCache(
    salonId: string, 
    task: string, 
    inputHash: string
  ): Promise<CacheResult> {
    try {
      const { data, error } = await this.supabase
        .from('ai_analysis_cache')
        .select('result, tokens_used, cost_usd, model_used')
        .eq('salon_id', salonId)
        .eq('analysis_type', task)
        .eq('input_hash', inputHash)
        .gte('expires_at', new Date().toISOString())
        .single()

      if (!error && data) {
        // Cache hit - update metrics
        this.metrics.totalHits++
        const savedTokens = data.tokens_used || 0
        const savedCost = data.cost_usd || 0
        
        this.metrics.totalSavedTokens += savedTokens
        this.metrics.totalSavedUSD += savedCost
        this.metrics.avgSavingsPerHit = this.metrics.totalSavedUSD / this.metrics.totalHits

        // Update most cached queries
        this.updateMostCachedQueries(task)
        
        // Save metrics periodically
        this.saveMetricsIfNeeded()

        return {
          hit: true,
          data: data.result,
          savedTokens,
          savedCost
        }
      }

      // Cache miss
      this.metrics.totalMisses++
      this.saveMetricsIfNeeded()
      
      return { hit: false }
    } catch (error) {
      console.error('Cache check error:', error)
      return { hit: false }
    }
  }

  /**
   * Save to cache with TTL based on task type
   */
  static async saveToCache(
    salonId: string,
    task: string,
    inputHash: string,
    inputData: any,
    result: any,
    model: string,
    tokensUsed: number,
    costUsd: number
  ): Promise<void> {
    try {
      const ttl = CACHE_TTL[task as keyof typeof CACHE_TTL] || CACHE_TTL.formula
      const expiresAt = new Date(Date.now() + ttl)

      await this.supabase
        .from('ai_analysis_cache')
        .upsert({
          salon_id: salonId,
          analysis_type: task,
          input_hash: inputHash,
          input_data: inputData,
          result: result,
          model_used: model,
          tokens_used: tokensUsed,
          cost_usd: costUsd,
          prompt_version: PROMPT_VERSION,
          created_at: new Date().toISOString(),
          expires_at: expiresAt.toISOString()
        })
    } catch (error) {
      console.error('Cache save error:', error)
    }
  }

  /**
   * Calculate cost savings
   */
  static calculateSavings(tokensUsed: number, model: string): number {
    const pricing = MODEL_PRICING[model as keyof typeof MODEL_PRICING] || MODEL_PRICING['gpt-4o']
    // Assume 70/30 split for input/output tokens (typical for our use case)
    const inputTokens = Math.floor(tokensUsed * 0.7)
    const outputTokens = tokensUsed - inputTokens
    
    const inputCost = (inputTokens / 1_000_000) * pricing.input
    const outputCost = (outputTokens / 1_000_000) * pricing.output
    
    return inputCost + outputCost
  }

  /**
   * Get current metrics
   */
  static getMetrics(): CacheMetrics {
    const hitRate = this.metrics.totalHits + this.metrics.totalMisses > 0
      ? (this.metrics.totalHits / (this.metrics.totalHits + this.metrics.totalMisses)) * 100
      : 0

    return {
      ...this.metrics,
      hitRate: Math.round(hitRate * 10) / 10 // Round to 1 decimal
    } as CacheMetrics & { hitRate: number }
  }

  /**
   * Clear old cache entries
   */
  static async clearExpiredCache(salonId?: string): Promise<number> {
    try {
      let query = this.supabase
        .from('ai_analysis_cache')
        .delete()
        .lt('expires_at', new Date().toISOString())
      
      if (salonId) {
        query = query.eq('salon_id', salonId)
      }

      const { data, error } = await query.select('id')
      
      if (error) throw error
      
      return data?.length || 0
    } catch (error) {
      console.error('Clear cache error:', error)
      return 0
    }
  }

  /**
   * Update most cached queries tracking
   */
  private static updateMostCachedQueries(task: string) {
    const existing = this.metrics.mostCachedQueries.find(q => q.query === task)
    
    if (existing) {
      existing.hits++
      existing.lastHit = new Date().toISOString()
    } else {
      this.metrics.mostCachedQueries.push({
        query: task,
        hits: 1,
        lastHit: new Date().toISOString()
      })
    }

    // Keep only top 10 most cached
    this.metrics.mostCachedQueries.sort((a, b) => b.hits - a.hits)
    this.metrics.mostCachedQueries = this.metrics.mostCachedQueries.slice(0, 10)
  }

  /**
   * Load metrics from database
   */
  private static async loadMetrics() {
    try {
      const { data } = await this.supabase
        .from('cache_metrics')
        .select('*')
        .single()
      
      if (data) {
        this.metrics = data.metrics
      }
    } catch (error) {
      // Metrics table might not exist or be empty - that's ok
      console.log('No existing metrics found, starting fresh')
    }
  }

  /**
   * Save metrics if needed
   */
  private static async saveMetricsIfNeeded() {
    const now = Date.now()
    if (now - this.metricsLastSaved < this.METRICS_SAVE_INTERVAL) {
      return
    }

    try {
      await this.supabase
        .from('cache_metrics')
        .upsert({
          id: 1, // Single row for global metrics
          metrics: this.metrics,
          updated_at: new Date().toISOString()
        })
      
      this.metricsLastSaved = now
    } catch (error) {
      console.error('Save metrics error:', error)
    }
  }

  /**
   * Get cache statistics for a specific salon
   */
  static async getSalonCacheStats(salonId: string): Promise<{
    totalEntries: number
    totalSizeMB: number
    oldestEntry: string | null
    newestEntry: string | null
  }> {
    try {
      const { data, error } = await this.supabase
        .from('ai_analysis_cache')
        .select('created_at, input_data, result')
        .eq('salon_id', salonId)
        .order('created_at', { ascending: true })

      if (error) throw error

      const totalEntries = data?.length || 0
      const totalSizeBytes = data?.reduce((sum, entry) => {
        const size = JSON.stringify(entry.input_data).length + 
                     JSON.stringify(entry.result).length
        return sum + size
      }, 0) || 0

      return {
        totalEntries,
        totalSizeMB: Math.round((totalSizeBytes / 1024 / 1024) * 100) / 100,
        oldestEntry: data?.[0]?.created_at || null,
        newestEntry: data?.[data.length - 1]?.created_at || null
      }
    } catch (error) {
      console.error('Get salon cache stats error:', error)
      return {
        totalEntries: 0,
        totalSizeMB: 0,
        oldestEntry: null,
        newestEntry: null
      }
    }
  }
}
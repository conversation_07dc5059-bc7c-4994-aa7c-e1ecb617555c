/**
 * Response Validator for AI responses
 */

import { REQUIRED_FIELDS } from '../constants.ts'

/**
 * Validates AI response has required fields
 */
export function validateAIResponse(
  taskType: string,
  response: any
): { isValid: boolean; missingFields: string[] } {
  const requiredFields = REQUIRED_FIELDS[taskType as keyof typeof REQUIRED_FIELDS] || []
  const missingFields: string[] = []

  for (const field of requiredFields) {
    if (!hasNestedProperty(response, field)) {
      missingFields.push(field)
    }
  }

  return {
    isValid: missingFields.length === 0,
    missingFields
  }
}

/**
 * Check if object has nested property (supports dot notation)
 */
function hasNestedProperty(obj: any, path: string): boolean {
  const parts = path.split('.')
  let current = obj

  for (const part of parts) {
    if (current === null || current === undefined || !(part in current)) {
      return false
    }
    current = current[part]
  }

  return true
}

/**
 * Ensures response maintains backward compatibility
 */
export function ensureBackwardCompatibility(
  taskType: string,
  response: any
): any {
  // Ensure all expected fields exist, even if null
  switch (taskType) {
    case 'diagnose_image':
      return {
        hairThickness: response.hairThickness || 'Medio',
        hairDensity: response.hairDensity || 'Media',
        overallTone: response.overallTone || '',
        overallUndertone: response.overallUndertone || 'Neutro',
        averageDepthLevel: response.averageDepthLevel || 5,
        zoneAnalysis: ensureZoneAnalysis(response.zoneAnalysis),
        detectedChemicalProcess: response.detectedChemicalProcess || null,
        estimatedLastProcessDate: response.estimatedLastProcessDate || null,
        detectedRisks: response.detectedRisks || {
          metallic: false,
          henna: false,
          damaged: false,
          overProcessed: false,
          incompatibleProducts: false
        },
        serviceComplexity: response.serviceComplexity || 'medium',
        estimatedTime: response.estimatedTime || 120,
        overallCondition: response.overallCondition || '',
        recommendations: response.recommendations || [],
        overallConfidence: response.overallConfidence || 80,
        ...response // Include any additional fields
      }

    case 'analyze_desired_look':
      return {
        detectedLevel: response.detectedLevel || 7,
        detectedTone: response.detectedTone || '',
        detectedTechnique: response.detectedTechnique || 'full_color',
        detectedTones: response.detectedTones || [],
        viabilityScore: response.viabilityScore || 50,
        estimatedSessions: response.estimatedSessions || 1,
        requiredProcesses: response.requiredProcesses || [],
        confidence: response.confidence || 80,
        warnings: response.warnings || [],
        ...response
      }

    default:
      return response
  }
}

/**
 * Ensure zone analysis has all required fields
 */
function ensureZoneAnalysis(zoneAnalysis: any): any {
  const defaultZone = {
    depth: 5,
    tone: '',
    undertone: 'Neutro',
    percentage: 33,
    state: 'Natural',
    unwantedTone: null,
    grayPercentage: 0,
    grayType: null,
    grayPattern: null,
    cuticleState: 'Cerrada',
    damage: 'Ninguno',
    elasticity: 'Buena',
    porosity: 'Media',
    resistance: 'Media'
  }

  return {
    roots: { ...defaultZone, ...zoneAnalysis?.roots },
    mids: { ...defaultZone, ...zoneAnalysis?.mids },
    ends: { ...defaultZone, ...zoneAnalysis?.ends }
  }
}

/**
 * Sanitize response to remove any sensitive data
 */
export function sanitizeResponse(response: any): any {
  // Remove any fields that might contain sensitive data
  const sanitized = { ...response }
  
  // Remove internal fields
  delete sanitized._raw
  delete sanitized._debug
  delete sanitized._internal
  
  return sanitized
}
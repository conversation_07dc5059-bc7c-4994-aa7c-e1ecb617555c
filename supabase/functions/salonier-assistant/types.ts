/**
 * Type definitions for Salonier Assistant Edge Function
 */

// Request/Response types
export interface AIRequest {
  task: 'diagnose_image' | 'analyze_desired_look' | 'generate_formula' | 'convert_formula' | 'parse_product_text' | 'analyze_product'
  payload: Record<string, any>
}

export interface AIResponse {
  success: boolean
  data?: any
  error?: string
  cached?: boolean
  metrics?: {
    tokensUsed?: number
    cost?: number
    cacheHit?: boolean
  }
}

// Cache types
export interface CacheResult {
  hit: boolean
  data?: any
  savedTokens?: number
  savedCost?: number
}

export interface CacheMetrics {
  totalHits: number
  totalMisses: number
  avgSavingsPerHit: number
  mostCachedQueries: Array<{
    query: string
    hits: number
    lastHit: string
  }>
  totalSavedUSD: number
  totalSavedTokens: number
}

// Template types
export type TemplateType = 'full' | 'optimized' | 'minimal'

export interface TemplateContext {
  hasHistory: boolean
  imageQuality: 'high' | 'medium' | 'low'
  userTier: 'free' | 'pro' | 'enterprise'
}

// Configuration types
export interface RegionalConfig {
  volumeUnit: string
  weightUnit: string
  developerTerminology: string
  colorTerminology: string
  maxDeveloperVolume: number
  currencySymbol: string
  measurementSystem: 'metric' | 'imperial'
  decimalSeparator: string
  thousandsSeparator: string
  language: 'es' | 'en'
}

export interface FormulaConfig {
  diagnosis: any
  desiredResult: any
  brand: string
  line: string
  clientHistory?: string
  regionalConfig: RegionalConfig
  selectedTechnique: string
  customTechnique?: string
}

// OpenAI types
export interface OpenAIRequestOptions {
  model: 'gpt-4o' | 'gpt-4o-mini' | 'gpt-3.5-turbo'
  maxTokens: number
  temperature: number
  responseFormat?: { type: 'json_object' | 'text' }
  imageUrl?: string
}

export interface OpenAIResponse {
  choices: Array<{
    message: {
      content: string
    }
  }>
  usage: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
}
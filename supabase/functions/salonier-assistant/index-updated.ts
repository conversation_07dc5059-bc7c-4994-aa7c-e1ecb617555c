import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
  'Access-Control-Max-Age': '86400',
}

interface AIRequest {
  task: 'diagnose_image' | 'analyze_desired_look' | 'generate_formula' | 'convert_formula' | 'parse_product_text' | 'analyze_product'
  payload: Record<string, any>
}

interface AIResponse {
  success: boolean
  data?: any
  error?: string
  cached?: boolean
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
const openaiApiKey = Deno.env.get('OPENAI_API_KEY')

// Log initialization status (without revealing secrets)
console.log('Edge Function initialization:', {
  hasSupabaseUrl: !!supabaseUrl,
  hasSupabaseKey: !!supabaseServiceKey,
  hasOpenAIKey: !!openaiApiKey,
  openAIKeyPrefix: openaiApiKey ? openaiApiKey.substring(0, 7) + '...' : 'NOT SET'
})

const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Helper to generate cache key
function generateCacheKey(task: string, payload: any): string {
  const normalized = JSON.stringify(payload, Object.keys(payload).sort())
  const encoder = new TextEncoder()
  const data = encoder.encode(`${task}:${normalized}`)
  return btoa(String.fromCharCode(...new Uint8Array(data)))
}

// Helper to check cache
async function checkCache(salonId: string, task: string, inputHash: string): Promise<any | null> {
  const { data, error } = await supabase
    .from('ai_analysis_cache')
    .select('result')
    .eq('salon_id', salonId)
    .eq('analysis_type', task)
    .eq('input_hash', inputHash)
    .gte('expires_at', new Date().toISOString())
    .single()

  if (!error && data) {
    return data.result
  }
  return null
}

// Helper to save to cache
async function saveToCache(
  salonId: string, 
  task: string, 
  inputHash: string, 
  inputData: any,
  result: any,
  model: string,
  tokensUsed: number,
  costUsd: number
) {
  await supabase
    .from('ai_analysis_cache')
    .upsert({
      salon_id: salonId,
      analysis_type: task,
      input_hash: inputHash,
      input_data: inputData,
      result: result,
      model_used: model,
      tokens_used: tokensUsed,
      cost_usd: costUsd,
      created_at: new Date().toISOString(),
      expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
    })
}

// Helper for retry with exponential backoff
async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  initialDelay: number = 1000
): Promise<T> {
  let lastError: any
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn()
    } catch (error: any) {
      lastError = error
      
      // Check if it's a rate limit error
      if (error.status === 429 || error.message?.includes('rate limit')) {
        const delay = initialDelay * Math.pow(2, i)
        console.log(`Rate limited. Retrying in ${delay}ms...`)
        await new Promise(resolve => setTimeout(resolve, delay))
      } else {
        // If it's not a rate limit error, throw immediately
        throw error
      }
    }
  }
  
  throw lastError
}

// Updated pricing as of 2025
const MODEL_PRICING = {
  'gpt-4o': { input: 2.50, output: 10.00 }, // per 1M tokens
  'gpt-4o-mini': { input: 0.15, output: 0.60 }, // per 1M tokens
  'gpt-3.5-turbo': { input: 0.50, output: 1.50 }, // per 1M tokens
}

function calculateCost(model: string, inputTokens: number, outputTokens: number): number {
  const pricing = MODEL_PRICING[model as keyof typeof MODEL_PRICING] || MODEL_PRICING['gpt-4o']
  const inputCost = (inputTokens / 1_000_000) * pricing.input
  const outputCost = (outputTokens / 1_000_000) * pricing.output
  return inputCost + outputCost
}

// Task handlers
async function diagnoseImage(payload: any, salonId: string): Promise<AIResponse> {
  const { imageUrl, imageBase64, diagnosis } = payload
  
  // Soportar ambos formatos para compatibilidad
  let imageDataUrl: string
  if (imageBase64) {
    // Validar y limpiar base64
    const cleanBase64 = imageBase64.replace(/\s/g, '').replace(/\n/g, '')
    
    // Verificar tamaño del base64
    const base64SizeInBytes = (cleanBase64.length * 3) / 4
    console.log(`Base64 size: ${base64SizeInBytes} bytes (${(base64SizeInBytes / 1024).toFixed(2)} KB)`)
    
    // Limitar tamaño máximo a ~4MB después de decodificar
    if (base64SizeInBytes > 4 * 1024 * 1024) {
      throw new Error('Image too large. Maximum size is 4MB')
    }
    
    // Validar que sea base64 válido
    try {
      // Verificar que solo tenga caracteres válidos de base64
      if (!/^[A-Za-z0-9+/]*={0,2}$/.test(cleanBase64)) {
        throw new Error('Invalid base64 format')
      }
      imageDataUrl = `data:image/jpeg;base64,${cleanBase64}`
    } catch (e) {
      console.error('Invalid base64:', e)
      throw new Error('Invalid image format')
    }
  } else if (imageUrl) {
    imageDataUrl = imageUrl
  } else {
    throw new Error('No image provided')
  }
  
  const prompt = `Eres un experto colorista profesional analizando el cabello de un cliente.
  
  Analiza la imagen proporcionada y devuelve un análisis COMPLETO en formato JSON con EXACTAMENTE esta estructura:
  {
    "hairThickness": "Fino|Medio|Grueso",
    "hairDensity": "Baja|Media|Alta",
    "overallTone": "nombre del tono general",
    "overallReflect": "Frío|Cálido|Neutro",
    "averageLevel": número decimal (1-10),
    "zoneAnalysis": {
      "roots": {
        "level": número decimal (1-10),
        "tone": "tono específico",
        "reflect": "Frío|Cálido|Neutro",
        "percentage": número (0-100),
        "state": "Natural|Procesado|Decolorado|Teñido",
        "unwantedTone": "Verde|Naranja|Amarillo|Rojo|Violeta|null",
        "grayPercentage": número (0-100),
        "grayType": "Blanco|Gris|Mixto|null",
        "grayPattern": "Uniforme|Localizado|Disperso|null",
        "cuticleState": "Cerrada|Abierta|Dañada",
        "damage": "Ninguno|Leve|Moderado|Severo",
        "elasticity": "Buena|Regular|Mala",
        "porosity": "Baja|Media|Alta",
        "resistance": "Fuerte|Media|Débil"
      },
      "mids": {
        "level": número decimal (1-10),
        "tone": "tono específico",
        "reflect": "Frío|Cálido|Neutro",
        "percentage": número (0-100),
        "state": "Natural|Procesado|Decolorado|Teñido",
        "unwantedTone": "Verde|Naranja|Amarillo|Rojo|Violeta|null",
        "demarkationBands": [{"location": número (1-10), "contrast": "Suave|Moderado|Fuerte"}] o [],
        "pigmentAccumulation": "Ninguna|Leve|Moderada|Severa",
        "cuticleState": "Cerrada|Abierta|Dañada",
        "damage": "Ninguno|Leve|Moderado|Severo",
        "elasticity": "Buena|Regular|Mala",
        "porosity": "Baja|Media|Alta",
        "resistance": "Fuerte|Media|Débil"
      },
      "ends": {
        "level": número decimal (1-10),
        "tone": "tono específico",
        "reflect": "Frío|Cálido|Neutro",
        "percentage": número (0-100),
        "state": "Natural|Procesado|Decolorado|Teñido",
        "unwantedTone": "Verde|Naranja|Amarillo|Rojo|Violeta|null",
        "pigmentAccumulation": "Ninguna|Leve|Moderada|Severa",
        "cuticleState": "Cerrada|Abierta|Dañada",
        "damage": "Ninguno|Leve|Moderado|Severo",
        "elasticity": "Buena|Regular|Mala",
        "porosity": "Baja|Media|Alta",
        "resistance": "Fuerte|Media|Débil"
      }
    },
    "detectedChemicalProcess": "Coloración|Decoloración|Permanente|Alisado|Ninguno|null",
    "estimatedLastProcessDate": "texto descriptivo",
    "detectedRisks": { 
      "metallic": boolean, 
      "henna": boolean, 
      "damaged": boolean,
      "overProcessed": boolean,
      "incompatibleProducts": boolean
    },
    "serviceComplexity": "simple|medium|complex",
    "estimatedTime": minutos estimados,
    "overallCondition": "descripción detallada",
    "recommendations": ["lista de al menos 3 recomendaciones específicas"],
    "overallConfidence": porcentaje (0-100)
  }
  
  IMPORTANTE:
  - Analiza CADA zona por separado con TODOS los campos
  - Si no hay canas, usa grayPercentage: 0 y los campos gray como null
  - Si no hay tonos no deseados, usa unwantedTone: null
  - Si no hay bandas de demarcación, usa demarkationBands: []
  - Sé MUY preciso con los niveles decimales (ej: 6.7, 7.3)
  - Evalúa el estado físico de cada zona independientemente`

  try {
    console.log('Preparing OpenAI request...')
    console.log('Image data URL length:', imageDataUrl?.length || 0)
    console.log('Image data URL prefix:', imageDataUrl?.substring(0, 50) || 'undefined')
    
    // Validar que imageDataUrl existe y tiene el formato correcto
    if (!imageDataUrl) {
      throw new Error('Image data URL is undefined')
    }
    
    if (!imageDataUrl.startsWith('data:image/')) {
      throw new Error(`Invalid image data URL format. Starts with: ${imageDataUrl.substring(0, 30)}`)
    }
    
    // Construir el mensaje de manera segura
    const messageContent = [
      { type: 'text', text: prompt },
      { type: 'image_url', image_url: { url: imageDataUrl } }
    ]
    
    console.log('Message content structure:', JSON.stringify(messageContent[1], null, 2))
    
    const requestBody = {
      model: 'gpt-4o',
      messages: [
        {
          role: 'user',
          content: messageContent
        }
      ],
      max_tokens: 1500,
      temperature: 0.3,
      response_format: { type: "json_object" }
    }
    
    console.log('Sending request to OpenAI...')
    console.log('OpenAI API key status:', openaiApiKey ? 'Present' : 'Missing')
    
    if (!openaiApiKey) {
      throw new Error('OpenAI API key is not configured')
    }
    
    const response = await retryWithBackoff(async () => {
      const headers = {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      }
      console.log('Request headers prepared, auth header length:', headers.Authorization.length)
      
      return await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody)
      })
    })

    // Check HTTP status first
    if (!response.ok) {
      console.error('OpenAI API request failed:', {
        status: response.status,
        statusText: response.statusText
      })
      
      const errorData = await response.json().catch(() => ({ error: 'Failed to parse error response' }))
      console.error('Error response:', errorData)
      
      if (response.status === 401) {
        throw new Error('OpenAI API key is invalid or missing')
      } else if (response.status === 429) {
        throw new Error('OpenAI API rate limit exceeded')
      } else if (response.status === 400) {
        throw new Error(`OpenAI API bad request: ${errorData.error?.message || 'Invalid request format'}`)
      } else {
        throw new Error(`OpenAI API error: ${response.status} ${errorData.error?.message || response.statusText}`)
      }
    }

    const data = await response.json()
    console.log('OpenAI response received, has choices:', !!data.choices)
    
    if (data.error) {
      console.error('OpenAI API error in response body:', data.error)
      console.error('Request body image_url was:', messageContent[1])
      throw new Error(data.error.message || 'OpenAI API error')
    }

    // Validate response structure
    if (!data.choices || !data.choices[0] || !data.choices[0].message || !data.choices[0].message.content) {
      console.error('Invalid OpenAI response structure:', JSON.stringify(data))
      throw new Error('Invalid response from OpenAI')
    }

    // Parse AI response with error handling
    let result
    try {
      result = JSON.parse(data.choices[0].message.content)
      console.log('Successfully parsed AI response')
    } catch (parseError) {
      console.error('Failed to parse AI response:', parseError)
      console.error('Raw AI response:', data.choices[0].message.content)
      throw new Error('Failed to parse AI response - invalid JSON')
    }

    // Validate required fields in result
    if (!result || typeof result !== 'object') {
      console.error('AI response is not an object:', result)
      throw new Error('AI response is not a valid object')
    }

    // Mapeo de compatibilidad para términos antiguos
    if (result) {
      // Mapear términos generales si vienen con nombres antiguos
      if (result.overallUndertone && !result.overallReflect) {
        result.overallReflect = result.overallUndertone;
        delete result.overallUndertone;
      }
      if (result.averageDepthLevel && !result.averageLevel) {
        result.averageLevel = result.averageDepthLevel;
        delete result.averageDepthLevel;
      }
      
      // Mapear términos en análisis por zonas
      if (result.zoneAnalysis) {
        ['roots', 'mids', 'ends'].forEach(zone => {
          if (result.zoneAnalysis[zone]) {
            // Mapear depth a level
            if (result.zoneAnalysis[zone].depth && !result.zoneAnalysis[zone].level) {
              result.zoneAnalysis[zone].level = result.zoneAnalysis[zone].depth;
              delete result.zoneAnalysis[zone].depth;
            }
            // Mapear undertone a reflect
            if (result.zoneAnalysis[zone].undertone && !result.zoneAnalysis[zone].reflect) {
              result.zoneAnalysis[zone].reflect = result.zoneAnalysis[zone].undertone;
              delete result.zoneAnalysis[zone].undertone;
            }
          }
        });
      }
    }

    // Log successful analysis
    console.log('AI analysis completed successfully:', {
      hasHairThickness: !!result.hairThickness,
      hasHairDensity: !!result.hairDensity,
      hasZoneAnalysis: !!result.zoneAnalysis,
      zoneCount: result.zoneAnalysis ? Object.keys(result.zoneAnalysis).length : 0
    })

    const inputTokens = data.usage?.prompt_tokens || 0
    const outputTokens = data.usage?.completion_tokens || 0
    const totalTokens = data.usage?.total_tokens || 0
    const costUsd = calculateCost('gpt-4o', inputTokens, outputTokens)

    // Save to cache (usar solo una pequeña parte del base64 para el hash si existe)
    const cachePayload = imageBase64 
      ? { imageHash: imageBase64.substring(0, 100) } 
      : { imageUrl }
    const inputHash = generateCacheKey('diagnose_image', cachePayload)
    await saveToCache(salonId, 'diagnose_image', inputHash, payload, result, 'gpt-4o', totalTokens, costUsd)

    return { success: true, data: result }
  } catch (error: any) {
    console.error('Error in diagnoseImage:', error)
    return { success: false, error: error.message }
  }
}

async function analyzeDesiredLook(payload: any, salonId: string): Promise<AIResponse> {
  const { imageUrl, imageBase64, currentLevel, diagnosis } = payload
  
  // Mantener compatibilidad hacia atrás: si no hay diagnosis, usar currentLevel
  const hairContext = diagnosis || { averageLevel: currentLevel || 6 }
  
  // Soportar ambos formatos para compatibilidad
  let imageDataUrl: string
  if (imageBase64) {
    // Validar y limpiar base64
    const cleanBase64 = imageBase64.replace(/\s/g, '').replace(/\n/g, '')
    
    // Verificar tamaño del base64
    const base64SizeInBytes = (cleanBase64.length * 3) / 4
    console.log(`Base64 size for desired look: ${base64SizeInBytes} bytes (${(base64SizeInBytes / 1024).toFixed(2)} KB)`)
    
    // Limitar tamaño máximo
    if (base64SizeInBytes > 4 * 1024 * 1024) {
      throw new Error('Image too large. Maximum size is 4MB')
    }
    
    // Validar formato
    if (!/^[A-Za-z0-9+/]*={0,2}$/.test(cleanBase64)) {
      throw new Error('Invalid base64 format')
    }
    
    imageDataUrl = `data:image/jpeg;base64,${cleanBase64}`
  } else if (imageUrl) {
    imageDataUrl = imageUrl
  } else {
    throw new Error('No image provided')
  }
  
  // Construir prompt mejorado con contexto completo del diagnóstico
  const prompt = diagnosis 
    ? `Eres un colorista experto evaluando la viabilidad de un look deseado.
  
  DIAGNÓSTICO COMPLETO DEL CABELLO ACTUAL:
  ${JSON.stringify(diagnosis, null, 2)}
  
  Basándote en TODAS las propiedades del cabello actual (grosor, densidad, porosidad, elasticidad, daño, canas, etc.), 
  analiza la imagen de referencia y proporciona un análisis profesional de viabilidad.
  
  Tu análisis debe considerar especialmente:
  - Si el cabello fino/dañado puede soportar el proceso
  - Si la porosidad alta afectará el resultado
  - Si el % de canas requiere consideraciones especiales
  - Número realista de sesiones basado en el estado físico actual
  - Procesos químicos seguros para el estado actual del cabello
  
  Proporciona un análisis en formato JSON:
  {
    "detectedLevel": número decimal del nivel objetivo,
    "detectedTone": "tono principal detectado",
    "detectedTechnique": "técnica de aplicación detectada",
    "detectedTones": ["lista de tonos presentes"],
    "viabilityScore": 0-100,
    "estimatedSessions": número de sesiones necesarias,
    "requiredProcesses": ["procesos necesarios"],
    "confidence": porcentaje de confianza,
    "warnings": ["advertencias si las hay"]
  }`
    : `Analiza esta imagen de referencia de color de cabello deseado.
  
  Considerando que el nivel actual del cliente es ${currentLevel}, proporciona un análisis en formato JSON:
  {
    "detectedLevel": número decimal del nivel objetivo,
    "detectedTone": "tono principal detectado",
    "detectedTechnique": "técnica de aplicación detectada",
    "detectedTones": ["lista de tonos presentes"],
    "viabilityScore": 0-100,
    "estimatedSessions": número de sesiones necesarias,
    "requiredProcesses": ["procesos necesarios"],
    "confidence": porcentaje de confianza,
    "warnings": ["advertencias si las hay"]
  }`

  try {
    console.log('Preparing OpenAI request for desired look...')
    console.log('Image data URL length:', imageDataUrl?.length || 0)
    console.log('Image data URL prefix:', imageDataUrl?.substring(0, 50) || 'undefined')
    
    if (!imageDataUrl) {
      throw new Error('Image data URL is undefined')
    }
    
    if (!imageDataUrl.startsWith('data:image/')) {
      throw new Error(`Invalid image data URL format. Starts with: ${imageDataUrl.substring(0, 30)}`)
    }
    
    // Validar que la imagen base64 no esté vacía
    if (!imageDataUrl || imageDataUrl === 'data:image/jpeg;base64,') {
      console.error('Empty image data URL for desired look analysis');
      throw new Error('La imagen está vacía o corrupta');
    }
    
    // Log del tamaño real del base64
    const base64Content = imageDataUrl.split(',')[1];
    if (base64Content) {
      console.log('Base64 content length for desired look:', base64Content.length);
      console.log('First 100 chars of base64:', base64Content.substring(0, 100));
    }
    
    const messageContent = [
      { type: 'text', text: prompt },
      { type: 'image_url', image_url: { url: imageDataUrl } }
    ]
    
    const requestBody = {
      model: 'gpt-4o',
      messages: [
        {
          role: 'user',
          content: messageContent
        }
      ],
      max_tokens: 800,
      temperature: 0.3,
      response_format: { type: "json_object" }
    }
    
    const response = await retryWithBackoff(async () => {
      return await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${openaiApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      })
    })

    // Check HTTP status first
    if (!response.ok) {
      console.error('OpenAI API request failed for desired look:', {
        status: response.status,
        statusText: response.statusText
      })
      
      const errorData = await response.json().catch(() => ({ error: 'Failed to parse error response' }))
      console.error('Error response:', errorData)
      
      if (response.status === 401) {
        throw new Error('OpenAI API key is invalid or missing')
      } else if (response.status === 429) {
        throw new Error('OpenAI API rate limit exceeded')
      } else if (response.status === 400) {
        throw new Error(`OpenAI API bad request: ${errorData.error?.message || 'Invalid request format'}`)
      } else {
        throw new Error(`OpenAI API error: ${response.status} ${errorData.error?.message || response.statusText}`)
      }
    }

    const data = await response.json()
    console.log('OpenAI response received for desired look, has choices:', !!data.choices)
    
    if (data.error) {
      console.error('OpenAI error for desired look:', {
        error: data.error,
        imageDataUrlLength: imageDataUrl?.length,
        hasBase64Content: imageDataUrl?.includes('base64,')
      });
      throw new Error(data.error.message)
    }

    // Log para debugging
    console.log('Raw AI response for desired look:', data.choices[0].message.content);
    const result = JSON.parse(data.choices[0].message.content);
    console.log('Parsed desired look analysis:', {
      detectedLevel: result.detectedLevel,
      detectedTone: result.detectedTone,
      imageAnalysisNotes: result.imageAnalysisNotes
    });
    
    const inputTokens = data.usage.prompt_tokens
    const outputTokens = data.usage.completion_tokens
    const totalTokens = data.usage.total_tokens
    const costUsd = calculateCost('gpt-4o', inputTokens, outputTokens)

    // Save to cache
    const cachePayload = imageBase64 
      ? { imageHash: imageBase64.substring(0, 100), hairContext } 
      : { imageUrl, hairContext }
    const inputHash = generateCacheKey('analyze_desired_look', cachePayload)
    await saveToCache(salonId, 'analyze_desired_look', inputHash, payload, result, 'gpt-4o', totalTokens, costUsd)
    
    return { success: true, data: result }
  } catch (error: any) {
    console.error('Error in analyzeDesiredLook:', error)
    return { success: false, error: error.message }
  }
}

// El resto del código permanece igual...
async function generateFormula(payload: any, salonId: string): Promise<AIResponse> {
  const { diagnosis, desiredResult, brand, line, clientHistory, regionalConfig } = payload
  
  // Determinar configuración regional
  const volumeUnit = regionalConfig?.volumeUnit || 'ml'
  const weightUnit = regionalConfig?.weightUnit || 'g'
  const developerTerm = regionalConfig?.developerTerminology || 'oxidante'
  const colorTerm = regionalConfig?.colorTerminology || 'tinte'
  const maxDeveloperVolume = regionalConfig?.maxDeveloperVolume || 40
  const currencySymbol = regionalConfig?.currencySymbol || '€'
  const measurementSystem = regionalConfig?.measurementSystem || 'metric'
  const decimalSeparator = regionalConfig?.decimalSeparator || ','
  
  // Detectar idioma
  const isEnglish = regionalConfig?.language === 'en'
  
  // Extraer técnica seleccionada
  const selectedTechnique = desiredResult?.general?.technique || 'full_color'
  const customTechnique = desiredResult?.general?.customTechnique
  
  // Obtener instrucciones específicas por técnica
  const getTechniqueInstructions = () => {
    if (selectedTechnique === 'custom' && customTechnique) {
      return isEnglish 
        ? `Custom technique described as: "${customTechnique}". Adapt the formula accordingly.`
        : `Técnica personalizada descrita como: "${customTechnique}". Adapta la fórmula según corresponda.`
    }
    
    const techniquePrompts = {
      full_color: isEnglish
        ? `- Single formula for complete coverage
- Ensure uniform application from roots to ends
- Consider natural regrowth for maintenance`
        : `- Fórmula única para cobertura completa
- Asegurar aplicación uniforme de raíces a puntas
- Considerar crecimiento natural para mantenimiento`,
      
      highlights: isEnglish
        ? `- Use foil technique for precision
- Create multiple formulas if needed (base + highlights)
- Thicker consistency to prevent bleeding
- Maximum ${developerTerm} 30 vol for highlights
- Consider placement pattern (full head, partial, face-framing)`
        : `- Usar técnica con papel aluminio para precisión
- Crear múltiples fórmulas si es necesario (base + mechas)
- Consistencia más espesa para evitar sangrado
- Máximo ${developerTerm} 30 vol para mechas
- Considerar patrón de colocación (cabeza completa, parcial, contorno facial)`,
      
      balayage: isEnglish
        ? `- Free-hand painting technique
- Gradual transition from dark to light
- Use lower ${developerTerm} volume (20 vol max recommended)
- Creamy consistency for controlled application
- Natural, sun-kissed effect
- Consider using clay or cream lightener`
        : `- Técnica de pintado a mano alzada
- Transición gradual de oscuro a claro
- Usar ${developerTerm} de menor volumen (20 vol máximo recomendado)
- Consistencia cremosa para aplicación controlada
- Efecto natural, como besado por el sol
- Considerar usar decolorante en crema o arcilla`,
      
      ombre: isEnglish
        ? `- Clear horizontal gradient
- Multiple formulas for different zones
- Seamless blending is crucial
- Start application from ends, work up
- Consider toner for perfect transition`
        : `- Degradado horizontal claro
- Múltiples fórmulas para diferentes zonas
- La mezcla perfecta es crucial
- Comenzar aplicación desde puntas, subir gradualmente
- Considerar toner para transición perfecta`,
      
      babylights: isEnglish
        ? `- Ultra-fine sections (max 1-2mm)
- Low ${developerTerm} volume (10-20 vol)
- Natural, subtle effect
- Longer processing time due to fine sections
- Mimic natural sun-lightened strands`
        : `- Secciones ultrafinas (máx 1-2mm)
- ${developerTerm} de bajo volumen (10-20 vol)
- Efecto natural y sutil
- Mayor tiempo de procesamiento por secciones finas
- Imitar mechones aclarados naturalmente por el sol`,
      
      color_correction: isEnglish
        ? `- Analyze underlying pigments carefully
- May need pre-pigmentation or color removal
- Multiple steps might be required
- Use appropriate neutralizing tones
- Consider strand test mandatory
- Document each step for future reference`
        : `- Analizar cuidadosamente pigmentos subyacentes
- Puede necesitar pre-pigmentación o remoción de color
- Pueden requerirse múltiples pasos
- Usar tonos neutralizantes apropiados
- Considerar prueba de mechón obligatoria
- Documentar cada paso para referencia futura`,
      
      foilyage: isEnglish
        ? `- Combine foil and balayage techniques
- Use foil for stronger lift at top sections
- Free-hand painting for natural flow
- Varying ${developerTerm} volumes by section
- Creates maximum dimension`
        : `- Combinar técnicas de aluminio y balayage
- Usar aluminio para mayor aclarado en secciones superiores
- Pintado a mano para flujo natural
- Variar volúmenes de ${developerTerm} por sección
- Crea máxima dimensión`,
      
      money_piece: isEnglish
        ? `- Focus on face-framing sections
- High contrast for impact
- Protect surrounding hair
- Consider client's skin tone
- Easy maintenance placement`
        : `- Enfoque en secciones que enmarcan el rostro
- Alto contraste para impacto
- Proteger cabello circundante
- Considerar tono de piel del cliente
- Colocación de fácil mantenimiento`,
      
      chunky_highlights: isEnglish
        ? `- Thick sections (1cm or more)
- Bold contrast recommended
- Strategic placement for maximum effect
- Higher ${developerTerm} volume acceptable (up to 40 vol)
- 90s-inspired dramatic look`
        : `- Secciones gruesas (1cm o más)
- Contraste audaz recomendado
- Colocación estratégica para máximo efecto
- ${developerTerm} de mayor volumen aceptable (hasta 40 vol)
- Look dramático inspirado en los 90s`,
      
      reverse_balayage: isEnglish
        ? `- Add depth to over-lightened hair
- Use demi-permanent or semi-permanent color
- Focus on roots and mid-lengths
- Create natural shadow root
- Low ${developerTerm} volume or no ${developerTerm}`
        : `- Agregar profundidad a cabello sobre-aclarado
- Usar color demi-permanente o semi-permanente
- Enfoque en raíces y medios
- Crear raíz sombreada natural
- ${developerTerm} de bajo volumen o sin ${developerTerm}`
    }
    
    return techniquePrompts[selectedTechnique as keyof typeof techniquePrompts] || techniquePrompts.full_color
  }
  
  // Crear ejemplos de formato según la región
  const formatExamples = measurementSystem === 'metric' 
    ? isEnglish 
      ? `- Quantities: "40${volumeUnit} of ${colorTerm} 7.1", "60${volumeUnit} of ${developerTerm} 20 vol"
  - Ratios: "1:1${decimalSeparator}5" or "1:2"
  - Weights: "15${weightUnit} of lightening powder"`
      : `- Cantidades: "40${volumeUnit} de ${colorTerm} 7.1", "60${volumeUnit} de ${developerTerm} 20 vol"
  - Proporciones: "1:1${decimalSeparator}5" o "1:2"
  - Pesos: "15${weightUnit} de polvo decolorante"`
    : `- Quantities: "1.35${volumeUnit} of ${colorTerm} 7.1", "2${volumeUnit} of ${developerTerm} 20 vol"
  - Ratios: "1:1.5" or "1:2"
  - Weights: "0.5${weightUnit} of lightening powder"`
  
  // Adaptar restricciones según región
  const volumeRestriction = maxDeveloperVolume < 40 
    ? isEnglish
      ? `IMPORTANT: In this region, the maximum allowed ${developerTerm} volume is ${maxDeveloperVolume} volumes.`
      : `IMPORTANTE: En esta región, el volumen máximo permitido de ${developerTerm} es ${maxDeveloperVolume} volúmenes.`
    : ''
  
  const techniqueInstructions = getTechniqueInstructions()
  const techniqueName = selectedTechnique === 'custom' && customTechnique 
    ? customTechnique 
    : {
        full_color: isEnglish ? 'Full Color' : 'Tinte Completo',
        highlights: isEnglish ? 'Highlights' : 'Mechas',
        balayage: 'Balayage',
        ombre: 'Ombré',
        babylights: 'Babylights',
        color_correction: isEnglish ? 'Color Correction' : 'Corrección de Color',
        foilyage: 'Foilyage',
        money_piece: 'Money Piece',
        chunky_highlights: isEnglish ? 'Chunky Highlights' : 'Mechas Gruesas',
        reverse_balayage: 'Reverse Balayage'
      }[selectedTechnique] || (isEnglish ? 'Full Color' : 'Tinte Completo')

  const prompt = isEnglish ? `You are a master colorist creating a professional formula for a ${techniqueName} service.

  REGIONAL CONFIGURATION:
  - Measurement system: ${measurementSystem}
  - Volume unit: ${volumeUnit}
  - Weight unit: ${weightUnit}
  - Term for developer/oxidant: ${developerTerm}
  - Term for color: ${colorTerm}
  - Decimal separator: ${decimalSeparator}
  ${volumeRestriction}

  QUANTITY FORMAT:
  ${formatExamples}

  TECHNIQUE-SPECIFIC REQUIREMENTS:
  ${techniqueInstructions}

  Current diagnosis: ${JSON.stringify(diagnosis)}
  Desired result: ${JSON.stringify(desiredResult)}
  Brand: ${brand}
  Line: ${line}
  Client history: ${clientHistory || 'First time'}

  Generate a detailed and professional formula including:
  - Preparation steps specific to ${techniqueName}
  - Specific formula(s) with exact proportions using ${volumeUnit} and ${weightUnit} units
  - For techniques requiring multiple formulas (highlights, ombre, etc.), provide all necessary formulas
  - Use the term "${developerTerm}" for developer/oxidant
  - Use the term "${colorTerm}" for hair color
  - Processing times by zone considering the specific technique
  - Detailed application technique for ${techniqueName}
  - Post-service care specific to this technique
  - If including estimated costs, use the ${currencySymbol} symbol
  
  IMPORTANT: 
  - Use EXACTLY the units and terminology specified above
  - Follow the technique-specific requirements carefully
  - Adapt formulation consistency and volumes based on the technique
  
  Format: Professional markdown with clear sections.`
  : `Eres un maestro colorista creando una fórmula profesional para un servicio de ${techniqueName}.

  CONFIGURACIÓN REGIONAL:
  - Sistema de medidas: ${measurementSystem}
  - Unidad de volumen: ${volumeUnit}
  - Unidad de peso: ${weightUnit}
  - Término para oxidante/revelador: ${developerTerm}
  - Término para coloración: ${colorTerm}
  - Separador decimal: ${decimalSeparator}
  ${volumeRestriction}

  FORMATO DE CANTIDADES:
  ${formatExamples}

  REQUISITOS ESPECÍFICOS DE LA TÉCNICA:
  ${techniqueInstructions}

  Diagnóstico actual: ${JSON.stringify(diagnosis)}
  Resultado deseado: ${JSON.stringify(desiredResult)}
  Marca: ${brand}
  Línea: ${line}
  Historial del cliente: ${clientHistory || 'Primera vez'}

  Genera una fórmula detallada y profesional que incluya:
  - Pasos de preparación específicos para ${techniqueName}
  - Fórmula(s) específica(s) con proporciones exactas usando las unidades ${volumeUnit} y ${weightUnit}
  - Para técnicas que requieren múltiples fórmulas (mechas, ombré, etc.), proporcionar todas las fórmulas necesarias
  - Usa el término "${developerTerm}" para el oxidante/revelador
  - Usa el término "${colorTerm}" para la coloración
  - Tiempos de procesamiento por zona considerando la técnica específica
  - Técnica de aplicación detallada para ${techniqueName}
  - Cuidados post-servicio específicos para esta técnica
  - Si incluyes costos estimados, usa el símbolo ${currencySymbol}
  
  IMPORTANTE: 
  - Usa EXACTAMENTE las unidades y terminología especificadas arriba
  - Sigue cuidadosamente los requisitos específicos de la técnica
  - Adapta la consistencia y volúmenes de la formulación según la técnica
  
  Formato: Markdown profesional con secciones claras.`

  try {
    const response = await retryWithBackoff(async () => {
      return await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${openaiApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-4o',
          messages: [
            { 
              role: 'system', 
              content: regionalConfig?.language === 'en' 
                ? `You are an expert colorist with 20 years of experience specializing in ${techniqueName}. Always respond in English and use the terminology and units specified in the prompt. You understand the nuances of different application techniques and adapt formulas accordingly. Consider factors like hair porosity, elasticity, and previous chemical processes when creating formulas.`
                : `Eres un experto colorista con 20 años de experiencia especializado en ${techniqueName}. Siempre responde en español y usa la terminología y unidades especificadas en el prompt. Comprendes los matices de diferentes técnicas de aplicación y adaptas las fórmulas según corresponda. Consideras factores como porosidad, elasticidad y procesos químicos previos al crear fórmulas.`
            },
            { role: 'user', content: prompt }
          ],
          max_tokens: 2000,
          temperature: 0.4
        })
      })
    })

    // Check HTTP status first
    if (!response.ok) {
      console.error('OpenAI API request failed for formula generation:', {
        status: response.status,
        statusText: response.statusText
      })
      
      const errorData = await response.json().catch(() => ({ error: 'Failed to parse error response' }))
      console.error('Error response:', errorData)
      
      if (response.status === 401) {
        throw new Error('OpenAI API key is invalid or missing')
      } else if (response.status === 429) {
        throw new Error('OpenAI API rate limit exceeded')
      } else if (response.status === 400) {
        throw new Error(`OpenAI API bad request: ${errorData.error?.message || 'Invalid request format'}`)
      } else {
        throw new Error(`OpenAI API error: ${response.status} ${errorData.error?.message || response.statusText}`)
      }
    }

    const data = await response.json()
    console.log('OpenAI response received for formula, has choices:', !!data.choices)
    
    if (data.error) {
      throw new Error(data.error.message)
    }

    const formulaText = data.choices[0].message.content
    const inputTokens = data.usage.prompt_tokens
    const outputTokens = data.usage.completion_tokens
    const totalTokens = data.usage.total_tokens
    const costUsd = calculateCost('gpt-4o', inputTokens, outputTokens)
    
    // Extract structured data from the formula
    const formulaData = {
      steps: [],
      products: [],
      processingTimes: {},
      technique: ''
    }
    
    // Save to cache
    const inputHash = generateCacheKey('generate_formula', { diagnosis, desiredResult, brand, line })
    await saveToCache(salonId, 'generate_formula', inputHash, payload, { formulaText, formulaData }, 'gpt-4o', totalTokens, costUsd)
    
    return { 
      success: true, 
      data: { 
        formulaText, 
        formulaData,
        totalTokens
      } 
    }
  } catch (error: any) {
    console.error('Error in generateFormula:', error)
    return { success: false, error: error.message }
  }
}

async function convertFormula(payload: any, salonId: string): Promise<AIResponse> {
  const { originalBrand, originalLine, originalFormula, targetBrand, targetLine, regionalConfig } = payload
  
  // Detectar idioma y configuración regional
  const isEnglish = regionalConfig?.language === 'en'
  const volumeUnit = regionalConfig?.volumeUnit || 'ml'
  const weightUnit = regionalConfig?.weightUnit || 'g'
  const developerTerm = regionalConfig?.developerTerminology || 'oxidante'
  const colorTerm = regionalConfig?.colorTerminology || 'tinte'
  
  const prompt = isEnglish 
    ? `Convert this hair color formula:
  
  Original brand: ${originalBrand} - ${originalLine}
  Formula: ${originalFormula}
  
  To target brand: ${targetBrand} - ${targetLine}
  
  Provide:
  1. Converted formula with equivalent products
  2. Necessary adjustments in ratios or timing
  3. Warnings about differences between brands
  4. Confidence level in the conversion
  
  Use ${volumeUnit} for volumes, ${weightUnit} for weights, "${developerTerm}" for developer, and "${colorTerm}" for color.`
    : `Convierte esta fórmula de coloración:
  
  Marca original: ${originalBrand} - ${originalLine}
  Fórmula: ${originalFormula}
  
  A la marca objetivo: ${targetBrand} - ${targetLine}
  
  Proporciona:
  1. Fórmula convertida con productos equivalentes
  2. Ajustes necesarios en proporciones o tiempos
  3. Advertencias sobre diferencias entre marcas
  4. Nivel de confianza en la conversión
  
  Usa ${volumeUnit} para volúmenes, ${weightUnit} para pesos, "${developerTerm}" para oxidante, y "${colorTerm}" para coloración.`

  try {
    const response = await retryWithBackoff(async () => {
      return await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${openaiApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-4o-mini',
          messages: [
            { 
              role: 'system', 
              content: isEnglish 
                ? 'You are an expert colorist specialized in converting formulas between different hair color brands. Always respond in English.'
                : 'Eres un experto colorista especializado en convertir fórmulas entre diferentes marcas de coloración. Siempre responde en español.'
            },
            { role: 'user', content: prompt }
          ],
          max_tokens: 1000,
          temperature: 0.3
        })
      })
    })

    // Check HTTP status first
    if (!response.ok) {
      console.error('OpenAI API request failed for formula conversion:', {
        status: response.status,
        statusText: response.statusText
      })
      
      const errorData = await response.json().catch(() => ({ error: 'Failed to parse error response' }))
      console.error('Error response:', errorData)
      
      if (response.status === 401) {
        throw new Error('OpenAI API key is invalid or missing')
      } else if (response.status === 429) {
        throw new Error('OpenAI API rate limit exceeded')
      } else if (response.status === 400) {
        throw new Error(`OpenAI API bad request: ${errorData.error?.message || 'Invalid request format'}`)
      } else {
        throw new Error(`OpenAI API error: ${response.status} ${errorData.error?.message || response.statusText}`)
      }
    }

    const data = await response.json()
    
    if (data.error) {
      throw new Error(data.error.message)
    }

    const result = data.choices[0].message.content
    const inputTokens = data.usage.prompt_tokens
    const outputTokens = data.usage.completion_tokens
    const totalTokens = data.usage.total_tokens
    const costUsd = calculateCost('gpt-4o-mini', inputTokens, outputTokens)

    // Save to cache
    const inputHash = generateCacheKey('convert_formula', payload)
    await saveToCache(salonId, 'convert_formula', inputHash, payload, result, 'gpt-4o-mini', totalTokens, costUsd)

    return { success: true, data: result }
  } catch (error: any) {
    console.error('Error in convertFormula:', error)
    return { success: false, error: error.message }
  }
}

async function parseProductText(payload: any, salonId: string): Promise<AIResponse> {
  const { text } = payload
  
  const prompt = `Analiza este texto sobre un producto de peluquería y extrae la información estructurada:
  
  Texto: "${text}"
  
  Devuelve un JSON con:
  {
    "brand": "marca detectada",
    "name": "nombre del producto",
    "line": "línea si se menciona",
    "type": "color|developer|treatment|shampoo|conditioner|styling|other",
    "size": { "value": número, "unit": "ml|g|oz" },
    "quantity": número de unidades,
    "details": { cualquier detalle adicional }
  }`

  try {
    const response = await retryWithBackoff(async () => {
      return await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${openaiApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-3.5-turbo',
          messages: [
            { role: 'user', content: prompt }
          ],
          max_tokens: 300,
          temperature: 0.2,
          response_format: { type: "json_object" }
        })
      })
    })

    // Check HTTP status first
    if (!response.ok) {
      console.error('OpenAI API request failed for product text parsing:', {
        status: response.status,
        statusText: response.statusText
      })
      
      const errorData = await response.json().catch(() => ({ error: 'Failed to parse error response' }))
      console.error('Error response:', errorData)
      
      if (response.status === 401) {
        throw new Error('OpenAI API key is invalid or missing')
      } else if (response.status === 429) {
        throw new Error('OpenAI API rate limit exceeded')
      } else if (response.status === 400) {
        throw new Error(`OpenAI API bad request: ${errorData.error?.message || 'Invalid request format'}`)
      } else {
        throw new Error(`OpenAI API error: ${response.status} ${errorData.error?.message || response.statusText}`)
      }
    }

    const data = await response.json()
    
    if (data.error) {
      throw new Error(data.error.message)
    }

    const result = JSON.parse(data.choices[0].message.content)
    const inputTokens = data.usage.prompt_tokens
    const outputTokens = data.usage.completion_tokens
    const totalTokens = data.usage.total_tokens
    const costUsd = calculateCost('gpt-3.5-turbo', inputTokens, outputTokens)

    // Save to cache
    const inputHash = generateCacheKey('parse_product_text', { text })
    await saveToCache(salonId, 'parse_product_text', inputHash, payload, result, 'gpt-3.5-turbo', totalTokens, costUsd)
    
    return { success: true, data: result }
  } catch (error: any) {
    console.error('Error in parseProductText:', error)
    return { success: false, error: error.message }
  }
}

async function analyzeProduct(payload: any): Promise<AIResponse> {
  const { productName, language = 'es', brandContext, lineContext } = payload

  // 🧠 CONTEXTUAL VALIDATION: Relax validation when brand/line context is provided
  const hasContext = brandContext || lineContext
  const minLength = hasContext ? 1 : 2
  const trimmedProductName = productName?.trim() || ''
  
  if (!productName || typeof productName !== 'string' || trimmedProductName.length < minLength) {
    console.error('Invalid product name:', {
      productName,
      trimmedLength: trimmedProductName.length,
      minLength,
      hasContext,
      brandContext: brandContext?.name,
      lineContext: lineContext?.name
    })
    
    const errorMessage = hasContext 
      ? (language === 'es' ? 'Nombre de producto requerido (mínimo 1 carácter)' : 'Product name required (minimum 1 character)')
      : (language === 'es' ? 'Nombre de producto requerido (mínimo 2 caracteres)' : 'Product name required (minimum 2 characters)')
      
    return {
      success: false,
      error: errorMessage
    }
  }
  
  console.log('✅ Validation passed:', {
    productName,
    trimmedLength: trimmedProductName.length,
    minLength,
    hasContext: hasContext ? 'Yes' : 'No'
  })

  // Check if OpenAI API key is available
  if (!openaiApiKey) {
    console.error('OpenAI API key not configured')
    return {
      success: false,
      error: 'OpenAI API key not configured'
    }
  }

  try {
    // Prepare context-aware prompt
    const isSpanish = language === 'es'
    
    // Build context information
    let contextInfo = ''
    if (brandContext) {
      contextInfo += `\n\nCONTEXTO DE MARCA SELECCIONADA:\n`
      contextInfo += `- Marca: ${brandContext.name} (${brandContext.country || 'País no especificado'})\n`
      if (brandContext.lines && brandContext.lines.length > 0) {
        contextInfo += `- Líneas disponibles: ${brandContext.lines.join(', ')}\n`
      }
    }
    
    if (lineContext) {
      contextInfo += `\n\nCONTEXTO DE LÍNEA SELECCIONADA:\n`
      contextInfo += `- Línea: ${lineContext.name}\n`
      if (lineContext.description) {
        contextInfo += `- Descripción: ${lineContext.description}\n`
      }
    }
    
    const prompt = isSpanish ? 
      `Eres un experto mundial en productos profesionales de peluquería. Analiza este producto y extrae información estructurada.

PRODUCTO: "${productName}"${contextInfo}

🚨 REGLAS OBLIGATORIAS DE CONTEXTO (PRIORITY 1):
${brandContext ? `✅ MARCA OBLIGATORIA: Debes usar EXACTAMENTE "${brandContext.name}" como brand
✅ PAÍS CONFIRMADO: ${brandContext.country}` : '❌ Sin contexto de marca - usar conocimiento general'}
${lineContext ? `✅ LÍNEA OBLIGATORIA: Debes usar EXACTAMENTE "${lineContext.name}" como line
✅ DESCRIPCIÓN: ${lineContext.description || 'Sin descripción'}` : '❌ Sin contexto de línea - inferir de la marca'}

🔒 CONSTRUCCIÓN OBLIGATORIA DE PRODUCTO:
${lineContext ? `✅ ProductName DEBE ser: "${lineContext.name} ${productName.replace(/[^0-9.,\/]/g, '')}"` : `✅ ProductName debe incluir marca y código/tono`}

⚠️ VALIDACIÓN CRÍTICA:
- Si hay brandContext → field "brand" DEBE ser "${brandContext?.name || 'NO_BRAND'}"
- Si hay lineContext → field "line" DEBE ser "${lineContext?.name || 'NO_LINE'}"
- NUNCA uses otras marcas si se proporciona contexto específico
- NUNCA uses otras líneas si se proporciona contexto específico

CONOCIMIENTO GLOBAL DE MARCAS (solo para inferencia sin contexto):
🇪🇸 ESPAÑOLAS: Salerm, Kemon, Tahe, Montibello, Hipertin, Válquer
🇺🇸 AMERICANAS: Matrix, Redken, Paul Mitchell, Joico, Schwarzkopf, Clairol, Revlon
🇫🇷 FRANCESAS: L'Oréal, Kérastase, Garnier, Dessange
🇩🇪 ALEMANAS: Wella, Schwarzkopf, Goldwell, Indola
🇮🇹 ITALIANAS: Alfaparf, Davines, Echosline, Selective
🇯🇵 JAPONESAS: Milbon, Shiseido, Lebel
🌍 INTERNACIONALES: Aveda, Tigi, Olaplex, Moroccanoil

PATRONES DE CÓDIGOS PROFESIONALES:
- SV001, SV002, SV005... = Salerm línea Salermvision (tintes profesionales)
- CV001, CV002... = Líneas de color profesionales
- 8/0, 9.1, 7/3 = Tonos de tinte (numeración internacional)
- 20vol, 30vol, 40vol = Oxidantes (volúmenes)
- Códigos numéricos = Referencias profesionales específicas

CONOCIMIENTO ESPECÍFICO DE LÍNEAS:
- Salerm SV### = Línea "Salermvision", productName debería ser "Salermvision SV###"
- Matrix SoColor = Línea "SoColor", tintes profesionales
- Wella Koleston = Línea "Koleston Perfect", tintes permanentes
- L'Oréal Majirel = Línea "Majirel", tintes profesionales
- Davines Alchemic = Línea "Alchemic", tratamientos de color

INFERENCIA INTELIGENTE:
1. Si hay CONTEXTO DE MARCA → usa esa marca específica como brand
2. Si hay CONTEXTO DE LÍNEA → usa esa línea específica como line
3. Si reconoces la MARCA → infiere tipo de producto más probable
4. Si hay CÓDIGO (SV001, CV002) → probablemente tinte/color
5. Si hay VOLUMEN (20vol, 30vol) → oxidante
6. Si hay TONO (8/0, 9.1) → tinte
7. Si es marca profesional conocida → usa contexto de la marca

PRIORIDAD DE CONTEXTO:
- Si hay brandContext → SIEMPRE usa brandContext.name como "brand"
- Si hay lineContext → SIEMPRE usa lineContext.name como "line"
- El productName debe construirse considerando la línea y código/referencia

EJEMPLOS SOLO SI NO HAY CONTEXTO ESPECÍFICO:
- "SV005" (sin contexto) → brand="Salerm", line="Salermvision", productName="Salermvision SV005"
- "8/0" (sin contexto) → inferir marca más probable para el tono
- "Oxidante 20 vol" → productType="Oxidante", productName="Oxidante 20 vol", tone="20 vol"

EJEMPLOS CON CONTEXTO (PRIORIDAD MÁXIMA):
- "9.1" + brandContext="Wella" + lineContext="Illumina Color" → brand="Wella Professionals", line="Illumina Color", productName="Illumina Color 9.1", tone="9.1"
- "SV005" + brandContext="Salerm" → brand="Salerm Cosmetics", line="Salermvision", productName="Salermvision SV005"

REGLAS ESPECÍFICAS PARA COMPLETAR CAMPOS:
1. SIEMPRE completa "productName" con línea + código/tono
2. Para códigos SV### → line="Salermvision", productName="Salermvision SV###"
3. Para códigos CV### → line="Color", productName="Color CV###"
4. Para tonos 8/0, 9.1 → incluye en productName y tone
5. NUNCA dejes productName vacío si puedes construirlo

CATEGORÍAS: "tinte", "oxidante", "decolorante", "tratamiento", "otro"
UNIDADES: "ml", "g", "unidad"

VALIDACIÓN FLEXIBLE:
- Para marcas profesionales conocidas: SIEMPRE válido aunque falten campos
- Para códigos profesionales: SIEMPRE válido si identificas marca/tipo
- NUNCA devuelvas todos los campos como null para marcas conocidas

Responde SOLO con JSON válido:
{
  "brand": "string o null",
  "productType": "string o null", 
  "line": "string o null",
  "productName": "string o null",
  "tone": "string o null",
  "category": "tinte|oxidante|decolorante|tratamiento|otro o null",
  "packageSize": "number o null",
  "unit": "ml|g|unidad o null",
  "barcode": "string o null",
  "additionalInfo": "string o null"
}` :
      `You are a global expert in professional hairdressing products. Analyze this product and extract structured information.

PRODUCT: "${productName}"${contextInfo}

🚨 MANDATORY CONTEXT RULES (PRIORITY 1):
${brandContext ? `✅ MANDATORY BRAND: You MUST use EXACTLY "${brandContext.name}" as brand
✅ CONFIRMED COUNTRY: ${brandContext.country}` : '❌ No brand context - use general knowledge'}
${lineContext ? `✅ MANDATORY LINE: You MUST use EXACTLY "${lineContext.name}" as line
✅ DESCRIPTION: ${lineContext.description || 'No description'}` : '❌ No line context - infer from brand'}

🔒 MANDATORY PRODUCT CONSTRUCTION:
${lineContext ? `✅ ProductName MUST be: "${lineContext.name} ${productName.replace(/[^0-9.,\/]/g, '')}"` : `✅ ProductName must include brand and code/tone`}

⚠️ CRITICAL VALIDATION:
- If brandContext exists → field "brand" MUST be "${brandContext?.name || 'NO_BRAND'}"
- If lineContext exists → field "line" MUST be "${lineContext?.name || 'NO_LINE'}"
- NEVER use other brands if specific context is provided
- NEVER use other lines if specific context is provided

GLOBAL BRAND KNOWLEDGE (for inference without context only):
🇪🇸 SPANISH: Salerm, Kemon, Tahe, Montibello, Hipertin, Válquer
🇺🇸 AMERICAN: Matrix, Redken, Paul Mitchell, Joico, Schwarzkopf, Clairol, Revlon
🇫🇷 FRENCH: L'Oréal, Kérastase, Garnier, Dessange
🇩🇪 GERMAN: Wella, Schwarzkopf, Goldwell, Indola
🇮🇹 ITALIAN: Alfaparf, Davines, Echosline, Selective
🇯🇵 JAPANESE: Milbon, Shiseido, Lebel
🌍 INTERNATIONAL: Aveda, Tigi, Olaplex, Moroccanoil

PROFESSIONAL CODE PATTERNS:
- SV001, SV002, SV005... = Salerm Salermvision line (professional hair dyes)
- CV001, CV002... = Professional color lines
- 8/0, 9.1, 7/3 = Hair dye tones (international numbering)
- 20vol, 30vol, 40vol = Developers (volumes)
- Numeric codes = Specific professional references

SPECIFIC LINE KNOWLEDGE:
- Salerm SV### = "Salermvision" line, productName should be "Salermvision SV###"
- Matrix SoColor = "SoColor" line, professional hair dyes
- Wella Koleston = "Koleston Perfect" line, permanent hair dyes
- L'Oréal Majirel = "Majirel" line, professional hair dyes
- Davines Alchemic = "Alchemic" line, color treatments

INTELLIGENT INFERENCE:
1. If there's BRAND CONTEXT → use that specific brand as brand
2. If there's LINE CONTEXT → use that specific line as line
3. If you recognize the BRAND → infer most likely product type
4. If there's a CODE (SV001, CV002) → probably hair dye/color
5. If there's VOLUME (20vol, 30vol) → developer
6. If there's TONE (8/0, 9.1) → hair dye
7. If it's a known professional brand → use brand context

CONTEXT PRIORITY:
- If brandContext exists → ALWAYS use brandContext.name as "brand"
- If lineContext exists → ALWAYS use lineContext.name as "line"
- The productName should be constructed considering the line and code/reference

EXAMPLES ONLY IF NO SPECIFIC CONTEXT:
- "SV005" (no context) → brand="Salerm", line="Salermvision", productName="Salermvision SV005"
- "8/0" (no context) → infer most likely brand for the tone
- "Developer 20 vol" → productType="Developer", productName="Developer 20 vol", tone="20 vol"

EXAMPLES WITH CONTEXT (MAXIMUM PRIORITY):
- "9.1" + brandContext="Wella" + lineContext="Illumina Color" → brand="Wella Professionals", line="Illumina Color", productName="Illumina Color 9.1", tone="9.1"
- "SV005" + brandContext="Salerm" → brand="Salerm Cosmetics", line="Salermvision", productName="Salermvision SV005"

SPECIFIC RULES FOR COMPLETING FIELDS:
1. ALWAYS complete "productName" with line + code/tone
2. For SV### codes → line="Salermvision", productName="Salermvision SV###"
3. For CV### codes → line="Color", productName="Color CV###"
4. For tones 8/0, 9.1 → include in both productName and tone
5. NEVER leave productName empty if you can construct it

CATEGORIES: "tinte", "oxidante", "decolorante", "tratamiento", "otro"
UNITS: "ml", "g", "unidad"

FLEXIBLE VALIDATION:
- For known professional brands: ALWAYS valid even if some fields are missing
- For professional codes: ALWAYS valid if you identify brand/type
- NEVER return all fields as null for known brands

Respond ONLY with valid JSON:
{
  "brand": "string or null",
  "productType": "string or null",
  "line": "string or null", 
  "productName": "string or null",
  "tone": "string or null",
  "category": "tinte|oxidante|decolorante|tratamiento|otro or null",
  "packageSize": "number or null",
  "unit": "ml|g|unidad or null",
  "barcode": "string or null",
  "additionalInfo": "string or null"
}`

    console.log('Calling OpenAI with prompt length:', prompt.length)
    console.log('Product name:', productName)

    // Call OpenAI
    const response = await retryWithBackoff(async () => {
      return await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${openaiApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-4o-mini',
          messages: [
            { role: 'system', content: prompt },
            { role: 'user', content: `Analiza: "${productName}"` }
          ],
          max_tokens: 500,
          temperature: 0.1,
          response_format: { type: 'json_object' }
        })
      })
    })

    console.log('OpenAI response status:', response.status)
    console.log('OpenAI response ok:', response.ok)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('OpenAI error response:', errorText)
      throw new Error(`OpenAI API error: ${response.status} - ${errorText}`)
    }

    const data = await response.json()
    console.log('OpenAI response data:', JSON.stringify(data, null, 2))

    if (!data.choices || !data.choices[0]) {
      console.error('Invalid OpenAI response structure')
      throw new Error('Invalid OpenAI response structure')
    }

    const aiContentRaw = data.choices[0].message.content
    console.log('AI content raw:', aiContentRaw)

    let aiResponse
    try {
      aiResponse = JSON.parse(aiContentRaw)
      console.log('AI response parsed:', JSON.stringify(aiResponse, null, 2))
    } catch (parseError: any) {
      console.error('JSON parse error:', parseError.message)
      console.error('Content that failed to parse:', aiContentRaw)
      throw new Error(`Failed to parse AI response as JSON: ${parseError.message}`)
    }

    // 🚨 PHASE 2: CONTEXT VALIDATION - Ensure AI respects provided context
    let contextViolations = []
    let forceCorrections = false

    if (brandContext && aiResponse.brand !== brandContext.name) {
      console.warn(`🚨 CONTEXT VIOLATION: AI returned brand "${aiResponse.brand}" but context requires "${brandContext.name}"`)
      contextViolations.push(`Brand mismatch: AI="${aiResponse.brand}" vs Context="${brandContext.name}"`)
      aiResponse.brand = brandContext.name // Force correction
      forceCorrections = true
    }

    if (lineContext && aiResponse.line !== lineContext.name) {
      console.warn(`🚨 CONTEXT VIOLATION: AI returned line "${aiResponse.line}" but context requires "${lineContext.name}"`)
      contextViolations.push(`Line mismatch: AI="${aiResponse.line}" vs Context="${lineContext.name}"`)
      aiResponse.line = lineContext.name // Force correction
      forceCorrections = true
    }

    // 🔒 PHASE 3: FORCE PRODUCT NAME CONSTRUCTION with context
    if (lineContext) {
      const extractedTone = productName.replace(/[^0-9.,\/]/g, '').trim()
      const expectedProductName = `${lineContext.name} ${extractedTone}`
      if (aiResponse.productName !== expectedProductName) {
        console.warn(`🔧 FORCING ProductName: AI="${aiResponse.productName}" → Context="${expectedProductName}"`)
        aiResponse.productName = expectedProductName
        forceCorrections = true
      }
    }

    if (forceCorrections) {
      console.log('🔧 Context corrections applied:', {
        violations: contextViolations,
        correctedResponse: aiResponse
      })
    } else {
      console.log('✅ AI response respects all provided context')
    }

    // Professional brands validation - more flexible for known brands
    const professionalBrands = [
      'salerm', 'kemon', 'tahe', 'montibello', 'hipertin', 'válquer',
      'matrix', 'redken', 'paul mitchell', 'joico', 'schwarzkopf', 'clairol', 'revlon',
      'l\'oréal', 'kérastase', 'garnier', 'dessange',
      'wella', 'goldwell', 'indola',
      'alfaparf', 'davines', 'echosline', 'selective',
      'milbon', 'shiseido', 'lebel',
      'aveda', 'tigi', 'olaplex', 'moroccanoil'
    ]
    
    const brandLower = aiResponse.brand?.toLowerCase() || ''
    const isProfessionalBrand = professionalBrands.some(brand => 
      brandLower.includes(brand) || brand.includes(brandLower)
    )
    
    // Check for professional code patterns
    const hasProCode = /^(SV|CV|SC|HC|TC)\d{3}$/i.test(productName) || 
                      /^\d{1,2}\/\d{1,2}$/.test(productName) || 
                      /^\d{1,2}\.?\d{1,2}?[A-Z]?$/.test(productName)
    
    // Flexible validation logic
    let hasValidData = false
    let validationReason = ''
    
    if (isProfessionalBrand) {
      // For professional brands: valid if we have brand + any other field
      hasValidData = aiResponse.brand && (aiResponse.productType || aiResponse.category || aiResponse.line)
      validationReason = 'Professional brand identified'
    } else if (hasProCode) {
      // For professional codes: valid if we have brand or productType
      hasValidData = aiResponse.brand || aiResponse.productType
      validationReason = 'Professional code pattern detected'
    } else {
      // Standard validation: need brand + productType + category
      hasValidData = aiResponse.brand && aiResponse.productType && aiResponse.category
      validationReason = 'Standard validation'
    }
    
    console.log('Enhanced validation:', {
      hasValidData,
      validationReason,
      isProfessionalBrand,
      hasProCode,
      brand: aiResponse.brand,
      productType: aiResponse.productType,
      category: aiResponse.category
    })

    if (!hasValidData) {
      console.log('AI could not identify product, returning user-friendly error')
      return {
        success: false,
        error: isSpanish ? 
          'No se pudo identificar el producto. Asegúrate de usar el nombre completo del producto (ej: "Wella Koleston 8/0")' :
          'Could not identify the product. Please use the full product name (e.g., "Wella Koleston 8/0")'
      }
    }

    console.log('Returning successful response:', aiResponse)

    return {
      success: true,
      data: aiResponse,
      cached: false
    }

  } catch (error: any) {
    console.error('Error in analyze_product:', error)
    return { success: false, error: error.message || 'Failed to analyze product' }
  }
}

serve(async (req) => {
  console.log('Edge function invoked:', {
    method: req.method,
    url: req.url,
    hasOpenAIKey: !!openaiApiKey
  })
  
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Verify JWT
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get user and salon info
    const { data: { user }, error: authError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    )
    
    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid token' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get user's salon_id
    const { data: profile } = await supabase
      .from('profiles')
      .select('salon_id')
      .eq('id', user.id)
      .single()

    if (!profile?.salon_id) {
      return new Response(
        JSON.stringify({ error: 'User not associated with a salon' }),
        { status: 403, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const salonId = profile.salon_id

    // Parse request
    const { task, payload }: AIRequest = await req.json()

    // Validate OpenAI API key
    if (!openaiApiKey) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'OpenAI API key not configured. Please contact support.' 
        }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Check cache first
    const cacheKey = generateCacheKey(task, payload)
    const cachedResult = await checkCache(salonId, task, cacheKey)
    
    if (cachedResult) {
      return new Response(
        JSON.stringify({ success: true, data: cachedResult, cached: true }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Process based on task
    let result: AIResponse
    
    console.log(`Processing task: ${task}`)
    
    switch (task) {
      case 'diagnose_image':
        result = await diagnoseImage(payload, salonId)
        break
      case 'analyze_desired_look':
        result = await analyzeDesiredLook(payload, salonId)
        break
      case 'generate_formula':
        result = await generateFormula(payload, salonId)
        break
      case 'convert_formula':
        result = await convertFormula(payload, salonId)
        break
      case 'parse_product_text':
        result = await parseProductText(payload, salonId)
        break
      case 'analyze_product':
        result = await analyzeProduct(payload)
        break
      default:
        result = { success: false, error: 'Invalid task' }
    }

    // Log the result before sending
    console.log('Task result:', {
      task,
      success: result.success,
      hasData: !!result.data,
      error: result.error,
      dataKeys: result.data ? Object.keys(result.data) : []
    })

    // Ensure we never return success with null data
    if (result.success && !result.data) {
      console.error('Warning: Success with no data, converting to error')
      result = { success: false, error: 'No data generated' }
    }

    return new Response(
      JSON.stringify(result),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
    
  } catch (error: any) {
    console.error('Edge function error:', error)
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})